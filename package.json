{"name": "@upshot-tech/api-responser", "version": "0.0.508", "description": "Upshot rest API request-response typings", "author": "", "license": "UNLICENSED", "engines": {"node": "^16.0.0"}, "scripts": {"clean": "rimraf dist && rimraf tsconfig.build.tsbuildinfo", "prebuild": "npm run clean", "build": "nest build rest-api; nest build kafka-consumers; nest build cron; nest build api-portal", "build:validate-ts": "tsc -p ./ --noEmit --skipL<PERSON><PERSON><PERSON><PERSON>", "build:swagger:prod": "NODE_ENV=mock npx nest start swagger-builder -- generate:rest-api-docs -o swagger.json", "build:swagger:stag": "NODE_ENV=stage-mock npx nest start swagger-builder -- generate:rest-api-docs -o stage.swagger.json", "build:swagger:types": "npx swagger-typescript-api -p stage.swagger.json -n stage.swagger.d.ts", "build:swagger:validate": "npx rdme openapi:validate ./stage.swagger.json && npx rdme openapi:validate ./swagger.json", "build:swagger": "npm run build:swagger:prod && npm run build:swagger:stag && npm run build:swagger:validate", "format": "prettier --write \"apps/**/*.ts\" \"libs/**/*.ts\"", "start": "concurrently \"nest start rest-api\" \"nest start kafka-consumers\" \"nest start api-portal\"", "start:kafka-consumers": "nest start kafka-consumers", "start:rest-api": "nest start rest-api", "start:api-portal": "nest start api-portal", "start:dev": "concurrently \"nest start --watch rest-api\" \"nest start --watch kafka-consumers\" \"nest start --watch email-preview\" \"nest start --watch api-portal\"", "start:dev:kafka-consumers": "nest start --watch kafka-consumers", "start:dev:rest-api": "nest start --watch rest-api", "start:dev:cron": "nest start --watch cron", "start:dev:api-portal": "nest start --watch api-portal", "start:debug": "concurrently \"nest start --watch --debug rest-api\" \"nest start --watch --debug kafka-consumers\" \"nest start --watch --debug email-preview\"", "start:debug:rest-api": "nest start --watch --debug rest-api", "start:debug:kafka-consumers": "nest start --watch --debug kafka-consumers", "start:": "nest start --debug --watch", "start:containers": "docker compose up -d --wait", "stop:containers": "docker compose --profile test-e2e stop", "start:prod": "node dist/apps/rest-api/main", "start:prod:kafka-consumers": "node dist/apps/kafka-consumers/main", "start:prod:cron": "node dist/apps/cron/main", "start:prod:api-portal": "node dist/apps/api-portal/main", "lint:eslint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "lint:prettier": "npx prettier --config .prettierrc '{apps,libs}/**/*.ts' --write", "lint": "npm run lint:eslint && npm run lint:prettier", "test": "NODE_ENV=test jest --filter=./test-config/unit-filter.js", "test:watch": "NODE_ENV=test jest --watch --filter=./test-config/unit-filter.js", "test:watch:verbose": "NODE_ENV=test jest --watch --filter=./test-config/unit-filter.js --verbose true", "test:cov": "NODE_ENV=test jest --coverage --filter=./test-config/unit-filter.js", "test:debug": "NODE_ENV=test node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand --filter=./test-config/unit-filter.js", "test:e2e:full": "NODE_ENV=test npm run test:e2e:start-containers && npm run test:e2e", "test:e2e:start-containers": "docker compose --profile test-e2e up -d --wait", "test:e2e": "NODE_ENV=test jest --filter=./test-config/e2e-filter.js --testTimeout=20000 --runInBand", "test:integration": "NODE_ENV=test jest --filter=./test-config/integration-filter.js --runInBand", "db:migrate": "chmod +x database/migrate-and-notify.sh && ./database/migrate-and-notify.sh", "db:migrate:down": "npx sequelize-cli db:migrate:undo", "db:migration:create": "npx sequelize-cli migration:generate --name", "db:seed:create": "npx sequelize-cli seed:generate --name", "db:seed:run": "npx sequelize-cli db:seed:all", "redis:seed": "ts-node redis/seeder.ts", "postversion": "git push && git push --tags", "install-deps": "npm i && git commit -am 'update package-lock.json' || echo 'no package-lock.json changes'", "generate:pgtypes": "node sql-ts.js", "db:sync-env": "./database/sync-env.sh", "sync:env-var": "ts-node ./infra/sync-env-vars.ts", "sync:env-var:help": "ts-node ./infra/sync-env-vars.ts -h", "create:task-def:help": "ts-node infra/create-task-def.ts -h", "create:task-def": "ts-node infra/create-task-def.ts", "add:path:to:waf": "ts-node infra/add-path-to-waf.ts"}, "dependencies": {"@nestjs/common": "^8.4.7", "@nestjs/core": "^8.4.7", "@nestjs/cqrs": "^8.0.5", "@nestjs/jwt": "^10.0.1", "@nestjs/microservices": "^8.4.7", "@nestjs/passport": "^8.2.2", "@nestjs/platform-express": "^8.4.7", "@nestjs/schedule": "^2.1.0", "@nestjs/sequelize": "^8.0.0", "@nestjs/swagger": "^5.2.1", "@rmp135/sql-ts": "^1.16.0", "@slack/web-api": "^7.0.2", "@types/object-hash": "^3.0.6", "@types/stripe": "^8.0.417", "@visx/mock-data": "^3.3.0", "alchemy-sdk": "^2.9.2", "aws-sdk": "^2.1692.0", "base64-js": "^1.5.1", "bcrypt": "^5.1.0", "bignumber.js": "^9.1.2", "cheerio": "^1.0.0-rc.12", "class-transformer": "^0.5.1", "class-validator": "^0.14.0", "compression": "^1.7.4", "cookie-parser": "^1.4.6", "crypto": "^1.0.1", "csv-parse": "^5.6.0", "csv-stringify": "^6.5.2", "dd-trace": "^3.7.1", "dotenv": "^16.0.0", "env-var": "^7.1.1", "ethers": "^5.6.6", "exponential-backoff": "^3.1.1", "express-basic-auth": "^1.2.1", "helmet": "^4.6.0", "hot-shots": "^9.3.0", "ioredis": "^5.0.5", "kafkajs": "^1.16.0", "kafkajs-snappy": "^1.1.0", "loadash": "^1.0.0", "mjml": "^4.13.0", "morgan": "^1.10.0", "nest-commander": "^3.4.0", "nestjs-cls": "^3.0.4", "nestjs-pino": "^3.1.2", "object-hash": "^3.0.0", "p-limit": "^2.3.0", "p-map": "^4.0.0", "passport": "^0.6.0", "passport-jwt": "^4.0.1", "pg": "^8.7.3", "pg-connection-string": "^2.5.0", "pg-hstore": "^2.3.4", "pino-http": "^8.3.1", "pino-pretty": "^9.1.1", "prom-client": "^15.1.3", "pusher": "^5.1.2", "react": "^18.2.0", "react-dom": "^18.2.0", "redis": "^3.1.1", "reflect-metadata": "^0.1.13", "rimraf": "^3.0.2", "rxjs": "^7.5.5", "sequelize": "^6.29.0", "sequelize-cli": "^6.4.1", "sequelize-typescript": "^2.1.3", "stream-transform": "^3.3.3", "stripe": "^11.11.0", "swagger-typescript-api": "^12.0.3", "swagger-ui-express": "^4.4.0"}, "devDependencies": {"@faker-js/faker": "^7.6.0", "@nestjs/cli": "^8.2.6", "@nestjs/schematics": "^8.0.11", "@nestjs/testing": "^8.4.7", "@reservoir0x/reservoir-kit-client": "^0.2.5", "@types/aws-sdk": "^2.7.0", "@types/cheerio": "^0.22.32", "@types/compression": "^1.7.2", "@types/cookie-parser": "^1.4.3", "@types/cron": "^2.0.0", "@types/express": "^4.17.11", "@types/helmet": "^4.0.0", "@types/jest": "^26.0.20", "@types/js-yaml": "^4.0.5", "@types/morgan": "^1.9.3", "@types/node": "^14.14.31", "@types/passport-jwt": "^3.0.6", "@types/react": "^18.0.15", "@types/react-dom": "^18.0.6", "@types/redis": "^2.8.32", "@types/sequelize": "^4.28.12", "@types/supertest": "^2.0.10", "@types/uuid": "^8.3.4", "@typescript-eslint/eslint-plugin": "^4.15.2", "@typescript-eslint/parser": "^4.15.2", "@typescript-eslint/utils": "^5.27.0", "axios": "^0.27.2", "concurrently": "^7.2.1", "eslint": "^7.20.0", "eslint-config-prettier": "^8.1.0", "eslint-plugin-local-rules": "^1.1.0", "eslint-plugin-no-only-tests": "^2.6.0", "eslint-plugin-prettier": "^3.3.1", "eslint-plugin-simple-import-sort": "^7.0.0", "husky": "^4.3.8", "ioredis-mock": "^8.2.2", "jest": "^26.6.3", "js-yaml": "^4.1.0", "mockdate": "^3.0.5", "nock": "^13.5.3", "openapi-typescript": "^5.4.1", "prettier": "^2.2.1", "pusher-js": "^8.0.0", "rdme": "^8.5.0", "ts-command-line-args": "^2.4.2", "ts-jest": "^26.5.2", "ts-loader": "^8.0.17", "ts-node": "^9.1.1", "tsconfig-paths": "^3.9.0", "typescript": "^4.1.5", "webpack": "^5.73.0"}, "jest": {"moduleFileExtensions": ["js", "json", "ts", "tsx"], "silent": false, "verbose": false, "forceExit": true, "rootDir": ".", "testRegex": "spec\\.ts$", "transform": {"^.+\\.(t|j)sx?$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "./coverage", "testEnvironment": "node", "roots": ["<rootDir>/apps/", "<rootDir>/libs/", "<rootDir>/test-e2e/"], "moduleNameMapper": {"^@app/app-logger(|/.*)$": "<rootDir>/libs/app-logger/src/$1", "^@app/ses-mailer(|/.*)$": "<rootDir>/libs/ses-mailer/src/$1", "^@app/email-templates(|/.*)$": "<rootDir>/libs/email-templates/src/$1", "^@app/app-metrics(|/.*)$": "<rootDir>/libs/app-metrics/src/$1", "^@app/datadog-client(|/.*)$": "<rootDir>/libs/datadog-client/src/$1", "^@app/core(|/.*)$": "<rootDir>/libs/core/src/$1"}}, "husky": {"hooks": {"pre-push": "npm run build:validate-ts && npm run install-deps && npm run lint"}}, "files": ["stage.swagger.d.ts"], "types": "stage.swagger.d.ts"}