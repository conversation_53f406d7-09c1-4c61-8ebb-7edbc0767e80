import {
  ALLORA_API_CLIENT,
  AlloraApiClient,
} from '@app/core/infra/allora/allora-api-client';
import {
  PROMETHEUS_METRICS_LOGGER,
  PrometheusMetricsLogger,
} from '@app/core/infra/prometheus/prometheus-metrics.logger';
import { Controller, Get, Inject, Logger, Res } from '@nestjs/common';
import { ApiExcludeEndpoint } from '@nestjs/swagger';

import { Public } from './common/decorator';
import { UnwrappedResponse } from './common/decorator/unwrapped-response.decorator';

@Controller()
export class AppController {
  private readonly logger = new Logger(AppController.name);

  constructor(
    @Inject(PROMETHEUS_METRICS_LOGGER)
    private readonly metricsLogger: PrometheusMetricsLogger,
    @Inject(ALLORA_API_CLIENT)
    private readonly alloraApiClient: AlloraApiClient,
  ) {}

  @Get()
  @ApiExcludeEndpoint()
  root() {
    return {
      status: true,
      message: 'Welcome to the Upshot v2 API  Service',
      // TODO: probably don't need this null data
      data: null,
    };
  }

  // this will be called by the AWS EC2 load balance every 5 mins
  @Public()
  @Get('health')
  @ApiExcludeEndpoint()
  healthCheck() {
    return {
      status: true,
      message: 'Upshot v2 API is healthy',
      // TODO: probably don't need this null data
      data: null,
    };
  }

  @Public()
  @Get('metrics')
  @ApiExcludeEndpoint()
  @UnwrappedResponse()
  getMetrics(@Res({ passthrough: true }) response: unknown) {
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-expect-error
    response.header('Content-Type', this.metricsLogger.registry.contentType);
    return this.metricsLogger.registry.metrics();
  }

  @Public()
  @Get('healthz')
  @ApiExcludeEndpoint()
  async healthzCheck() {
    try {
      await this.alloraApiClient.getStatus();
    } catch (error) {
      this.logger.error(error);
    }

    return {
      status: true,
      message: 'Upshot v2 API is healthy',
    };
  }
}
