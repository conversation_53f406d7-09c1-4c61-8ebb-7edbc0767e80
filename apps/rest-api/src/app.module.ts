import { AppLoggerModule } from '@app/app-logger/logger.module';
import { HandlerDurationInterceptor } from '@app/app-metrics';
import { UnitOfWorkModule } from '@app/core/app/unit-of-work/unit-of-work.module';
import { ENodeEnv, NODE_ENV } from '@app/core/config/env';
import { DomainEventAggregatorModule } from '@app/core/domain/domain-event/domain-event.aggregator.module';
import { MetricsLoggerModule } from '@app/core/domain/metrics-logger/metrics-logger.module';
import { AlloraApiClientModule } from '@app/core/infra/allora/allora-api-client.module';
import {
  DOMAIN_EVENT_INTERCEPTOR,
  DomainEventInterceptorModule,
} from '@app/core/infra/interceptor/domain-event.interceptor.module';
import { AsyncContextMiddleware } from '@app/core/infra/middleware/async-context-middleware';
import { PrometheusMetricsModule } from '@app/core/infra/prometheus/prometheus-metrics.module';
import { DatadogClientModule } from '@app/datadog-client';
import { MiddlewareConsumer, Module, RequestMethod } from '@nestjs/common';
import { APP_FILTER, APP_INTERCEPTOR } from '@nestjs/core';
import { SequelizeModule } from '@nestjs/sequelize';
import { ClsModule } from 'nestjs-cls';
import { v4 } from 'uuid';

import { ApiModule } from './api/api.module';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { GlobalExceptionsFilter } from './common';
import {
  HeaderCheckInterceptor,
  ResponseFormatInterceptor,
} from './common/interceptor';
import { mockOptions, sequelizeOptions } from './config/sequelize.config';
import { E2ETestRunnerModule } from './e2e-test-runner/e2e-test-runner.module';
import { RewriteIpMiddleware } from './middleware/rewrite-ip.middleware';

// TODO: change ternary to DB_MOCKED=boolean, move to config.ts
const dbOptions =
  NODE_ENV === ENodeEnv.MOCK || NODE_ENV === ENodeEnv.STAGE_MOCK
    ? mockOptions
    : sequelizeOptions;

@Module({
  imports: [
    SequelizeModule.forRoot(dbOptions),
    // Register the ClsModule and automatically mount the ClsMiddleware
    ClsModule.forRoot({
      global: true,
      middleware: {
        mount: true,
        generateId: true,
        idGenerator: (req: Request) => req.headers['x-request-id'] ?? v4(),
      },
    }),
    AppLoggerModule,
    DatadogClientModule,
    PrometheusMetricsModule.forRoot({
      appName: 'allora-rest-api',
    }),
    AlloraApiClientModule,
    E2ETestRunnerModule,
    DomainEventAggregatorModule,
    DomainEventInterceptorModule,
    UnitOfWorkModule,
    MetricsLoggerModule,
    ApiModule,
  ],
  controllers: [AppController],
  providers: [
    {
      provide: APP_INTERCEPTOR,
      useClass: HeaderCheckInterceptor,
    },
    {
      provide: APP_INTERCEPTOR,
      useClass: ResponseFormatInterceptor,
    },
    {
      provide: APP_INTERCEPTOR,
      useClass: HandlerDurationInterceptor,
    },
    {
      provide: APP_INTERCEPTOR,
      useExisting: DOMAIN_EVENT_INTERCEPTOR,
    },
    {
      provide: APP_FILTER,
      useClass: GlobalExceptionsFilter,
    },
    AppService,
  ],
})
export class AppModule {
  configure(consumer: MiddlewareConsumer) {
    consumer
      .apply(RewriteIpMiddleware)
      .forRoutes({ path: '*', method: RequestMethod.ALL });
    consumer.apply(AsyncContextMiddleware).forRoutes('*');
  }
}
