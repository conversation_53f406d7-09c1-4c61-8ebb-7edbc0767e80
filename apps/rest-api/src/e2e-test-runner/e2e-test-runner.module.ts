import { RedisModule } from '@app/core/infra/redis/redis.module';
import { Module, ModuleMetadata } from '@nestjs/common';
import { ScheduleModule } from '@nestjs/schedule';

import { ACTIVATE_E2E_TESTS } from './config';
import { TestRunnerService } from './test-runner.service';
import { TriggerController } from './trigger.controller';

const populatedModule: ModuleMetadata = {
  imports: [RedisModule, ScheduleModule.forRoot()],
  providers: [TestRunnerService],
  controllers: [TriggerController],
};

const disabledModule: ModuleMetadata = {};

const actualModule = ACTIVATE_E2E_TESTS ? populatedModule : disabledModule;

@Module(actualModule)
export class E2ETestRunnerModule {}
