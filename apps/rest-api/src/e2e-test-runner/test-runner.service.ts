import { REDIS_DATA_SERVICE } from '@app/core/infra/redis/redis.module';
import { RedisService } from '@app/core/infra/redis/redis.service';
import { Inject, Injectable, Logger } from '@nestjs/common';
import axios from 'axios';
import { exec } from 'child_process';

import { e2eTTL, SLACK_BACKEND_E2E_TEST_URL } from './config';

@Injectable()
export class TestRunnerService {
  private readonly logger = new Logger(TestRunnerService.name);
  private readonly testLockKey = 'e2e-test-running';

  // The way RedisService is imported is a hack for simplicity.
  // e2e test is a distinct app that should exist independent of the infra layer.
  constructor(
    @Inject(REDIS_DATA_SERVICE)
    private readonly redisService: RedisService,
  ) {}

  // NOTE: this cron job is being disable temporarily until the e2e tests can be refactored
  // in to a more stable state.
  // @Cron(e2eTestInterval())
  async runTests(force = false): Promise<string> {
    // check if the tests are already running on another cluster
    const result = await this.redisService.setnx(this.testLockKey, 'true');
    // stop if tests are already running on another cluster
    if (!force && !result.status) {
      this.logger.log('Tests are already running on another cluster. Exiting.');
      return 'Tests are already running on another cluster. Exiting.';
    }

    // set the key to expire
    await this.redisService.set(this.testLockKey, 'true', e2eTTL());
    return this.actuallyRunTests();
  }

  /**
   * run the tests
   */
  private actuallyRunTests() {
    // capture stdout and stderr, in case the test runner fails
    let stdoutCatch = '';
    let stderrCatch = '';

    return new Promise<string>(async (resolve, reject) => {
      this.logger.log('Running tests...');
      const childProcess = exec(
        'HIDE_REPLICATION_LOGS=true npm run test:e2e',
        async (error, stdout, stderr) => {
          try {
            if (error) {
              this.logger.error(
                'Tests failed, posting failure message to Slack...',
              );
              await this.postFailureMessage(stdout, stderr, error);
              resolve('Tests failed');
            } else {
              this.logger.log('Posting pass message to Slack...');
              await this.postSuccessMessage(stdout, stderr);
              resolve('Tests passed');
            }

            this.logger.log('Tests finished.');
          } catch (e) {
            reject(e);
          }
        },
      );

      // log out stdout and stderr
      childProcess.stdout!.on('data', (data) => {
        stdoutCatch = stdoutCatch.concat('\n', data);
        this.logger.log(data);
      });
      childProcess.stderr!.on('data', (data) => {
        stderrCatch = stderrCatch.concat(`\n`, data);
        this.logger.log(data);
      });
    })
      .catch(async (error) => {
        this.logger.error('Test runner failed.');
        this.logger.error(error.stackTrace);
        await this.postFailureMessage(stdoutCatch, stderrCatch, error);
        return 'Tests failed';
      })
      .then(async (result) => {
        await this.redisService.del(this.testLockKey);
        return result;
      });
  }

  /**
   * Post a success message to Slack
   * @param stdout - stdout from the test runner
   * @param stderr - stderr from the test runner (includes the test summary)
   */
  private postSuccessMessage(stdout: string, stderr: string) {
    if (!SLACK_BACKEND_E2E_TEST_URL) {
      this.logger.error(
        'SLACK_BACKEND_E2E_TEST_URL is not defined, cannot post to Slack',
      );
      this.logger.debug('✅Tests passed✅');
      return;
    }

    return axios.post(
      SLACK_BACKEND_E2E_TEST_URL,
      {
        blocks: this.formatOutputBlocks(
          '\n\n✅Tests passed✅',
          '',
          '',
          undefined,
        ),
      },
      {
        headers: {
          'Content-type': 'application/json',
        },
      },
    );
  }

  /**
   * Post a failure message to Slack
   * @param stdout - stdout from the test runner
   * @param stderr - stderr from the test runner (includes the test summary)
   * @param error - error object from the test runner
   */
  private postFailureMessage(
    stdout: string | null,
    stderr: string | null,
    error: Error,
  ) {
    if (!SLACK_BACKEND_E2E_TEST_URL) {
      this.logger.error(
        'SLACK_BACKEND_E2E_TEST_URL is not defined, cannot post to Slack',
      );
      this.logger.error(error.message, error.stack);
      return;
    }

    return axios
      .post(
        SLACK_BACKEND_E2E_TEST_URL,
        {
          blocks: this.formatOutputBlocks(
            '⛔️Tests Failed⛔️: <!here> \n',
            stdout || '',
            stderr || '',
            error,
            error?.stack,
          ),
        },
        {
          headers: {
            'Content-type': 'application/json',
          },
        },
      )
      .catch((error) => {
        throw error;
      });
  }

  /**
   * @param message - message to display above the test output
   * @param stdout - stdout from the test runner
   * @param stderr - stderr from the test runner (includes the test summary)
   * @param error - error object from the test runner
   * @returns - an array of Slack blocks
   */
  private formatOutputBlocks(
    message: string,
    stdout: string,
    stderr: string,
    error: Error | undefined,
    stackTrace?: string,
  ) {
    // TODO: Clean this up please
    const errorBlock = error
      ? [
          {
            type: 'section',
            text: {
              type: 'mrkdwn',
              text: '```' + this.transformTestOutput(error.message) + '```',
            },
          },
        ]
      : [];
    const stackTraceBlock = stackTrace
      ? [
          {
            type: 'section',
            text: {
              type: 'mrkdwn',
              text: '```' + this.transformTestOutput(stackTrace) + '```',
            },
          },
        ]
      : [];
    const stdoutBlock =
      stdout.trim().length > 0
        ? [
            {
              type: 'section',
              text: {
                type: 'mrkdwn',
                text: '```' + this.transformTestOutput(stdout) + '```',
              },
            },
          ]
        : [];
    const stderrBlocks =
      stdout.trim().length > 0
        ? this.splitStringIntoBlocks(stderr).map((block) => ({
            type: 'section',
            text: {
              type: 'mrkdwn',
              text: '```' + this.transformTestOutput(block) + '```',
            },
          }))
        : [];

    return [
      {
        type: 'section',
        text: {
          type: 'mrkdwn',
          text: message,
        },
      },
      ...errorBlock,
      ...stackTraceBlock,
      ...stdoutBlock,
      ...stderrBlocks,
    ];
  }

  /**
   * Strip CLI color characters from a string
   * from: https://github.com/jonschlinkert/strip-color/blob/f18fb7802b54be5acc44e069702fcc6265ff785d/index.js
   */
  private stripCliColor(str: string) {
    return str.replace(/\x1B[[(?);]{0,2}(;?\d)*./g, '');
  }

  /**
   * Apply text transformations to the test output
   */
  private transformTestOutput(str: string) {
    return this.stripCliColor(str).trim();
  }

  /**
   * Split string into blocks of max 2000 characters
   */
  private splitStringIntoBlocks(str: string) {
    const blocks: string[] = [];
    let block = '';
    str.split('\n').forEach((line) => {
      if (block.length + line.length > 2000) {
        blocks.push(block);
        block = '';
      }
      block += line + '\n';
    });
    blocks.push(block);
    return blocks;
  }
}
