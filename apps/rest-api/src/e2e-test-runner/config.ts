import { ENodeEnv, NODE_ENV } from '@app/core/config/env';
import { CronExpression } from '@nestjs/schedule';
import { config } from 'dotenv';
import * as env from 'env-var';

config();

export const SLACK_BACKEND_E2E_TEST_URL = env
  .get('SLACK_BACKEND_E2E_TEST_URL')
  .asString();

export const e2eTestInterval = () => {
  if (NODE_ENV === ENodeEnv.DEV || NODE_ENV === ENodeEnv.TEST) {
    return CronExpression.EVERY_MINUTE;
  } else {
    return CronExpression.EVERY_3_HOURS;
  }
};

export const e2eTTL = () => {
  if (NODE_ENV === ENodeEnv.DEV) {
    // dev tests run every minute, so set the TTL to 30 seconds
    return 60 / 2;
  } else {
    // prod tests run three hours, so set the TTL to 90 minutes
    return (3 * 60 * 60) / 2;
  }
};

export const ACTIVATE_E2E_TESTS = env
  .get('ACTIVATE_E2E_TESTS')
  .default('false')
  .asBool();
