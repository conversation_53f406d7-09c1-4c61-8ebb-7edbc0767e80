import { Injectable } from '@nestjs/common';
import {
  registerDecorator,
  ValidationArguments,
  ValidationOptions,
  ValidatorConstraint,
  ValidatorConstraintInterface,
} from 'class-validator';

export function IsAnyUuid(
  validationOptions?: ValidationOptions,
): PropertyDecorator {
  return (object: any, propertyName: string) => {
    registerDecorator({
      target: object.constructor,
      propertyName,
      options: validationOptions,
      validator: IsAnyUuidValidator,
    });
  };
}

@ValidatorConstraint()
@Injectable()
export class IsAnyUuidValidator implements ValidatorConstraintInterface {
  async validate(value: string): Promise<boolean> {
    // https://ihateregex.io/expr/uuid/
    return /^[0-9a-fA-F]{8}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{12}$/.test(
      value,
    );
  }

  public defaultMessage(validationArguments?: ValidationArguments): string {
    if (validationArguments) {
      if (Array.isArray(validationArguments.value)) {
        return `each value in ${validationArguments.property} must be a valid UUID`;
      }
      return `${validationArguments.property} is not a valid UUID`;
    }
    return 'Invalid UUID';
  }
}
