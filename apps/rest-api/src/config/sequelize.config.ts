import { ENodeEnv, NODE_ENV } from '@app/core/config/env';
import { SequelizeModuleOptions } from '@nestjs/sequelize';
import { ConnectionOptions, parse } from 'pg-connection-string';
import Sequelize, { PoolOptions } from 'sequelize';

import * as ENV from './env.config';

// do more works in this config

export const mapConnectionOptions = (
  connection: ConnectionOptions,
): Sequelize.ConnectionOptions => ({
  database: connection.database ?? undefined,
  host: connection.host ?? undefined,
  password: connection.password,
  port: connection.port ?? 5432,
  username: connection.user,
});

export const parseReaderUrls = (
  readerUrls: string | undefined,
): Sequelize.ConnectionOptions[] => {
  if (!readerUrls) {
    return [];
  }

  return readerUrls.split('|').map(parse).map(mapConnectionOptions);
};

export const databaseLogString = (
  connection: Sequelize.ConnectionOptions,
): string => `${connection.host}:${connection.port}/${connection.database}`;

const poolOptions: PoolOptions = {
  acquire: ENV.POSTGRES_POOL_ACQUIRE,
  idle: ENV.POSTGRES_POOL_IDLE,
  max: ENV.POSTGRES_POOL_MAX,
  min: 0,
};

const testSequelizeOptions: SequelizeModuleOptions = {
  dialect: 'postgres',
  host: ENV.TEST_DB_HOST,
  port: ENV.TEST_DB_PORT,
  username: ENV.TEST_DB_USER,
  password: ENV.TEST_DB_PASS,
  database: ENV.TEST_DB_NAME,
  autoLoadModels: true,
  synchronize: true,
  logging: ENV.SEQUELIZE_LOG ? console.log : false,
};

const isSSLEnabled = NODE_ENV === ENodeEnv.PROD || NODE_ENV === ENodeEnv.STAGE;

const defaultSequelizeOptions: SequelizeModuleOptions = {
  dialect: 'postgres',
  host: ENV.DB_HOST,
  port: ENV.DB_PORT,
  username: ENV.DB_USER,
  password: ENV.DB_PASS,
  database: ENV.DB_NAME,
  autoLoadModels: true,
  synchronize: false,
  logging: ENV.SEQUELIZE_LOG ? console.log : false,
  ssl: isSSLEnabled,
  dialectOptions: {
    ssl: isSSLEnabled && {
      require: true,
      rejectUnauthorized: false,
    },
  },
  pool: ENV.POSTGRES_POOL_ENABLED ? poolOptions : undefined,
  models: ['CTopHolder'],
};

let sequelizeOptions: SequelizeModuleOptions;

export const mockOptions: Partial<SequelizeModuleOptions> = {
  dialect: 'sqlite',
};

export const assignReplicationOptions = (
  options: Sequelize.Options,
  readConnections: Sequelize.ConnectionOptions[],
): Sequelize.Options => {
  if (!ENV.POSTGRES_REPLICATION_ENABLED) {
    return options;
  }

  return {
    ...options,
    hooks: {
      beforeQuery: (options, query) => {
        if (ENV.HIDE_REPLICATION_LOGS) {
          return;
        }

        const { host, port, database } = query.connection as ConnectionOptions;
        console.log(`${options.type} - postgres://${host}:${port}/${database}`);
      },
    },
    replication: {
      write: {
        database: options.database,
        host: options.host,
        port: options.port,
        username: options.username,
        password: options.password,
      },
      read: readConnections,
    },
  };
};

if (NODE_ENV === ENodeEnv.TEST) {
  const readConnections = parseReaderUrls(
    ENV.TEST_DATABASE_READER_URLS_PUBLIC_API,
  );
  sequelizeOptions = assignReplicationOptions(
    testSequelizeOptions,
    readConnections,
  );
} else {
  const readConnections = parseReaderUrls(ENV.DATABASE_READER_URLS_PUBLIC_API);
  sequelizeOptions = assignReplicationOptions(
    defaultSequelizeOptions,
    readConnections,
  );
}

if (sequelizeOptions.replication && sequelizeOptions.replication?.read) {
  console.log(
    `Read Connections (${sequelizeOptions.replication?.read.map(
      databaseLogString,
    )})`,
  );
}

export { sequelizeOptions };
