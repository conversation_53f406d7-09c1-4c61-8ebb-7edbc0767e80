import { config } from 'dotenv';
import * as env from 'env-var';

config();

export const USE_KAFKA_IN_API = env
  .get('USE_KAFKA_IN_API')
  .default('true')
  .asBool();
export const PORT = env.get('PORT').default('5000').asInt();

export const POSTGRES_REPLICATION_ENABLED = env
  .get('POSTGRES_REPLICATION_ENABLED')
  .asBool();

export const POSTGRES_POOL_MAX = env.get('POSTGRES_POOL_MAX').asInt();
export const POSTGRES_POOL_ACQUIRE = env.get('POSTGRES_POOL_ACQUIRE').asInt();
export const POSTGRES_POOL_ENABLED = env.get('POSTGRES_POOL_ENABLED').asBool();
export const POSTGRES_POOL_IDLE = env.get('POSTGRES_POOL_IDLE').asInt();

// redis
export const REDIS_URL = env.get('REDIS_URL').required().asString();

// postgres
export const DB_HOST = env.get('DB_HOST').required().asString();
export const DB_PORT = env.get('DB_PORT').required().asInt();
export const DB_USER = env.get('DB_USER').required().asString();
export const DB_PASS = env.get('DB_PASS').required().asString();
export const DB_NAME = env.get('DB_NAME').required().asString();
export const DATABASE_READER_URLS_PUBLIC_API = env
  .get('DATABASE_READER_URLS_PUBLIC_API')
  .asString();

// slack integration
export const SLACK_TEAM_ID = env.get('SLACK_TEAM_ID').asString();
export const SLACK_KEY_GENERATION_BOT_CHANNEL = env
  .get('SLACK_KEY_GENERATION_BOT_CHANNEL')
  .asString();
export const SLACK_KEY_GENERATION_BOT_COMMAND = env
  .get('SLACK_KEY_GENERATION_BOT_COMMAND')
  .asString();

// test postgres
export const TEST_DB_HOST = env.get('TEST_DB_HOST').required().asString();
export const TEST_DB_PORT = env.get('TEST_DB_PORT').required().asInt();
export const TEST_DB_USER = env.get('TEST_DB_USER').required().asString();
export const TEST_DB_PASS = env.get('TEST_DB_PASS').required().asString();
export const TEST_DB_NAME = env.get('TEST_DB_NAME').required().asString();
export const TEST_DATABASE_READER_URLS_PUBLIC_API = env
  .get('TEST_DATABASE_READER_URLS_PUBLIC_API')
  .asString();

export const CI = env.get('CI').asBool();

export const SEQUELIZE_LOG = env.get('SEQUELIZE_LOG').asBool();

// JWT
export const JWT_KEY = env.get('JWT_KEY').required().asString();
export const JWT_EXPIRES = env.get('JWT_EXPIRES').default('90d').asString();

export const HIDE_REPLICATION_LOGS = env
  .get('HIDE_REPLICATION_LOGS')
  .default('false')
  .asBool();

// AWS
export const AWS_REGION = env.get('DYNAMO_DB_REGION').required().asString();
export const AWS_ACCESS_KEY_ID = env
  .get('AMAZON_ACCESS_KEY')
  .required()
  .asString();
export const AWS_SECRET_ACCESS_KEY = env
  .get('AMAZON_SECRET_KEY')
  .required()
  .asString();

export const S3_BUCKET_MARKET_DATA_UPLOADS = env
  .get('AMAZON_S3_BUCKET_MARKET_DATA_UPLOADS')
  .required()
  .asString();
