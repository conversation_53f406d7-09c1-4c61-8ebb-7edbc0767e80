import { ENodeEnv, NODE_ENV } from '@app/core/config/env';
import { applyDecorators } from '@nestjs/common';
import { ApiExcludeController, ApiExcludeEndpoint } from '@nestjs/swagger';

/**
 * `true` if we wish to hide endpoints and controllers meant strictly for internal use in current `NODE_ENV`
 */
const IS_ENV_TO_HIDE_INTERNAL_ENDPOINTS =
  NODE_ENV === ENodeEnv.PROD || NODE_ENV === ENodeEnv.MOCK;

export const HideControllerOnlyInProd = () => {
  return applyDecorators(
    ApiExcludeController(IS_ENV_TO_HIDE_INTERNAL_ENDPOINTS),
  );
};

export const HideEndpointOnlyInProd = () => {
  return applyDecorators(ApiExcludeEndpoint(IS_ENV_TO_HIDE_INTERNAL_ENDPOINTS));
};
