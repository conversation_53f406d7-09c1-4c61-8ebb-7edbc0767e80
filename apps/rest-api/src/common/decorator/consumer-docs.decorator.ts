import { Api<PERSON>aram, ApiQuery } from '@nestjs/swagger';

import { ConsumerInferenceDataEndpointResponse } from '../../api/allora/inference-consumer/consumer-inference-data.response';
import { Api<PERSON>ey } from './api-key.decorator';
import { ApiResponses } from './api-responses.decorator';

/**
 * @dev Does not include extraData field! That has to be added in the endpoint-specific docs.
 * @returns docs common to all endpoints to pull numeric data for Upshot adapters
 */
export const generateInferenceDataConsumerDocs = (
  topicId = 1,
  responseIsArray = false,
) => {
  return [
    ApiKey(),
    ApiParam({
      name: 'chain_slug',
      type: String,
      required: true,
      description:
        'Slug of the chain on which the target adapter contract is deployed',
      example: 'ethereum-11155111',
    }),
    ApiParam({
      name: 'topic_id',
      type: String,
      required: true,
      description: 'Id of the topic to onboard signed index data to',
      example: `${topicId}`,
    }),
    ...ApiResponses(
      responseIsArray
        ? [ConsumerInferenceDataEndpointResponse]
        : ConsumerInferenceDataEndpointResponse,
    ),
  ];
};

/**
 * @dev Does not include extraData field! That has to be added in the endpoint-specific docs.
 * @dev Main diff from v1: 2 topic ids. One of which will be deleted later when updated in Adapter.
 * @returns docs common to all endpoints to pull numeric data for Upshot adapters
 */
export const generateInferenceDataConsumerDocsV2 = (
  topicId = 1,
  responseIsArray = false,
) => {
  return [
    ApiKey(),
    ApiParam({
      name: 'chain_slug',
      type: String,
      required: true,
      description:
        'Slug of the chain on which the target adapter contract is deployed',
      example: 'ethereum-11155111',
    }),
    ApiQuery({
      name: 'allora_topic_id',
      type: String,
      required: true,
      description: 'Id of the topic in Allora',
      example: `${topicId}`,
    }),
    ...ApiResponses(
      responseIsArray
        ? [ConsumerInferenceDataEndpointResponse]
        : ConsumerInferenceDataEndpointResponse,
    ),
  ];
};
