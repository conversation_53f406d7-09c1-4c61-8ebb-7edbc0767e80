import { ENodeEnv, NODE_ENV } from '@app/core/config/env';
import { applyDecorators } from '@nestjs/common';
import { ApiHeader } from '@nestjs/swagger';

/**
 * `true` if we wish to hide endpoints and controllers meant strictly for internal use in current `NODE_ENV`
 */
const IS_ENV_TO_HIDE_API_KEY_INPUT =
  NODE_ENV === ENodeEnv.PROD ||
  NODE_ENV === ENodeEnv.STAGE ||
  NODE_ENV === ENodeEnv.MOCK ||
  NODE_ENV === ENodeEnv.STAGE_MOCK;

export const ApiKey = () => {
  if (!IS_ENV_TO_HIDE_API_KEY_INPUT)
    return applyDecorators(
      ApiHeader({ name: 'x-api-key', description: 'API key' }),
    );
  return applyDecorators();
};
