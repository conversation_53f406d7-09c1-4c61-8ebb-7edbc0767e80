import { applyDecorators } from '@nestjs/common';
import { ApiQuery } from '@nestjs/swagger';

export const TimeseriesFiltersDocs = (defaults?: {
  start?: string;
  end?: string;
}) => {
  const start = defaults?.start ? `default: ${defaults?.start}` : '';
  const end = defaults?.end ? `default: ${defaults?.end}` : '';
  return applyDecorators(
    ApiQuery({
      name: 'start',
      type: Number,
      description: `earliest unix timestamp in window. Must not exceed \`end\`. ${start}.`,
      required: false,
    }),
    ApiQuery({
      name: 'end',
      type: Number,
      description: `latest unix timestamp in window. Must not be less than \`start\`. ${end}.`,
      required: false,
    }),
  );
};
