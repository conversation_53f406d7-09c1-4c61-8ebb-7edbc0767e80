import { applyDecorators } from '@nestjs/common';
import { ApiProperty, ApiQuery } from '@nestjs/swagger';

import { ApiResponses } from './api-responses.decorator';

export const ContinuationTokenDocs = () => {
  return applyDecorators(
    ApiQuery({
      name: 'continuation_token',
      type: String,
      description: `Send the latest \`continuation_token\` you received in the response to get the next page of results.`,
      required: false,
    }),
  );
};

/**
 * TODO test and proliferate use of there where applicable
 * @dev Use this decorator to add a `continuation_token` property to the response
 *  in lieu of the `ApiResponses()` decorator.
 * @param propertyName
 * @param propertyType
 */
export const ContinuationPaginationResponseDocs = (
  propertyName: string,
  propertyType: any,
) => {
  class ContinuationPaginationResponse {
    @ApiProperty({
      type: String,
      description: `Send this value back in the next request to get the next page of results.`,
      example: 'cb3b6f8f-83ea-401d-b54e-d0dc34cf8319',
      nullable: true,
    })
    continuation_token: string | null;

    constructor() {
      this[propertyName] = [propertyType];
    }
  }

  return ApiResponses(ContinuationPaginationResponse);
};
