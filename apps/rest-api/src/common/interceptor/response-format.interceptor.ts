import {
  CallHandler,
  ExecutionContext,
  Injectable,
  NestInterceptor,
} from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { ClsService } from 'nestjs-cls';
import { map, Observable } from 'rxjs';

import { sendJsonResponse } from '../utils';

@Injectable()
export class ResponseFormatInterceptor implements NestInterceptor {
  constructor(
    private readonly cls: ClsService,
    private readonly reflector: Reflector,
  ) {}

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    return next.handle().pipe(
      map((flow) => {
        const request = context.switchToHttp().getRequest();
        const response = context.switchToHttp().getResponse();
        const apiUser = request.apiUser;

        if (apiUser && apiUser.rateLimit) {
          const nextSecondTimestamp =
            Math.ceil(apiUser.rateLimit.timestamp / 1000) * 1000;
          const millisecondsLeftUntilNextSecond =
            nextSecondTimestamp - apiUser.rateLimit.timestamp;

          response.setHeader(
            'X-RateLimit-Limit',
            apiUser.tier.requestsPerSecond,
          );
          response.setHeader(
            'X-RateLimit-Remaining',
            apiUser.tier.requestsPerSecond - apiUser.rateLimit.requestCount,
          );
          response.setHeader(
            'X-RateLimit-Reset',
            millisecondsLeftUntilNextSecond,
          );
        }

        const isUnwrappedJsonRequested =
          request.headers['x-unwrapped-json'] === 'true';
        const provideUnwrappedResponse = this.reflector.get<boolean>(
          'unwrappedResponse',
          context.getHandler(),
        );

        if (isUnwrappedJsonRequested || provideUnwrappedResponse) {
          return flow;
        }

        return sendJsonResponse({
          request_id: this.cls.getId(),
          content: flow,
        });
      }),
    );
  }
}
