import {
  <PERSON><PERSON><PERSON><PERSON>,
  ExecutionContext,
  HttpException,
  Injectable,
  NestInterceptor,
} from '@nestjs/common';
import { Observable } from 'rxjs';

const APPLICATION_JSON = 'application/json' as const;
const POST_FORM = 'application/x-www-form-urlencoded' as const;
const VALID_CONTENT_TYPES = [APPLICATION_JSON, POST_FORM] as const;

const errorMsg = (header: string) =>
  `Request must include header '${header}' with value '${APPLICATION_JSON}'`;

/**
 * Interceptor to check if request has the correct headers
 * If request has...
 *   empty body => anything goes
 *   non-empty body => must have `content-type: application/json` header
 * This is also the very first interceptor to run => first log emitted here.
 */
@Injectable()
export class HeaderCheckInterceptor implements NestInterceptor {
  async intercept(
    context: ExecutionContext,
    next: CallHandler,
  ): Promise<Observable<any>> {
    const request = context.switchToHttp().getRequest();

    if (request.url.slice(14) === 'generateAPIUserFromSlackBotRequest') {
      return next.handle();
    }

    const headers = {};
    for (const key of Object.keys(request.headers)) {
      headers[key.toLowerCase()] = request.headers[key];
    }

    // we only need to check the content-type header if the request has a body
    // if a body exists, be 'application/json' or 'application/x-www-form-urlencoded'
    if (Object.keys(request.body).length > 0) {
      const header = 'content-type';
      const contentType = headers[header];

      if (!contentType || VALID_CONTENT_TYPES.indexOf(contentType) === -1) {
        throw new HttpException(errorMsg(header), 400);
      }
    }

    return next.handle();
  }
}
