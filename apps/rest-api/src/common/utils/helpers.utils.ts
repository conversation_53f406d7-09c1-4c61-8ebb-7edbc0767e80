import { HttpException, HttpStatus } from '@nestjs/common';
import axios, { AxiosRequestConfig } from 'axios';
import { formatEther } from 'ethers/lib/utils';
import pLimit from 'p-limit';

import { ROUNDING_DIGITS } from './constants.utils';
import { ETimeWindow } from './enum.utils';

interface IResponse<T> {
  request_id?: string;
  status: boolean;
  message?: string;
  data: T;
}

/**
 * Sends default JSON response to client
 * @param {string} request_id UUID
 * @param {*} content
 */
export const sendJsonResponse = <T>({
  request_id,
  content,
}: {
  request_id?: string;
  content: T;
}): IResponse<T> | T => {
  if (content && content['response_type'] === 'in_channel') {
    return content;
  }
  return {
    request_id,
    status: true,
    data: content,
  };
};

export const sendPaginateResponse = (
  message: string,
  content: Record<string, unknown>,
  paginate: any,
) => {
  return {
    status: true,
    message,
    data: content,
    meta: paginate,
  };
};

/**
 * Sends error response to client
 * @param {*} content
 * @param {*} message
 * @param code
 */
export const sendErrorResponse = (
  code: string,
  message: string,
  content: Record<string, unknown>,
): IResponse<Record<string, unknown>> => {
  const data = {
    status: false,
    message,
    data: content,
  };

  throw new HttpException(data, HttpStatus[code]);
};

/**
 * Utility method that send an http request based off on the inputed parameters
 * @param  {AxiosRequestConfig} requestConfig
 * @returns Promise
 */
export const sendRequest = async (
  requestConfig: AxiosRequestConfig,
): Promise<any> => {
  try {
    const response = await axios(requestConfig);
    return response.data;
  } catch (err) {
    throw {
      status: err.response.status,
      statusText: err.response.statusText,
      msg: err.response.data,
    };
  }
};

export const getNow = (datestring?: string | Date): number => {
  return Math.round(
    datestring ? new Date(datestring).getTime() / 1000 : Date.now() / 1000,
  );
};

/**
 * @param {EWindowSize} windowSize Size of timedelta
 * @param {number} now Current time in seconds
 * @returns Unix timedelta of `windowSize` (in seconds). Returns `now`
 *  if `windowSize == ALLTIME`.
 */
export const windowSize2Seconds = (windowSize: ETimeWindow): number => {
  switch (windowSize) {
    case ETimeWindow.HOUR:
      return 3600;
    case ETimeWindow.DAY:
      return 3600 * 24;
    case ETimeWindow.WEEK:
      return 3600 * 24 * 7;
    case ETimeWindow.MONTH:
      return 3600 * 24 * 30;
    case ETimeWindow.ALLTIME:
      return getNow();
    default:
      return 0;
  }
};

export const getEthFromWei = (weiString: string): number => {
  return parseFloat(formatEther(weiString));
};

export const bnPercentDelta = (
  wei_start: string,
  wei_end: string,
): number | null => {
  const start = getEthFromWei(wei_start);
  if (Math.abs(start) < Math.pow(10, -6)) return null;
  return parseFloat(
    ((getEthFromWei(wei_end) - start) / start).toFixed(ROUNDING_DIGITS),
  );
};

export const runConcurrently = <T>(
  promises: (Promise<any> | any)[],
  maxConcurrency: number,
): Promise<T[]> => {
  const limit = pLimit(maxConcurrency);
  return Promise.all(promises.map((promise) => limit(() => promise)));
};

/**
 * @param {String} str the string to split
 * @returns {Array<String>} containing the sub-strings within the input string when it is split on spaces with
 *          the exception of spaces contained within double quotes (such double quotes are then stripped away).
 *          E.g.: hello to "the world" => ["hello", "to", "the world"]
 */
export const splitOnSpacesNotInsideQuotes = (str: string): Array<string> => {
  const regex = /"[^"]+"|[^\s]+/g;

  const matches = str.match(regex);
  if (matches) {
    const textParts = matches.map((e) => e.replace(/"(.+)"/, '$1'));
    return textParts;
  } else {
    return [];
  }
};
