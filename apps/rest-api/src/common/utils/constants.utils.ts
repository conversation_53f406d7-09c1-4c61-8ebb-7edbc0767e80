import { EAlloraNetworkId } from './enum.utils';

/**
 * For building arrays in pg
 */
export const INTRA_ARRAY_JOIN = ', ';

/**
 * In the unnested override, this means that the ask or related value was nullified, not unchanged.
 * null => unchanged. -1 => cancelled/nullified.
 */
export const NULLIFY_SIGNIFIER = '-1';

/**
 * Wallet with GMI above this => they are based
 */
export const NONEXISTENT_USER_ID = -1;

/**
 * pMap concurrency parameter to use per API request
 */
export const REDIS_CONCURRENCY = 20;

/**
 * Round all decimals to 6 decimal places, because this looks neater
 */
export const ROUNDING_DIGITS = 6;

export const LIMIT = 100;
export const OFFSET = 0;

/**
 * Decimal precision for floats
 */
export const PRECISION = 6;

// Keywords not allowed in the filter function
export const keywordsNotAllowed = [
  'nonce',
  'limit',
  'offset',
  'orderby',
  'to',
  'current',
  'nostale',
  'assetids',
];

export const MAX_API_CALL_PER_CREDIT = 100;

export const API_TIER_RPS = {
  one: 2,
  two: 25,
  three: 40,
};

export const SECONDS_TO_EXPIRE_SIGNATURES = 60 * 60; // 1 hour

/**
 * How many assets to show in the preview for each trait, when trait previews requested
 */
export const NUM_PREVIEW_EXAMPLE_ASSETS = 4;

export const DEFAULT_ALLORA_NETWORK_ID = EAlloraNetworkId.TESTNET;
