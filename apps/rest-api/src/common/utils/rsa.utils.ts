import * as crypto from 'crypto';

/**
 * Utility class for RSA operations.
 */
export class RSAUtils {
  /**
   * Verifies the signature of the given parameters using the provided public key.
   * @param parameterStr - The string representation of the parameters to verify.
   * @param publicKey - The public key used for verification.
   * @param sign - The signature to verify.
   * @returns A boolean indicating whether the signature is valid or not.
   */
  static verify(
    parameterStr: string,
    publicKey: string,
    sign: string,
  ): boolean {
    try {
      const verify = crypto.createVerify('SHA256');
      verify.update(parameterStr);
      verify.end();
      return verify.verify(publicKey, sign, 'base64');
    } catch (error) {
      console.error('Error verifying signature:', error);
      return false;
    }
  }

  /**
   * Retrieves the public key from the given string representation.
   * @param publicKey - The string representation of the public key.
   * @returns The public key as a crypto.KeyObject.
   * @throws Error if there is an error getting the public key.
   */
  static getPublicKey(publicKey: string): crypto.KeyObject {
    try {
      return crypto.createPublicKey(publicKey);
    } catch (error) {
      throw new Error('Error getting public key: ' + error.message);
    }
  }

  /**
   * Retrieves the private key from the given string representation.
   * @param privateKey - The string representation of the private key.
   * @returns The private key as a crypto.KeyObject.
   * @throws Error if there is an error getting the private key.
   */
  static getPrivateKey(privateKey: string): crypto.KeyObject {
    try {
      return crypto.createPrivateKey(privateKey);
    } catch (error) {
      throw new Error('Error getting private key: ' + error.message);
    }
  }

  /**
   * Signs the given parameters using the provided private key.
   * @param parameters - The string representation of the parameters to sign.
   * @param privateKey - The private key used for signing.
   * @returns The signature as a string.
   * @throws Error if there is an error signing the parameters.
   */
  static sign(parameters: string, privateKey: string): string {
    try {
      const sign = crypto.createSign('SHA256');
      sign.update(parameters);
      sign.end();
      return sign.sign(privateKey, 'base64');
    } catch (error) {
      throw new Error('Error signing parameters: ' + error.message);
    }
  }
}
