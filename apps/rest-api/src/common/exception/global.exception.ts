import {
  ArgumentsHost,
  Catch,
  ExceptionFilter,
  HttpException,
  HttpStatus,
  Logger,
} from '@nestjs/common';
import { StatsD } from 'hot-shots';
import { ClsServiceManager } from 'nestjs-cls/dist/src/lib/cls-service-manager';

@Catch()
export class GlobalExceptionsFilter implements ExceptionFilter {
  private readonly logger = new Logger(GlobalExceptionsFilter.name);

  constructor(private readonly dataDogClient: StatsD) {}

  catch(exception: unknown, host: ArgumentsHost): void {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse();
    const endpoint = ctx.getRequest().url.split('?')[0];

    const status = false;
    let statusCode, resObj, meta, apiResponseMessage, logMessage;
    if (exception instanceof HttpException) {
      statusCode = exception.getStatus();
      resObj = exception.getResponse();
      apiResponseMessage =
        statusCode === 429 ? 'Too many request' : resObj.message;
      logMessage = apiResponseMessage;
      meta = resObj.data || undefined;
    } else {
      statusCode = HttpStatus.INTERNAL_SERVER_ERROR;
      apiResponseMessage =
        'Sorry, we are unable to process your request. If you think this is an error, please send us the request_id via <NAME_EMAIL>';
      logMessage =
        exception instanceof Error ? exception.message : apiResponseMessage;
      this.dataDogClient.increment(`xyz.upshot.backend.endpoint.500`, [
        `endpoint:${endpoint}`,
      ]);
    }
    this.logger.error(logMessage, (exception as any).stack);
    const request_id = ClsServiceManager.getClsService().getId();

    return response
      .status(statusCode)
      .json({ request_id, status, apiResponseMessage, meta });
  }
}
