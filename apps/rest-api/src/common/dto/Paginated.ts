import { applyDecorators } from '@nestjs/common';
import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsInt, IsOptional, IsString, Max, Min } from 'class-validator';

import { IsAnyUuid } from '../../validator/uuid.validator';
import * as Constants from './constants';

export const Limit = (args?: {
  default?: number;
  max?: number;
  min?: number;
}) =>
  applyDecorators(
    IsOptional(),
    IsInt(),
    Max(args?.max ?? Constants.MAX_LIMIT),
    Min(args?.min ?? Constants.MIN_LIMIT),
    ApiProperty({
      default: args?.default ?? Constants.DEFAULT_LIMIT,
      required: false,
    }),
    Type(() => Number),
  );

export class Limited {
  @IsOptional()
  @IsInt()
  @Limit()
  @ApiProperty({ default: Constants.DEFAULT_LIMIT, required: false })
  limit: number = Constants.DEFAULT_LIMIT;
}

export class Paginated extends Limited {
  @IsOptional()
  @IsInt()
  @ApiProperty({ default: Constants.DEFAULT_OFFSET, required: false })
  @Type(() => Number)
  offset: number = Constants.DEFAULT_OFFSET;
}

export class CursorPaginated {
  @IsOptional()
  @ApiProperty({ type: String, required: false })
  @IsString()
  @IsAnyUuid({ each: true })
  continuation_token?: string;
}
