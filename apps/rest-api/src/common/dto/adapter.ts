import * as CustomValidators from '@app/core/app/validation/validators';
import { IsOptional, IsString, Validate } from 'class-validator';

export class GetDataForAdapterParamDto {
  @IsString()
  @Validate(CustomValidators.AlphanumericOrLimitedSet)
  chain_slug: string;
}

export class GetDataForAdapterQueryDto {
  @IsString()
  @Validate(CustomValidators.AlphanumericOrLimitedSet)
  @IsOptional()
  extra_data?: string;

  @IsString()
  @Validate(CustomValidators.NumericString)
  allora_topic_id: string;
}
