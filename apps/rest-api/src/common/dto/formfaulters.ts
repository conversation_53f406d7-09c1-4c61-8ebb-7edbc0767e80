import * as Constants from './constants';

export function formatStringArrayQueryParam<T>(
  param: T[] | undefined | T,
  maxSize?: number,
): T[] {
  if (!maxSize) maxSize = Constants.ARRAY_MAX_SIZE;

  if (!param) {
    return [];
  }

  if (!Array.isArray(param) && typeof param === 'string' && param.length > 0) {
    return [param];
  }

  if (Array.isArray(param)) {
    return param.slice(0, maxSize);
  }

  return [];
}
