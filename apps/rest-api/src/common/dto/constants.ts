export const MIN_LIMIT = 1;
export const MAX_LIMIT = 500;

export const DEFAULT_LIMIT = 50;

export const DEFAULT_OFFSET = 0;

export const SECONDS_IN_A_DAY = 60 * 60 * 24;
export const SECONDS_IN_30_MINUTES = 60 * 30;

export const ARRAY_MAX_SIZE = 500;

export const UUID_VERSION = 4;

export const INCLUDE_COUNT_DESCRIPTION =
  'whether or not to return a count of all possible results (faster if `false`). default: `false`';
