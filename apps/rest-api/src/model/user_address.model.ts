import { Column, DataType, Model, NotEmpty, Table } from 'sequelize-typescript';

export interface IBasicAttributes {
  id?: number;
  created_at?: Date;
}

export const exclude = ['created_at', 'createdAt'];

export interface IUserAddressUniqueAttributes
  extends Pick<
    UserAddress,
    'user_id' | 'address' | 'name' | 'is_public' | 'nonce'
  > {}

export interface IUserAddressAttributes
  extends IUserAddressUniqueAttributes,
    Omit<IBasicAttributes, 'id'> {}

@Table({
  tableName: 'user_addresses',
  defaultScope: { attributes: { exclude } },
  underscored: true,
})
export class UserAddress extends Model<IUserAddressAttributes> {
  @NotEmpty
  @Column({
    type: DataType.INTEGER,
    allowNull: true,
  })
  user_id?: number;

  // TODO: seamless way to always set address to lowercase value.
  @NotEmpty
  @Column({
    primaryKey: true,
    type: DataType.TEXT,
    allowNull: false,
  })
  address: string;

  @Column({
    type: DataType.TEXT,
  })
  name?: string;

  @NotEmpty
  @Column({
    type: DataType.BOOLEAN,
    allowNull: false,
    defaultValue: false,
  })
  is_public?: boolean;

  @Column({
    type: DataType.TEXT,
  })
  nonce?: string;
}

export default UserAddress;
