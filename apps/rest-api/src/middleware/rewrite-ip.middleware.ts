import { Injectable, NestMiddleware } from '@nestjs/common';
import { Request, Response } from 'express';
import { StatsD } from 'hot-shots';

@Injectable()
export class RewriteIpMiddleware implements NestMiddleware {
  constructor(private readonly dataDogClient: StatsD) {}

  use(req: Request, res: Response, next: () => void) {
    const cloudflareIpHeader = req.header('cf-connecting-ip');
    if (cloudflareIpHeader) {
      req['realIp'] = cloudflareIpHeader;
    } else {
      req['realIp'] = req.ip;
    }
    this.dataDogClient.increment(`xyz.upshot.backend.request.all`, [
      `endpoint:${req.url.split('?')[0]}`,
    ]);
    next();
  }
}
