/*global Enumerator: false, ActiveXObject: false */

import { ApiProperty } from '@nestjs/swagger';
import { Contains, IsInt, IsString, Length } from 'class-validator';

export class GenerateAPIUserFromSlackBotDTO {
  @IsString()
  @Contains('T01C1N48ZLL')
  @Length(11)
  team_id: string;

  @IsString()
  @Contains('C03B607TH1R')
  @Length(11)
  channel_id: string;

  @IsString()
  @Contains('/generatekey-v2')
  @Length(11)
  command: string;

  @IsString()
  text: string;
}

export class GenerateAPIUserFromSlackBotResponseDTO {
  @IsString()
  @Contains('in_channel')
  @Length(10)
  response_type: string;

  @IsString()
  text: string;
}

export class UpdatePricePerCredit {
  @IsString()
  slug?: string;

  @IsString()
  pricePerCredit?: string;
}

export class CreateAPICreditDTO {
  @IsString()
  slug: string;

  @IsInt()
  @ApiProperty({ default: 0 })
  credit_amount: number;
}
