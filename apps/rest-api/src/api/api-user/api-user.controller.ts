import { GetApiUserUseCase } from '@app/core/app/use-case/api/get.api-user.use-case';
import { CustomApiOperation } from '@app/core/infra/utils/custom-api-operation.decorator';
import { Trace } from '@app/core/infra/utils/tracing.decorator';
import {
  Body,
  Controller,
  Get,
  Headers,
  Logger,
  NotFoundException,
  Post,
} from '@nestjs/common';
import { ApiExcludeEndpoint, ApiTags } from '@nestjs/swagger';

import { GenerateAPIUserFromSlackBotDTO } from './api-user.dto';
import { ApiUserResponse } from './response/api-user.response';

@Controller('api-users')
@ApiTags('API Usage')
export class APIUserController {
  private readonly logger = new Logger(APIUserController.name);

  constructor(
    private readonly getApiUserUseCase: GetApiUserUseCase, //TODO - can be injected via method?
  ) {}

  @ApiExcludeEndpoint()
  @Post('/generateAPIUserFromSlackBotRequest')
  async generateAPIUserFromSlackBotRequest(
    @Body() payload: GenerateAPIUserFromSlackBotDTO,
  ) {
    this.logger.log('api-users/generateAPIUserFromSlackBotRequest');
    //return this.apiUserService.generateAPIUserFromSlackBotRequest(payload);
  }

  @Get('/')
  @CustomApiOperation({
    description:
      'Obtain details about the API user, identified by the provided API key, including information such as the current subscription tier, consumed usage, and more.',
    summary: 'API User Data',
  })
  @Trace
  async getApiUser(@Headers() headers) {
    const apiUser = await this.getApiUserUseCase.execute(headers['x-api-key']);
    if (!apiUser) {
      throw new NotFoundException('API User not found');
    }

    const apiKey = apiUser.getApiKeyByKey(headers['x-api-key']);

    if (!apiKey) {
      throw new NotFoundException('API Key not found');
    }

    return new ApiUserResponse(apiUser, apiKey);
  }
}
