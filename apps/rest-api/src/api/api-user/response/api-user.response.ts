import { Api<PERSON><PERSON> } from '@app/core/domain/api/user/api-key';
import { ApiUser } from '@app/core/domain/api/user/api-user';

type TApiKey = {
  key: string;
  monthly_usage: number;
};

type TierType = {
  name: string;
  monthly_limit: number;
  requests_per_second: number;
  overage_cost_per_request: number;
};

export class ApiUserResponse {
  email: string;
  api_key: TApiKey;
  overage_warning_alerts: boolean;
  tier: TierType;
  billing_date: Date;
  total_monthly_usage: number;

  constructor(apiUser: ApiUser, apiKey: ApiKey) {
    this.email = apiUser.email ?? '';
    this.api_key = {
      key: apiKey.key,
      monthly_usage: apiKey.monthlyUsage,
    };
    this.overage_warning_alerts = apiUser.overageWarningAlerts;
    this.tier = {
      name: apiUser.tier.name,
      monthly_limit: apiUser.tier.monthlyLimit,
      requests_per_second: apiUser.tier.requestsPerSecond,
      overage_cost_per_request: apiUser.tier.overageCostPerRequest,
    };
    this.billing_date = apiUser.nextBillingDate();
    this.total_monthly_usage = apiUser.monthlyUsage();
  }
}
