/* eslint-disable @typescript-eslint/ban-ts-comment */
import { Injectable, Logger } from '@nestjs/common';
/*
import { v4 as uuid } from 'uuid';

import {
  CreateAPICreditDTO,
  GenerateAPIUserFromSlackBotDTO,
  GenerateAPIUserFromSlackBotResponseDTO,
  UpdatePricePerCredit,
} from '../../../common/dto';
import { splitOnSpacesNotInsideQuotes } from '../../../common/utils';
import {
  SLACK_KEY_GENERATION_BOT_CHANNEL,
  SLACK_KEY_GENERATION_BOT_COMMAND,
  SLACK_TEAM_ID,
} from '../../../config';
*/
@Injectable()
export class APIUserService {
  private readonly logger = new Logger(APIUserService.name);

  /*
  async generateAPIUserFromSlackBotRequest(
    payload: GenerateAPIUserFromSlackBotDTO,
  ): Promise<GenerateAPIUserFromSlackBotResponseDTO> {
    this.logger.log(
      this.generateAPIUserFromSlackBotRequest.name,
      'service function',
    );
    if (
      payload.team_id != SLACK_TEAM_ID ||
      payload.channel_id != SLACK_KEY_GENERATION_BOT_CHANNEL ||
      payload.command != SLACK_KEY_GENERATION_BOT_COMMAND
    ) {
      return {
        response_type: 'in_channel',
        text: 'Error in payload arguments. You are probably trying to use the bot from a non-whitelisted channel or command.',
      };
    }

    const textParts = splitOnSpacesNotInsideQuotes(payload.text);

    if (textParts.length != 2) {
      return {
        response_type: 'in_channel',
        text: `Number of slash command arguments incorrect! Follow this form: *${payload.command} [entityName] [tier: one|two|three]*`,
      };
    }

    const entityName = textParts[0];
    const tier = textParts[1].toLowerCase() as EAPITierName;

    if (!Object.values(EAPITierName).includes(tier as EAPITierName)) {
      return {
        response_type: 'in_channel',
        text: `Tier value is malformed! The value *"${textParts[1]}"* was provided. It must be one of the following: *one*, *two* or *three*.`,
      };
    }

    const args: CreateAPIUserDTO = {
      entity: entityName as string,
      tiers: tier,
    };
    let apiUser;
    try {
      apiUser = await this.generateAPIUser(args);
    } catch (e) {
      return {
        response_type: 'in_channel',
        text: `There was an error within the key generation API service.`,
      };
    }

    if (
      apiUser.api_key.length != 27 ||
      apiUser.api_key.substring(0, 3) != 'UP-'
    ) {
      return {
        response_type: 'in_channel',
        text: `There was an error with the key returned by the key generation API service.`,
      };
    }

    if (!(apiUser.tiers.toUpperCase() in EAPITierName)) {
      return {
        response_type: 'in_channel',
        text: `There was an error due to an unexpected tier value set for the API user that was generated.`,
      };
    }

    return {
      response_type: 'in_channel',
      text: `New tier _${apiUser.tiers}_ API key generated for entity "${entityName}": \n*${apiUser.api_key}*`,
    };
  }
*/
}
