import { GetApiUserUseCaseModule } from '@app/core/app/use-case/api/get.api-user.use-case.module';
import { RedisModule } from '@app/core/infra/redis/redis.module';
import { Module } from '@nestjs/common';

import { APIUserController } from './api-user.controller';
import { APIUserService } from './api-user.service';

@Module({
  imports: [RedisModule, GetApiUserUseCaseModule],
  controllers: [APIUserController],
  providers: [APIUserService],
})
export class APIUserModule {}
