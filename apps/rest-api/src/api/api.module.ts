import { Module } from '@nestjs/common';

import { AdapterModule } from './allora/adapter/module';
import { AlloraForgeModule } from './allora/allora-forge/allora-forge.module';
import { AlloraUserModule } from './allora/allora-user/allora-user.module';
import { BinanceTasksModule } from './allora/binance-tasks/binance-tasks.module';
import { ConsumerModule } from './allora/inference-consumer/inference-consumer.module';
import { MarketDataModule } from './allora/market-data/market-data.module';
import { PointModule } from './allora/point/point.module';
import { ReputerDataModule } from './allora/reputer/reputer.module';
import { TopicModule } from './allora/topics/topic.module';
import { APIUserModule } from './api-user/api-user.module';
// DISABLED: Legacy Robonet and Wallet API modules - can be re-enabled if needed
// import { AgentModule } from './robonet/agent/agent.module';
// import { RobonetMailingListModule } from './robonet/mailing-list/mailing-list.module';
// import { PointModule as RoboNetPointModule } from './robonet/point/point.module';
// import { VaultModule } from './robonet/vault/vault.module';
// import { VaultUserModule } from './robonet/vault-user/vault-user.module';
import { UserAddressesModule } from './user_addresses/user_addresses.module';
// import { WalletModule } from './wallet/wallet.module';

@Module({
  imports: [
    AdapterModule,
    APIUserModule,
    ConsumerModule,
    UserAddressesModule,
    // DISABLED: Legacy Robonet and Wallet API modules - can be re-enabled if needed
    // WalletModule,
    // RoboNetPointModule,
    // VaultModule,
    // AgentModule,
    // VaultUserModule,
    AlloraUserModule,
    PointModule,
    BinanceTasksModule,
    TopicModule,
    MarketDataModule,
    ReputerDataModule,
    AlloraForgeModule,
    // RobonetMailingListModule,
  ],
})
export class ApiModule {}
