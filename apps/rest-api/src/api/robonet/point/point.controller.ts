import { GetActiveRoboNetSeasonQuery } from '@app/core/app/use-case/robonet/point/query/get-active-seasion/get-active-season.query';
import { GetSeasonLeaderboardQuery } from '@app/core/app/use-case/robonet/point/query/get-season-leaderboard/get-season-leaderboard.query';
import { GetUserTotalSeasonPointsQuery } from '@app/core/app/use-case/robonet/point/query/get-user-total-season-points/get-user-total-season-points.query';
import { ContinuationToken } from '@app/core/domain/continuation-token/continuation-token';
import {
  Controller,
  Get,
  NotFoundException,
  Param,
  Query,
} from '@nestjs/common';
import { QueryBus } from '@nestjs/cqrs';
import { ApiTags } from '@nestjs/swagger';

import { LeaderboardQueryDTO } from './dto/leaderboard.dto';
import * as Docs from './point.docs';
import { RoboNetSeasonResponse } from './response/season.response';
import { SeasonUserLeaderboardEntryResponse } from './response/user-leaderboard-entry.response';

@Controller('robonet/points')
@ApiTags('Robonet Points')
export class PointController {
  constructor(private readonly queryBus: QueryBus) {}

  @Get('season/active')
  @Docs.getActivePointsSeasonDecorators()
  async getActivePointsSeason() {
    const season = await this.queryBus.execute(
      new GetActiveRoboNetSeasonQuery(),
    );

    if (!season) {
      throw new NotFoundException('No active season found');
    }

    return new RoboNetSeasonResponse(season);
  }

  @Get('total/:season_id/:user_address')
  @Docs.getUserTotalSeasonPointsDecorators()
  async getUserTotalSeasonPoints(
    @Param('season_id') seasonId: number,
    @Param('user_address') userAddress: string,
  ) {
    const res = await this.queryBus.execute(
      new GetUserTotalSeasonPointsQuery(seasonId, userAddress),
    );

    if (!res) {
      throw new NotFoundException(
        `No data found for user with address '${userAddress}' in season ${seasonId}`,
      );
    }

    return new SeasonUserLeaderboardEntryResponse(res);
  }

  @Get('leaderboard/:season_id')
  async getSeasonLeaderboard(
    @Param('season_id') seasonId: number,
    @Query() query: LeaderboardQueryDTO,
  ) {
    const pagination = query.continuation_token
      ? { continuationToken: new ContinuationToken(query.continuation_token) }
      : {};

    const res = await this.queryBus.execute(
      new GetSeasonLeaderboardQuery(seasonId, pagination),
    );

    return {
      leaderboard: res.leaderboard.map(
        (entry) => new SeasonUserLeaderboardEntryResponse(entry),
      ),
      continuation_token: res.continuationToken ?? null,
    };
  }
}
