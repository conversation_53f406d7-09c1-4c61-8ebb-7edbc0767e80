import { SeasonUserLeaderboardEntry } from '@app/core/domain/robonet/season/season-user-leaderboard';
import { CustomApiOperation } from '@app/core/infra/utils/custom-api-operation.decorator';
import { Trace } from '@app/core/infra/utils/tracing.decorator';
import { applyDecorators } from '@nestjs/common';
import { ApiParam } from '@nestjs/swagger';

import { ApiResponses } from '../../../common/decorator/api-responses.decorator';
import { RoboNetSeasonResponse } from './response/season.response';

export const getActivePointsSeasonDecorators = () => {
  return applyDecorators(
    Trace,
    CustomApiOperation({
      description: 'Retrieves the active RoboNet rewards season.',
      summary: 'Active Season',
    }),
    ...ApiResponses(RoboNetSeasonResponse),
  );
};

export const getUserTotalSeasonPointsDecorators = () => {
  return applyDecorators(
    Trace,
    CustomApiOperation({
      description: `Get User Total Rewards Points By Season ID`,
      summary: 'User Total Points',
    }),
    ApiParam({
      name: 'season_id',
      type: Number,
      required: true,
      description: 'Season ID',
    }),
    ApiParam({
      name: 'user_address',
      type: Number,
      required: true,
      description: 'Address of the user wallet',
    }),
    ...ApiResponses(SeasonUserLeaderboardEntry),
  );
};
