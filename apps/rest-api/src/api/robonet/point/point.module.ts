import { GetActiveRoboNetSeasonModule } from '@app/core/app/use-case/robonet/point/query/get-active-seasion/get-active-season.module';
import { GetSeasonLeaderboardModule } from '@app/core/app/use-case/robonet/point/query/get-season-leaderboard/get-season-leaderboard.module';
import { GetUserTotalSeasonPointsModule } from '@app/core/app/use-case/robonet/point/query/get-user-total-season-points/get-user-total-season-points.module';
import { Module } from '@nestjs/common';
import { CqrsModule } from '@nestjs/cqrs';

import { PointController } from './point.controller';

@Module({
  imports: [
    CqrsModule,
    GetActiveRoboNetSeasonModule,
    GetUserTotalSeasonPointsModule,
    GetSeasonLeaderboardModule,
  ],
  controllers: [PointController],
})
export class PointModule {}
