import { SeasonUserLeaderboardEntry } from '@app/core/domain/robonet/season/season-user-leaderboard';
import { ApiProperty } from '@nestjs/swagger';

export class SeasonUserLeaderboardEntryResponse {
  @ApiProperty({
    description: 'The ID of the season',
    type: Number,
    example: 1,
  })
  season_id: number;

  @ApiProperty({
    description: 'The address of the user',
    type: String,
    example: '0xAb5801a7D398351b8bE11C439e05C5B3259aeC9B',
  })
  address: string;

  @ApiProperty({
    description: 'The liquidity points of the user for the season',
    type: Number,
    example: 150,
  })
  liquidity_points: number;

  @ApiProperty({
    description: 'The referral points of the user for the season',
    type: Number,
    example: 50,
  })
  referral_points: number;

  @ApiProperty({
    description: 'The total points of the user for the season',
    type: Number,
    example: 200,
  })
  total: number;

  @ApiProperty({
    description: 'The rank/index of the user in the season leaderboard',
    type: Number,
    example: 5,
  })
  rank: number;

  constructor(leaderboardEntry: SeasonUserLeaderboardEntry) {
    this.season_id = leaderboardEntry.seasonId;
    this.address = leaderboardEntry.userAddress;
    this.liquidity_points = leaderboardEntry.liquidityPoints;
    this.referral_points = leaderboardEntry.referralPoints;
    this.total = leaderboardEntry.total;
    this.rank = leaderboardEntry.rank;
  }
}
