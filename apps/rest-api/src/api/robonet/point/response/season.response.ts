import { RoboNetSeason } from '@app/core/domain/robonet/season/season';
import { ApiProperty } from '@nestjs/swagger';

export class RoboNetSeasonResponse {
  @ApiProperty({
    description: 'The ID of the season',
    type: Number,
    example: 1,
  })
  id: number;

  @ApiProperty({
    description: 'The name of the season',
    type: String,
    example: 'Season 1',
  })
  name: string;

  @ApiProperty({
    description: 'The start date of the season',
    type: String,
    example: '2023-03-21T00:00:00.000Z',
  })
  start_date: string;

  @ApiProperty({
    description: 'The end date of the season',
    type: String,
    example: '2023-06-21T00:00:00.000Z',
  })
  end_date: string;

  constructor(season: RoboNetSeason) {
    this.id = season.id;
    this.name = season.name;
    this.start_date = season.startDate.toISOString();
    this.end_date = season.endDate.toISOString();
  }
}
