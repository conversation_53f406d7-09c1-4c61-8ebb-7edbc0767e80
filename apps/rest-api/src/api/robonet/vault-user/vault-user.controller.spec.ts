import { QueryBus } from '@nestjs/cqrs';
import { Test, TestingModule } from '@nestjs/testing';

import { VaultUserController } from './vault-user.controller';

describe('VaultUserController', () => {
  let controller: VaultUserController;

  const mockQueryBus = { execute: jest.fn() };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        VaultUserController,
        { provide: QueryBus, useValue: mockQueryBus },
      ],
    }).compile();

    controller = module.get<VaultUserController>(VaultUserController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
});
