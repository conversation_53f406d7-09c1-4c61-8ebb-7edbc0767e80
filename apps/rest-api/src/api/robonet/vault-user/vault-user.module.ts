import { ConnectUserWalletUseCaseModule } from '@app/core/app/use-case/robonet/vault-user/query/connect-user-wallet/connect-user-wallet.module.';
import { GetUserVaultActivityModule } from '@app/core/app/use-case/robonet/vault-user/query/get-user-vault-activity/get-user-vault-activity.module';
import { GetUserVaultLiquidityPositionModule } from '@app/core/app/use-case/robonet/vault-user/query/get-user-vault-liquidity-position/get-user-vault-liquidity-position.module';
import { VaultActivityQueryRepositoryModule } from '@app/core/domain/robonet/vault-activity/vault-activity.query.repository.module';
import { Module } from '@nestjs/common';
import { CqrsModule } from '@nestjs/cqrs';

import { VaultUserController } from './vault-user.controller';

@Module({
  imports: [
    CqrsModule,
    GetUserVaultActivityModule,
    GetUserVaultLiquidityPositionModule,
    VaultActivityQueryRepositoryModule,
    ConnectUserWalletUseCaseModule,
  ],
  controllers: [VaultUserController],
})
export class VaultUserModule {}
