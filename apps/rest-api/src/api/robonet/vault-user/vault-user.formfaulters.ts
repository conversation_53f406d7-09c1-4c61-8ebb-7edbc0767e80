import { ContinuationToken } from '@app/core/domain/continuation-token/continuation-token';
import { VaultActivityFilters } from '@app/core/domain/robonet/vault-activity/vault-activity.filter';
import { VaultActivityPagination } from '@app/core/domain/robonet/vault-activity/vault-activity.sort';
import { DEFAULT_LIMIT, MAX_LIMIT } from 'apps/rest-api/src/common/dto';

import {
  UserVaultActivityParamDTO,
  UserVaultActivityQueryDTO,
  UserVaultLiquidityPositionParamDTO,
} from './dto/vault-user.dto';

export interface IGetUserVaultActivityQueryArgs {
  pagination: VaultActivityPagination;
  filters: VaultActivityFilters;
  includeCount: boolean;
}

export const UserVaultActivityQueryDTOFormFaulter = (
  param: UserVaultActivityParamDTO,
  query: UserVaultActivityQueryDTO,
): IGetUserVaultActivityQueryArgs => {
  return {
    pagination: {
      limit: Math.min(query.limit ?? DEFAULT_LIMIT, MAX_LIMIT),
      continuationToken: query.continuation_token
        ? new ContinuationToken(query.continuation_token)
        : undefined,
    },
    filters: {
      userAddress: param.user_address.toLowerCase(),
      vaultAddress: param.vault_address.toLowerCase(),
    },
    includeCount: query.include_count ?? false,
  };
};

export const UserVaultLiquidityPositionParamsDTOFormFaulter = (
  param: UserVaultLiquidityPositionParamDTO,
): { filters: VaultActivityFilters } => {
  return {
    filters: {
      userAddress: param.user_address.toLowerCase(),
      vaultAddress: param.vault_address.toLowerCase(),
    },
  };
};
