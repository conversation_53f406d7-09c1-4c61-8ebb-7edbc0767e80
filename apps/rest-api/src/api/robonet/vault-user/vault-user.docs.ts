import { Trace } from '@app/core/infra/utils/tracing.decorator';
import { applyDecorators } from '@nestjs/common';
import { ApiOperation, ApiParam, ApiQuery } from '@nestjs/swagger';

import { Api<PERSON>ey } from '../../../common/decorator/api-key.decorator';
import { ApiResponses } from '../../../common/decorator/api-responses.decorator';
import { ContinuationTokenDocs } from '../../../common/decorator/continuation-token.decorator';
import { DEFAULT_LIMIT, INCLUDE_COUNT_DESCRIPTION } from '../../../common/dto';
import { VaultLiquidityPositionResponse } from '../vault/response/vault.response';
import {
  ConnectWalletResponse,
  UserVaultActivityResponse,
  UserVaultsPortfolioStatsResponse,
} from './response/vault-user.response';

export const connectWalletDecorators = () => {
  return applyDecorators(
    Trace,
    ApiOperation({
      operationId: 'connectWallet',
      description: 'Connects a wallet to the user account',
      summary: 'connect wallet',
    }),
    ApiKey(),
    ...ApiResponses([ConnectWalletResponse]),
  );
};

export const getUserPortfolioStatsDecorators = () => {
  return applyDecorators(
    Trace,
    ApiOperation({
      operationId: 'getUserPortfolioStats',
      description:
        'Returns the portfolio stats of a user accross all the RoboNet vaults',
      summary: 'get user portfolio stats',
    }),
    ApiKey(),
    ApiParam({
      name: 'user_address',
      type: String,
      description: 'Address of the user',
    }),
    ...ApiResponses([UserVaultsPortfolioStatsResponse]),
  );
};

export const getUserVaultActivityDecorators = () => {
  return applyDecorators(
    Trace,
    ApiOperation({
      operationId: 'getUserVaultActivity',
      description:
        'Returns a list representing the activity logs of a user on a specific vault',
      summary: 'user vault activity',
    }),
    ApiKey(),
    ApiParam({
      name: 'user_address',
      type: String,
      description: 'Address of the user',
    }),
    ApiParam({
      name: 'vault_address',
      type: String,
      description: 'address of the vault',
    }),
    ApiQuery({
      name: 'limit',
      type: Number,
      description: `the maximum page size. default: \`${DEFAULT_LIMIT}\``,
      required: false,
    }),
    ApiQuery({
      name: 'include_count',
      type: Boolean,
      description: INCLUDE_COUNT_DESCRIPTION,
      required: false,
    }),
    ContinuationTokenDocs(),
    ...ApiResponses([UserVaultActivityResponse]),
  );
};

export const getUserVaultLiquidityPosition = () => {
  return applyDecorators(
    Trace,
    ApiOperation({
      operationId: 'getUserVaultLiquidityPosition',
      description:
        'Returns the current liquidity position of a user in a vault',
      summary: 'user vault liquidity position',
    }),
    ApiKey(),
    ApiParam({
      name: 'user_address',
      type: String,
      description: 'Address of the user',
    }),
    ApiParam({
      name: 'vault_address',
      type: String,
      description: 'address of the vault',
    }),
    ...ApiResponses([VaultLiquidityPositionResponse]),
  );
};
