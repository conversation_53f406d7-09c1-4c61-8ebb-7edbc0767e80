import { ApiProperty } from '@nestjs/swagger';
import { IsInt, IsObject, IsOptional, IsString } from 'class-validator';

import { DEFAULT_LIMIT, ToBoolean } from '../../../../common/dto';
import { CursorPaginated, Limit } from '../../../../common/dto/Paginated';

export class ConnectWalletDTO {
  @IsString()
  wallet_address: string;

  @IsString()
  wallet_id: string;

  @IsObject()
  metadata: Record<string, any>;
}

export class UserParamDTO {
  @IsString()
  user_address: string;
}

export class UserVaultActivityParamDTO {
  @IsString()
  user_address: string;

  @IsString()
  vault_address: string;
}

export class UserVaultActivityQueryDTO extends CursorPaginated {
  @IsOptional()
  @IsInt()
  @Limit({ default: DEFAULT_LIMIT })
  limit?: number;

  @IsOptional()
  @ApiProperty({ default: false })
  @ToBoolean()
  include_count: boolean;
}

export class UserVaultLiquidityPositionParamDTO {
  @IsString()
  user_address: string;

  @IsString()
  vault_address: string;
}
