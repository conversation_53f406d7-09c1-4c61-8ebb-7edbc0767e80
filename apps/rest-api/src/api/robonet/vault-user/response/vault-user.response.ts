import { VaultActivityOutput } from '@app/core/app/use-case/robonet/vault-user/dto/vault-activity.output';
import { ApiProperty } from '@nestjs/swagger';

export class ConnectWalletResponse {
  @ApiProperty({
    type: String,
    description: 'The allora user ID associated with the connected wallet',
  })
  user_id: string;

  constructor(connectWalletOutput: any) {
    this.user_id = connectWalletOutput.user_id;
  }
}

export class UserVaultsPortfolioStatsResponse {
  @ApiProperty({
    type: String,
    description: "User's total RoboNet vaults shares value denominated in ETH",
  })
  portfolio_balance_eth: string;

  @ApiProperty({
    type: String,
    description: "User's total vaults deposits denominated in ETH",
  })
  total_deposited_eth: string;

  @ApiProperty({
    type: String,
    description: "User's total vaults unrealized returns in percentage",
  })
  total_returns_perc: string;

  // TODO: clarify the actual type of portfolio output when implementing the actual logic
  constructor(portfolioOutput: any) {
    this.portfolio_balance_eth = portfolioOutput.portfolioBalanceEth;
    this.total_deposited_eth = portfolioOutput.totalDepositedEth;
    this.total_returns_perc = portfolioOutput.totalReturnsPercentage;
  }
}

export class UserVaultActivityResponse {
  @ApiProperty({
    type: String,
    description: 'Vault smart contract address',
  })
  vault_address: string;

  @ApiProperty({
    type: Number,
    description: 'Chain ID',
  })
  chain_id: number;

  @ApiProperty({
    type: String,
    description: 'Type of vault user activity',
  })
  action_type: string;

  @ApiProperty({
    type: String,
    description: 'Transaction hash of the activity',
  })
  transaction_hash: string;

  @ApiProperty({
    type: String,
    description: 'Address of the user that triggered the transaction',
  })
  user_address: string;

  @ApiProperty({
    type: String,
    description: 'ERC20 amount',
  })
  tokens_amount: string;

  @ApiProperty({
    type: String,
    description: 'Shares amount',
  })
  shares_amount: string;

  @ApiProperty({
    type: String,
    description: 'Block timestamp of the specified transaction',
  })
  block_timestamp: number;

  constructor(activityOutput: VaultActivityOutput) {
    this.vault_address = activityOutput.vaultAddress;
    this.action_type = activityOutput.actionType;
    this.transaction_hash = activityOutput.txHash;
    this.user_address = activityOutput.userAddress;
    this.tokens_amount = activityOutput.erc20Amount.value.toString();
    this.shares_amount = activityOutput.sharesAmount.value.toString();
    this.block_timestamp = activityOutput.blockTimestamp;
  }
}
