import { ConnectUserWalletQuery } from '@app/core/app/use-case/robonet/vault-user/query/connect-user-wallet/connect-user-wallet.query';
import { GetUserVaultActivityQuery } from '@app/core/app/use-case/robonet/vault-user/query/get-user-vault-activity/get-user-vault-activity.query';
import { GetUserVaultLiquidityPositionQuery } from '@app/core/app/use-case/robonet/vault-user/query/get-user-vault-liquidity-position/get-user-vault-liquidity-position.query';
import {
  Body,
  Controller,
  Get,
  NotFoundException,
  Param,
  Post,
  Query,
} from '@nestjs/common';
import { QueryBus } from '@nestjs/cqrs';
import { ApiTags } from '@nestjs/swagger';

import { VaultLiquidityPositionResponse } from '../vault/response/vault.response';
import * as DTO from './dto/vault-user.dto';
import { UserVaultActivityResponse } from './response/vault-user.response';
import * as Docs from './vault-user.docs';

@Controller('vault-user')
@ApiTags('Robonet Vault User')
export class VaultUserController {
  constructor(private readonly queryBus: QueryBus) {}

  // related to the Binance points campaign for allora
  @Post('connect-wallet')
  @Docs.connectWalletDecorators()
  async connectWallet(@Body() body: DTO.ConnectWalletDTO) {
    await this.queryBus.execute(new ConnectUserWalletQuery(body));
  }

  @Get(':user_address/portfolio-stats')
  @Docs.getUserPortfolioStatsDecorators()
  async getUserPortfolioStats(@Param() param: DTO.UserParamDTO) {
    return {
      total_deposited_eth: '15400000000000000000',
      total_deposited_usd: 54894.07,
      total_returns_percentage: 22.44,
      estimated_apr: 9.27,
    };
  }

  @Get(':user_address/vaults/:vault_address/activity')
  @Docs.getUserVaultActivityDecorators()
  async getUserVaultActivity(
    @Param() param: DTO.UserVaultActivityParamDTO,
    @Query() query: DTO.UserVaultActivityQueryDTO,
  ) {
    const res = await this.queryBus.execute(
      new GetUserVaultActivityQuery(param, query),
    );

    return {
      count: res.count,
      activity: res.activity.map(
        (activityLog) => new UserVaultActivityResponse(activityLog),
      ),
      continuation_token: res.continuationToken,
    };
  }

  @Get(':user_address/vaults/:vault_address/liquidity-position')
  @Docs.getUserVaultLiquidityPosition()
  async getUserVaultLiquidityPosition(
    @Param() param: DTO.UserVaultLiquidityPositionParamDTO,
  ) {
    const liquidityPosition = await this.queryBus.execute(
      new GetUserVaultLiquidityPositionQuery(param),
    );

    if (!liquidityPosition) {
      throw new NotFoundException('No liquidity position found!');
    }

    return new VaultLiquidityPositionResponse(liquidityPosition);
  }
}
