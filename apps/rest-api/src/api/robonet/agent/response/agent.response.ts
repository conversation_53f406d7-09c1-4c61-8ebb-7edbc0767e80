import { AgentEntry } from '@app/core/domain/robonet/agent/agent';
import { ApiProperty } from '@nestjs/swagger';

export class AgentResponse {
  @ApiProperty({
    type: String,
    description: "Address of the agent's smart contract",
    example: '0x26875d37daa90af4bfe2ab68f069e0c98abdcaff',
  })
  id: number;

  @ApiProperty({
    type: String,
    description: 'Display name of the agent',
    example: '$NICK Agent',
  })
  name: string;

  @ApiProperty({
    type: String,
    description: 'Description of the agent',
    nullable: true,
  })
  description: string | null;

  @ApiProperty({
    type: String,
    description: 'Display image of the agent',
    nullable: true,
  })
  image_url: string | null;

  @ApiProperty({
    type: String,
    description: 'Network the agent is deployed on',
    example: 'ethereum',
  })
  network: string;

  @ApiProperty({
    type: String,
    description: 'Id of the chain the agent smart contract is deployed on',
  })
  chain_id: number;

  @ApiProperty({
    type: String,
    description: "Address of the agent's wallet",
  })
  wallet_address: string;

  @ApiProperty({
    type: String,
    description: "Address of the agent's token",
  })
  token_address: string;

  @ApiProperty({
    type: Date,
    description: 'Date and time the agent was created',
  })
  created_at: Date;

  @ApiProperty({
    type: Date,
    description: 'Date and time the agent was updated',
  })
  updated_at: Date;

  constructor(source: AgentEntry) {
    this.id = source.id;
    this.name = source.name;
    this.description = source.description;
    this.image_url = source.imageUrl;
    this.chain_id = source.chainId;
    this.network = source.network;
    this.wallet_address = source.walletAddress;
    this.token_address = source.tokenAddress;
    this.created_at = source.createdAt;
    this.updated_at = source.updatedAt;
  }
}
