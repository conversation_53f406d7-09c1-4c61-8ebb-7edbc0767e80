import { AGENT_QUERY_REPOSITORY } from '@app/core/domain/robonet/agent/agent.query.repository';
import { AgentQueryRepository } from '@app/core/domain/robonet/agent/agent.query.repository';
import {
  Controller,
  Get,
  Inject,
  NotFoundException,
  Param,
  Query,
} from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';

import * as Docs from './agent.docs';
import * as DTO from './dto/agent.dto';
import { AgentResponse } from './response/agent.response';

@Controller('/agents')
@ApiTags('Robonet Agent')
export class AgentController {
  constructor(
    @Inject(AGENT_QUERY_REPOSITORY)
    private agentQueryRepository: AgentQueryRepository,
  ) {}

  @Get()
  @Docs.getAllAgentsDecorators()
  async getAllAgents(@Query() query: DTO.GetAllAgentsQueryDTO) {
    const response = await this.agentQueryRepository.getAll(query);

    return {
      agents: response.agents.map((agent) => new AgentResponse(agent)),
      continuation_token: response.continuationToken?.value ?? null,
    };
  }

  @Get(':agent_id')
  @Docs.getAgentDecorators()
  async getAgent(@Param() param: DTO.AgentParamDTO) {
    const agent = await this.agentQueryRepository.getById(
      Number(param.agent_id),
    );

    if (!agent) {
      throw new NotFoundException('Agent not found');
    }

    return new AgentResponse(agent);
  }
}
