import { Trace } from '@app/core/infra/utils/tracing.decorator';
import { applyDecorators } from '@nestjs/common';
import { ApiOperation, ApiParam, ApiQuery } from '@nestjs/swagger';

import { <PERSON>pi<PERSON>ey } from '../../../common/decorator/api-key.decorator';
import { ApiResponses } from '../../../common/decorator/api-responses.decorator';
import { ContinuationTokenDocs } from '../../../common/decorator/continuation-token.decorator';
import { DEFAULT_LIMIT } from '../../../common/dto';
import { AgentResponse } from './response/agent.response';

export const getAllAgentsDecorators = () => {
  return applyDecorators(
    Trace,
    ApiOperation({
      operationId: 'getAllAgents',
      description: 'Returns a list of RoboNet agents',
      summary: 'get all agents',
    }),
    ApiKey(),
    ApiQuery({
      name: 'limit',
      type: Number,
      description: `the maximum page size. default: \`${DEFAULT_LIMIT}\``,
      required: false,
    }),
    ContinuationTokenDocs(),
    ...ApiResponses([AgentResponse]),
  );
};

export const getAgentDecorators = () => {
  return applyDecorators(
    Trace,
    ApiOperation({
      operationId: 'getAgent',
      description: 'Returns a RoboNet agent by id',
      summary: 'get agent',
    }),
    ApiKey(),
    ApiParam({
      name: 'agent_id',
      type: Number,
      description: 'id of the agent',
    }),
    ...ApiResponses([AgentResponse]),
  );
};
