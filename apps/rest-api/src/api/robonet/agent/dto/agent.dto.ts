import { IsInt, IsOptional, IsString } from 'class-validator';

import { DEFAULT_LIMIT } from '../../../../common/dto';
import { CursorPaginated, Limit } from '../../../../common/dto/Paginated';

export class GetAllAgentsQueryDTO extends CursorPaginated {
  @IsOptional()
  @IsInt()
  @Limit({ default: DEFAULT_LIMIT })
  limit?: number;
}

export class AgentParamDTO {
  @IsString()
  agent_id: string;
}
