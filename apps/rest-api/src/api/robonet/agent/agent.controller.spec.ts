import { AGENT_QUERY_REPOSITORY } from '@app/core/domain/robonet/agent/agent.query.repository';
import { Test, TestingModule } from '@nestjs/testing';

import { AgentController } from './agent.controller';

describe('AgentController', () => {
  let controller: AgentController;

  const mockAgentQueryRepository = {
    getAll: jest.fn(),
    getById: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AgentController,
        { provide: AGENT_QUERY_REPOSITORY, useValue: mockAgentQueryRepository },
      ],
    }).compile();

    controller = module.get<AgentController>(AgentController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
});
