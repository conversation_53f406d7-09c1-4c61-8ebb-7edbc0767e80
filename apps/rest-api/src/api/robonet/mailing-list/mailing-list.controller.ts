import { BadRequestException, Body, Controller, Post } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { Sequelize } from 'sequelize-typescript';

import * as DTO from './dto/mailing-list.dto';

@Controller('/mailing-list')
@ApiTags('Robonet MailingList')
export class MailingListController {
  constructor(private readonly sequelize: Sequelize) {}

  @Post('sign-up')
  async signup(@Body() body: DTO.MailingListDTO) {
    // Required field check
    if (!body.email) {
      throw new BadRequestException('Missing required fields: email');
    }

    const now = new Date().toISOString();

    const query = `
      INSERT INTO robonet_mailing_list (name, email, created_at, updated_at)
      VALUES ('${body.name}', '${body.email}', '${now}', '${now}')
      ON CONFLICT (email) DO UPDATE
      SET
        updated_at = '${now}',
        name = '${body.name}'
      RETURNING *`;

    const res = await this.sequelize.query(query);

    if (res) {
      return res;
    } else {
      throw new BadRequestException('Failed to sign up');
    }
  }
}
