import { GetVaultQuery } from '@app/core/app/use-case/robonet/vault/query/get-vault/get-vault.query';
import { SearchVaultsQuery } from '@app/core/app/use-case/robonet/vault/query/search-vaults/search-vaults.query';
import {
  VAULT_QUERY_REPOSITORY,
  VaultQueryRepository,
} from '@app/core/domain/robonet/vault/vault.query.repository';
import {
  Controller,
  Get,
  Inject,
  NotFoundException,
  Param,
  Query,
} from '@nestjs/common';
import { QueryBus } from '@nestjs/cqrs';
import { ApiTags } from '@nestjs/swagger';

import * as DTO from './dto/vault.dto';
import {
  VaultLiquidityPositionResponse,
  VaultResponse,
  VaultsGeneralStatsResponse,
} from './response/vault.response';
import * as Docs from './vault.docs';

@Controller('/vaults')
@ApiTags('Robonet Vault')
export class VaultController {
  constructor(
    private readonly queryBus: QueryBus,
    @Inject(VAULT_QUERY_REPOSITORY)
    private vaultQueryRepository: VaultQueryRepository,
  ) {}

  @Get()
  @Docs.searchVaultsDecorators()
  async searchVaults(@Query() query: DTO.SearchVaultsQueryDTO) {
    const response = await this.queryBus.execute(new SearchVaultsQuery(query));

    return {
      count: response.count,
      vaults: response.vaults.map((vault) => new VaultResponse(vault)),
      liquidity_positions: response.liquidityPositions.map(
        (lp) => new VaultLiquidityPositionResponse(lp),
      ),
      continuation_token: response.continuationToken ?? null,
    };
  }

  @Get('/stats')
  @Docs.getVaultsGeneralStatsDecorators()
  async getVaultsGeneralStats() {
    const stats = await this.vaultQueryRepository.getVaultsGeneralStats();

    return new VaultsGeneralStatsResponse(stats);
  }

  @Get(':vault_address')
  @Docs.getVaultDecorators()
  async getVault(@Param() param: DTO.VaultParamDTO) {
    const vault = await this.queryBus.execute(
      new GetVaultQuery(param.vault_address),
    );

    if (!vault) {
      throw new NotFoundException('Vault not found');
    }

    return new VaultResponse(vault);
  }
}
