import { VAULT_QUERY_REPOSITORY } from '@app/core/domain/robonet/vault/vault.query.repository';
import { QueryBus } from '@nestjs/cqrs';
import { Test, TestingModule } from '@nestjs/testing';

import { VaultController } from './vault.controller';

describe('VaultController', () => {
  let controller: VaultController;

  const mockQueryBus = { execute: jest.fn() };

  const mockVaultQueryReposity = {
    getByIdsOrSlugs: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        VaultController,
        { provide: QueryBus, useValue: mockQueryBus },
        { provide: VAULT_QUERY_REPOSITORY, useValue: mockVaultQueryReposity },
      ],
    }).compile();

    controller = module.get<VaultController>(VaultController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
});
