import { ContinuationToken } from '@app/core/domain/continuation-token/continuation-token';
import { VaultFilters } from '@app/core/domain/robonet/vault/vault.filter';
import { VaultsPagination } from '@app/core/domain/robonet/vault/vault.sort';
import { Address } from '@app/core/domain/value-object/address.evm';
import { DEFAULT_LIMIT, MAX_LIMIT } from 'apps/rest-api/src/common/dto';

import { SearchVaultsQueryDTO } from './dto/vault.dto';

export interface ISearchVaultsQueryArgs {
  pagination: VaultsPagination;
  filters: VaultFilters;
  includeCount: boolean;
  walletAddress?: Address;
}

export const SearchVaultsQueryDTOFormFaulter = (
  dto: SearchVaultsQueryDTO,
): ISearchVaultsQueryArgs => {
  return {
    pagination: {
      limit: Math.min(dto.limit ?? DEFAULT_LIMIT, MAX_LIMIT),
      continuationToken: dto.continuation_token
        ? new ContinuationToken(dto.continuation_token)
        : undefined,
    },
    filters: {
      nameOrAddress: dto.search_term
        ? dto.search_term.toLowerCase()
        : undefined,
      chainIds: dto.chain_ids?.length ? dto.chain_ids : undefined,
      vaultType: dto.vault_type ? dto.vault_type.toLowerCase() : undefined,
    },
    includeCount: dto.include_count ?? false,
    walletAddress: dto.wallet_address
      ? new Address(dto.wallet_address)
      : undefined,
  };
};
