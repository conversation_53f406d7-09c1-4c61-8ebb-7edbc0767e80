import { EVaultType } from '@app/core/domain/robonet/vault/vault';
import { Address } from '@app/core/domain/value-object/address.evm';
import { Trace } from '@app/core/infra/utils/tracing.decorator';
import { applyDecorators } from '@nestjs/common';
import { ApiOperation, ApiParam, ApiQuery } from '@nestjs/swagger';

import { ApiKey } from '../../../common/decorator/api-key.decorator';
import { ApiResponses } from '../../../common/decorator/api-responses.decorator';
import { ContinuationTokenDocs } from '../../../common/decorator/continuation-token.decorator';
import { DEFAULT_LIMIT, INCLUDE_COUNT_DESCRIPTION } from '../../../common/dto';
import {
  VaultResponse,
  VaultsGeneralStatsResponse,
} from './response/vault.response';

export const searchVaultsDecorators = () => {
  return applyDecorators(
    Trace,
    ApiOperation({
      operationId: 'searchVaults',
      description: 'Returns a list of RoboNet vaults',
      summary: 'search vaults',
    }),
    ApiKey(),
    ApiQuery({
      name: 'search_term',
      type: String,
      description: 'search term to filter vaults by name or address',
      required: false,
    }),
    ApiQuery({
      name: 'chain_ids',
      description: 'only include vaults deployed on the specified chains',
      required: false,
      schema: {
        type: 'array',
        items: {
          type: 'number',
          example: '********',
        },
      },
    }),
    ApiQuery({
      name: 'vault_type',
      enum: EVaultType,
      required: false,
      description: 'only include vaults of the specified type',
      example: 'DEFI',
    }),
    ApiQuery({
      name: 'wallet_address',
      type: Address,
      description:
        "If provided, only include account's liquidity positions in the vaults",
      required: false,
    }),
    ApiQuery({
      name: 'limit',
      type: Number,
      description: `the maximum page size. default: \`${DEFAULT_LIMIT}\``,
      required: false,
    }),
    ApiQuery({
      name: 'include_count',
      type: Boolean,
      description: INCLUDE_COUNT_DESCRIPTION,
      required: false,
    }),
    ContinuationTokenDocs(),
    ...ApiResponses([VaultResponse]),
  );
};

export const getVaultDecorators = () => {
  return applyDecorators(
    Trace,
    ApiOperation({
      operationId: 'getVault',
      description: 'Returns a RoboNet vault by address',
      summary: 'get vault',
    }),
    ApiKey(),
    ApiParam({
      name: 'vault_address',
      type: String,
      description: 'address of the vault on the specified chain',
    }),
    ...ApiResponses([VaultResponse]),
  );
};

export const getVaultsGeneralStatsDecorators = () => {
  return applyDecorators(
    Trace,
    ApiOperation({
      operationId: 'getVaultsStats',
      description: 'Return stats for all the vaults',
      summary: 'get vaults stats',
    }),
    ...ApiResponses([VaultsGeneralStatsResponse]),
  );
};
