import { VaultLiquidityStatsOutput } from '@app/core/app/use-case/robonet/vault/dto/vault-liquidity-stats.output';
import { VaultOutput } from '@app/core/app/use-case/robonet/vault/dto/vault-output';
import { VaultsGeneralStats } from '@app/core/domain/robonet/vault/vault';
import { VaultLiquidityPosition } from '@app/core/domain/robonet/vault-activity/vault-liquidity-position';
import { ApiProperty } from '@nestjs/swagger';

class VaultLiquidityStatsResponse {
  @ApiProperty({
    type: Number,
    description: 'The daily percentage change of the vault',
  })
  daily_change: number;

  @ApiProperty({
    type: Number,
    description: 'The weekly percentage change of the vault',
  })
  weekly_change: number;

  @ApiProperty({
    type: Number,
    description: 'The monthly percentage change of the vault',
  })
  monthly_change: number;

  @ApiProperty({
    type: Number,
    description: 'The inception percentage change of the vault',
  })
  inception_change: number;

  @ApiProperty({
    type: Number,
    description: 'The current daily APR of the vault',
  })
  daily_apr: number;

  @ApiProperty({
    type: Number,
    description: 'The current weekly APR of the vault',
  })
  weekly_apr: number;

  @ApiProperty({
    type: Number,
    description: 'The 30d APR of the vault',
  })
  monthly_apr: number;

  @ApiProperty({
    type: Number,
    description: 'The historical APR of the vault since inception',
  })
  inception_apr: number;

  @ApiProperty({
    type: Number,
    description:
      "The total deposits in the vault denominated in the vault's underlying token",
  })
  total_deposits_wei: string;

  @ApiProperty({
    type: Number,
    description:
      "The total debt of the vault denominated in the vault's underlying token",
  })
  total_debt_wei: string;

  @ApiProperty({
    type: Number,
    description:
      "The total idle funds of the vault denominated in the vault's underlying token",
  })
  total_idle_wei: string;

  @ApiProperty({
    type: Number,
    description: 'Total value locked in the vault denominated in USD',
  })
  tvl_usd: number;

  constructor(source: VaultLiquidityStatsOutput) {
    this.daily_change = source.dailyChange;
    this.weekly_change = source.weeklyChange;
    this.monthly_change = source.monthlyChange;
    this.inception_change = source.inceptionChange;
    this.inception_apr = source.inceptionApr;
    this.daily_apr = source.dailyApr;
    this.weekly_apr = source.weeklyApr;
    this.monthly_apr = source.monthlyApr;
    this.total_deposits_wei = source.totalDepositsWei.value.toString();
    this.total_debt_wei = source.totalDebtWei.value.toString();
    this.total_idle_wei = source.totalIdleWei.value.toString();
    this.tvl_usd = source.tvlUsd;
  }
}

export class VaultResponse {
  @ApiProperty({
    type: String,
    description: "Address of the vault's smart contract",
    example: '******************************************',
  })
  address: string;

  @ApiProperty({
    type: String,
    description: 'Display name of the vault',
    nullable: true,
    example: 'WETH Perp Vault',
  })
  name: string | null;

  @ApiProperty({
    type: String,
    description: 'Description of the vault',
    nullable: true,
  })
  description: string | null;

  @ApiProperty({
    type: String,
    description: 'Display image of the vault',
  })
  vault_image_url: string | null;

  @ApiProperty({
    type: String,
    description: 'Type of the vault',
  })
  vault_type: string;

  @ApiProperty({
    type: String,
    description: 'Address of the token used by the vault',
  })
  token_address: string;

  @ApiProperty({
    type: String,
    description: 'Name of the token used by the vault',
    nullable: true,
  })
  token_name: string | null;

  @ApiProperty({
    type: String,
    description: 'Logo image of the token used by the vault',
    nullable: true,
  })
  token_image_url: string | null;

  @ApiProperty({
    type: String,
    description: 'Symbol of the token used by the vault',
    nullable: true,
  })
  token_symbol: string | null;

  @ApiProperty({
    type: Number,
    description: 'Decimals of the token used by the vault',
    nullable: true,
  })
  token_decimals: number | null;

  @ApiProperty({
    type: String,
    description: 'Performance fee of the vault',
    nullable: true,
  })
  performance_fee: string | null;

  @ApiProperty({
    type: String,
    description: 'The transaction hash of the vault creation',
  })
  tx_hash: string;

  @ApiProperty({
    type: Number,
    description: 'The block timestamp of the vault creation',
  })
  block_timestamp: number;

  @ApiProperty({
    type: Number,
    description: 'Id of the chain the vault smart contract is deployed on',
  })
  chain_id: number;

  @ApiProperty({
    type: Boolean,
    description: 'Whether referral points are enabled for the vault',
  })
  points_enabled: boolean;

  @ApiProperty({
    type: Number,
    description: 'The multuplier for referral points for the vault',
  })
  points_multiplier: number;

  @ApiProperty({
    type: 'object',
    description: 'Overall stats of the vault',
    additionalProperties: true,
  })
  liquidity_stats: VaultLiquidityStatsResponse;

  constructor(source: VaultOutput) {
    this.address = source.address;
    this.name = source.name;
    this.description = source.description;
    this.vault_image_url = source.vaultImageUrl;
    this.vault_type = source.vaultType;
    this.token_address = source.tokenAddress;
    this.token_name = source.tokenName;
    this.token_image_url = source.tokenImageUrl;
    this.token_symbol = source.tokenSymbol;
    this.token_decimals = source.tokenDecimals;
    this.performance_fee = source.performanceFee;
    this.tx_hash = source.txHash;
    this.block_timestamp = source.blockTimestamp;
    this.chain_id = source.chainId;
    this.points_enabled = source.pointsEnabled;
    this.points_multiplier = source.pointsMultiplier;
    this.liquidity_stats = new VaultLiquidityStatsResponse(
      source.liqudityStats,
    );
  }
}

export class VaultLiquidityPositionResponse {
  @ApiProperty({
    type: String,
    description: 'ERC20 amount deposited in the vault by the specified user',
  })
  tokens_amount: string;
  @ApiProperty({
    type: String,
    description: 'Address of the user that owns the liquidity position',
  })
  owner_address: string;
  @ApiProperty({
    type: String,
    description:
      'Address of the vault that the user owns the liquidity position in',
  })
  vault_address: string;

  constructor(liquidityPositionOutput: VaultLiquidityPosition) {
    this.tokens_amount = liquidityPositionOutput.tokenAmount.value.toString();
    this.owner_address = liquidityPositionOutput.ownerAddress.value;
    this.vault_address = liquidityPositionOutput.vaultAddress.value;
  }
}
export class VaultsGeneralStatsResponse {
  @ApiProperty({
    type: String,
    description: 'ERC20 amount deposited in the vault by the specified user',
  })
  tvl_usd: number;

  constructor(stats: VaultsGeneralStats) {
    this.tvl_usd = stats.tvlUsd;
  }
}
