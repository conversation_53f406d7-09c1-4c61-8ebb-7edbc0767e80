import { GetVaultModule } from '@app/core/app/use-case/robonet/vault/query/get-vault/get-vault.module';
import { SearchVaultsModule } from '@app/core/app/use-case/robonet/vault/query/search-vaults/search-vaults.module';
import { VaultQueryRepositoryModule } from '@app/core/domain/robonet/vault/vault.query.repository.module';
import { Module } from '@nestjs/common';
import { CqrsModule } from '@nestjs/cqrs';

import { VaultController } from './vault.controller';

@Module({
  imports: [
    CqrsModule,
    VaultQueryRepositoryModule,
    SearchVaultsModule,
    GetVaultModule,
  ],
  controllers: [VaultController],
})
export class VaultModule {}
