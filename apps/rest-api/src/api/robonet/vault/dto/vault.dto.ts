import * as CustomValidators from '@app/core/app/validation/validators';
import { EVaultType } from '@app/core/domain/robonet/vault/vault';
import { Address } from '@app/core/domain/value-object/address.evm';
import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsInt, IsOptional, IsString, Validate } from 'class-validator';

import { DEFAULT_LIMIT, ToBoolean } from '../../../../common/dto';
import { CursorPaginated, Limit } from '../../../../common/dto/Paginated';

export class SearchVaultsQueryDTO extends CursorPaginated {
  @IsOptional()
  @IsString()
  search_term?: string;

  @IsOptional()
  @ApiProperty({
    type: String,
    isArray: true,
    required: false,
  })
  @IsString({ each: true })
  @Validate(CustomValidators.NumericString, { each: true })
  chain_ids?: string[];

  @IsOptional()
  @IsEnum(Object.values(EVaultType))
  vault_type?: EVaultType;

  @IsOptional()
  @IsInt()
  @Limit({ default: DEFAULT_LIMIT })
  limit?: number;

  @IsOptional()
  @ApiProperty({ default: false })
  @ToBoolean()
  include_count: boolean;

  @IsOptional()
  @IsString()
  @Validate(Address)
  wallet_address: string;
}

export class VaultParamDTO {
  @IsString()
  vault_address: string;
}
