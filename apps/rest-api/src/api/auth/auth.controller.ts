import { Trace } from '@app/core/infra/utils/tracing.decorator';
import { Body, Controller, Logger, Post } from '@nestjs/common';
import { ApiOperation, ApiTags } from '@nestjs/swagger';

import { HideControllerOnlyInProd } from '../../common/decorator';
import { CreateNonceDTO, LoginDTO } from './auth.dtos';
import { AuthService } from './auth.service';

@Controller('/auth')
@ApiTags('Auth Endpoints')
@HideControllerOnlyInProd()
export class AuthController {
  private readonly logger = new Logger(AuthController.name);

  constructor(private readonly authService: AuthService) {}

  @Post('/login')
  @ApiOperation({ operationId: 'login' })
  @Trace
  async login(@Body() loginDTO: LoginDTO) {
    this.logger.log('auth/login !');
    return this.authService.login(loginDTO);
  }

  @Post('/nonce')
  @ApiOperation({ operationId: 'nonce' })
  @Trace
  async nonce(@Body() nonceDTO: CreateNonceDTO) {
    this.logger.log('auth/nonce !');
    return this.authService.getNonce(nonceDTO.address);
  }
}
