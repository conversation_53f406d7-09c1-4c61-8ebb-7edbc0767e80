import { OverageEmailModule } from '@app/core/app/use-case/api/command/overage-email/overage-email.module';
import { VerifyApiRequestUseCaseModule } from '@app/core/app/use-case/api/verify.api-request.use-case.module';
import { GetProxyUserUseCaseModule } from '@app/core/app/use-case/api-user/get.proxy-user.use-case.module';
import { ENodeEnv, NODE_ENV } from '@app/core/config/env';
import { RequestByEndpointRepositoryModule } from '@app/core/domain/api/request-by-endpoint/request-by-endpoint.repository.module';
import { ApiUserRepositoryModule } from '@app/core/domain/api/user/api-user.repository.module';
import { DomainEventAggregatorModule } from '@app/core/domain/domain-event/domain-event.aggregator.module';
import { UserRepositoryModule } from '@app/core/domain/user/user.repository.module';
import { RedisModule } from '@app/core/infra/redis/redis.module';
import { Module } from '@nestjs/common';
import { APP_GUARD, APP_INTERCEPTOR } from '@nestjs/core';
import { JwtModule } from '@nestjs/jwt';
import { SequelizeModule } from '@nestjs/sequelize';

import { JWT_EXPIRES, JWT_KEY } from '../../config';
import { UserAddress } from '../../model';
import { APITransactionInterceptor } from './apiTransaction.interceptor';
import { AuthController } from './auth.controller';
import { AuthService } from './auth.service';
import { RouteService } from './route.service';
import { TestAuthController } from './test-auth.controller';
import { UpshotAuthGuard } from './upshot-auth.guard';

let controllers: any[] = [AuthController];
if ([ENodeEnv.TEST, ENodeEnv.DEV, ENodeEnv.STAGE].includes(NODE_ENV)) {
  controllers = controllers.concat(TestAuthController);
}

@Module({
  imports: [
    RedisModule,
    SequelizeModule.forFeature([UserAddress]),
    JwtModule.register({
      secret: JWT_KEY,
      signOptions: { expiresIn: JWT_EXPIRES },
    }),
    // TODO refactor out repostory modules
    ApiUserRepositoryModule,
    UserRepositoryModule,
    VerifyApiRequestUseCaseModule,
    OverageEmailModule,
    GetProxyUserUseCaseModule,
    DomainEventAggregatorModule,
    RequestByEndpointRepositoryModule,
  ],
  providers: [
    AuthService,
    UpshotAuthGuard,
    {
      provide: APP_GUARD,
      useClass: UpshotAuthGuard,
    },
    {
      provide: APP_INTERCEPTOR,
      useClass: APITransactionInterceptor,
    },
    RouteService,
  ],
  exports: [
    // used by alert module on the gateway, can remove after pusher refactor
    AuthService,
    RouteService,
  ],
  controllers,
})
export class AuthModule {}
