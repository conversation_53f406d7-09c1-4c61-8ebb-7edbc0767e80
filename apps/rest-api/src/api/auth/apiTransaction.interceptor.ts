import { OverageEmailCommand } from '@app/core/app/use-case/api/command/overage-email/overage-email.command';
import { OverageEmailUseCase } from '@app/core/app/use-case/api/command/overage-email/overage-email.use-case';
import { VerifyApiRequestUseCase } from '@app/core/app/use-case/api/verify.api-request.use-case';
import { BYPASS_AUTH } from '@app/core/config/env';
import { NewApiRequestEvent } from '@app/core/domain/api/domain-event/new-api-request.event';
import {
  REQUEST_BY_ENDPOINT_REPOSITORY,
  RequestByEndpointRepository,
} from '@app/core/domain/api/request-by-endpoint/request-by-endpoint.repository';
import { ApiUser } from '@app/core/domain/api/user/api-user';
import { DomainEventAggregator } from '@app/core/domain/domain-event/domain-event-aggregator';
import { TraceClass } from '@app/core/infra/utils/tracing.decorator';
import {
  CallHand<PERSON>,
  ExecutionContext,
  Inject,
  Injectable,
  Logger,
  NestInterceptor,
} from '@nestjs/common';
import { Observable } from 'rxjs';
import { tap } from 'rxjs/operators';

import { RouteService } from './route.service';

@TraceClass
@Injectable()
export class APITransactionInterceptor implements NestInterceptor {
  private readonly logger = new Logger(APITransactionInterceptor.name);

  constructor(
    private readonly routeService: RouteService,
    private readonly verifyApiKeyUseCase: VerifyApiRequestUseCase,
    private readonly domainEventAggregator: DomainEventAggregator,
    private readonly overageEmailUseCase: OverageEmailUseCase,
    @Inject(REQUEST_BY_ENDPOINT_REPOSITORY)
    private readonly requestByEndpointRepository: RequestByEndpointRepository,
  ) {}

  async intercept(
    context: ExecutionContext,
    next: CallHandler,
  ): Promise<Observable<any>> {
    const request = context.switchToHttp().getRequest();

    const user: ApiUser = request.apiUser;
    const endpoint: string = request.route.path;

    if (this.routeService.isRoutePublic(context) || BYPASS_AUTH) {
      this.logger.log(
        `SKIP authorization to ${endpoint}: route is public or BYPASS_AUTH is true`,
      );
      return next.handle();
    }

    await this.verifyApiKeyUseCase.execute(user, endpoint);

    this.logger.log(
      `AUTHORIZED endpoint ${endpoint} access by user ${user.id}`,
    );

    await this.overageEmailUseCase.execute(new OverageEmailCommand(user));

    try {
      await this.requestByEndpointRepository.incrementByApiUserIdAndEndpoint(
        user.id,
        endpoint,
      );
    } catch (error) {
      this.logger.warn(
        `Unable to increment api_requests_by_endpoint for user ${user.id} and endpoint ${endpoint}; skip → ${error}`,
      );
    }

    return next.handle().pipe(
      tap(async (response) => {
        const event: NewApiRequestEvent = NewApiRequestEvent.create({
          apiUserId: user.id.value,
          apiTierId: user.tier.id.value,
          apiKey: request.headers['x-api-key'],
          method: request.method,
          rawUrl: request.url,
          baseUrl: endpoint,
          ip: request.realIp,
        });
        this.domainEventAggregator.raise(event);
      }),
    );
  }
}
