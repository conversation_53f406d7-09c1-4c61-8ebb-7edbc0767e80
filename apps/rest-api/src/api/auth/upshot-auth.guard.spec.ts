import { GetProxyUserUseCase } from '@app/core/app/use-case/api-user/get-proxy-user.use-case';
import { ApiUser } from '@app/core/domain/api/user/api-user';
import { ApiUserId } from '@app/core/domain/api/user/api-user.id';
import { API_USER_REPOSITORY } from '@app/core/domain/api/user/api-user.repository';
import {
  FlatUserFactory,
  User,
  USER_REPOSITORY,
  UserId,
} from '@app/core/domain/user';
import { HashedPassword } from '@app/core/domain/value-object/hashed-password';
import { REDIS_IDEMPOTENCY_SERVICE } from '@app/core/infra/redis/redis.module';
import { faker } from '@faker-js/faker';
import { UnauthorizedException } from '@nestjs/common';
import { Test } from '@nestjs/testing';
import { v4 } from 'uuid';

import { AuthService } from './auth.service';
import { RouteService } from './route.service';
import { UpshotAuthGuard } from './upshot-auth.guard';

describe('UpshotAuthGuard', () => {
  const flatUserFactory = new FlatUserFactory();
  const userId = new UserId(Math.ceil(Math.random() * 1000));
  const validatedUser = new User({
    id: userId,
    email: faker.internet.email(),
    createdAt: new Date(),
    isUpshotUser: false,
    updatedAt: new Date(),
    imageUrl: faker.internet.url(),
    nonce: v4(),
    username: faker.internet.userName(),
  });

  const proxyUserId = new UserId(Math.ceil(Math.random() * 1000));
  const proxyUser = new User({
    id: proxyUserId,
    email: faker.internet.email(),
    createdAt: new Date(),
    isUpshotUser: false,
    updatedAt: new Date(),
    imageUrl: faker.internet.url(),
    nonce: v4(),
    username: faker.internet.userName(),
  });

  const mockApiTier = {
    isInternal: jest.fn().mockReturnValue(true),
    hasMonthlyCap: jest.fn().mockReturnValue(false),
  };
  const apiUser = new ApiUser({
    id: new ApiUserId(v4()),
    email: faker.internet.email(),
    name: faker.internet.userName(),
    active: true,
    overageWarningAlerts: false,
    _overageRequestCap: 1000,
    apiKeys: [],
    tier: mockApiTier as any,
    billingDay: 0,
    createdAt: new Date(),
    password: new HashedPassword(v4()),
    refreshToken: null,
    googleId: null,
    paymentAccount: null,
    userId: validatedUser.id,
    oneTimeLoginToken: null,
    isEmailConfirmed: true,
    lastOverageEmailAtPercent: 0,
  });

  const mockRouteService = {
    isRoutePublic: jest.fn().mockReturnValue(false),
    isAuthenticated: jest.fn().mockReturnValue(true),
  };

  const mockApiUserRepository = {
    getByActiveApiKey: jest.fn().mockReturnValue(apiUser),
  };

  const mockAuthService = {
    validateUser: jest
      .fn()
      .mockReturnValue(flatUserFactory.toFlat(validatedUser)),
    decodeUser: jest
      .fn()
      .mockReturnValue(flatUserFactory.toFlat(validatedUser)),
  };

  const mockGetProxyUserUseCase = {
    execute: jest.fn().mockReturnValue(proxyUser),
  };

  const mockRequest = {
    headers: {
      'x-api-key': '123',
      authorization: 'Bearer 456',
    },
  };

  let guard: UpshotAuthGuard;

  const wrapRequest = (request: any): any => ({
    switchToHttp: () => ({ getRequest: () => request }),
  });

  beforeAll(async () => {
    const module = await Test.createTestingModule({
      providers: [
        UpshotAuthGuard,
        {
          provide: RouteService,
          useValue: mockRouteService,
        },
        {
          provide: API_USER_REPOSITORY,
          useValue: mockApiUserRepository,
        },
        {
          provide: AuthService,
          useValue: mockAuthService,
        },
        {
          provide: GetProxyUserUseCase,
          useValue: mockGetProxyUserUseCase,
        },
        {
          provide: REDIS_IDEMPOTENCY_SERVICE,
          useValue: {},
        },
        {
          provide: USER_REPOSITORY,
          useValue: {
            getUserById: (a: UserId) => {
              if (a.value === userId.value) {
                return validatedUser;
              }

              if (a.value === proxyUserId.value) {
                return proxyUser;
              }

              throw new Error('User not found');
            },
          },
        },
      ],
    }).compile();

    guard = module.get<UpshotAuthGuard>(UpshotAuthGuard);
  });

  beforeEach(() => {
    mockRouteService.isRoutePublic.mockReturnValue(false);
    mockRouteService.isAuthenticated.mockReturnValue(true);
    mockApiUserRepository.getByActiveApiKey.mockReturnValue(apiUser);
    mockAuthService.validateUser.mockReturnValue(validatedUser);
    mockGetProxyUserUseCase.execute.mockReturnValue(proxyUser);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('no api key', () => {
    const requestNoApiKey = {
      headers: {},
    };

    it('returns true if the route is public', async () => {
      mockRouteService.isRoutePublic.mockReturnValue(true);
      const result = await guard.canActivate(wrapRequest(requestNoApiKey));
      expect(result).toBeTruthy();
    });

    it('throws if the route is not public', async () => {
      try {
        await guard.canActivate(wrapRequest(requestNoApiKey));
      } catch (e) {
        expect(e).toBeInstanceOf(UnauthorizedException);
        expect(e.message).toBe('No API key found');
      }
    });
  });

  describe('no api user', () => {
    it('throws', async () => {
      mockApiUserRepository.getByActiveApiKey.mockReturnValue(null);

      try {
        await guard.canActivate(wrapRequest(mockRequest));
      } catch (e) {
        expect(e).toBeInstanceOf(UnauthorizedException);
        expect(e.message).toBe('No API user found for API key');
      }
    });
  });

  describe('other api user', () => {
    let requestOtherUser;
    let wrappedRequest;

    beforeEach(() => {
      requestOtherUser = {
        headers: {
          'x-api-key': '123',
        },
      };
      wrappedRequest = wrapRequest(requestOtherUser);
      mockApiTier.isInternal.mockReturnValue(false);
    });

    it('returns true if the route is public', async () => {
      mockRouteService.isRoutePublic.mockReturnValue(true);
      const result = await guard.canActivate(wrappedRequest);
      expect(result).toBeTruthy();
    });

    describe('non public route', () => {
      it('returns true', async () => {
        expect(await guard.canActivate(wrappedRequest)).toBeTruthy();
      });

      it('attaches proxy the user to the request', async () => {
        const result = await guard.canActivate(wrappedRequest);
        expect(result).toBeTruthy();
        expect(requestOtherUser.user).toBe(proxyUser);
      });

      it('skips the proxy user if it fails to load, on non authenticated routes', async () => {
        mockRouteService.isAuthenticated.mockReturnValue(false);
        mockGetProxyUserUseCase.execute.mockImplementationOnce(() => {
          throw new Error('User not found');
        });

        const result = await guard.canActivate(wrappedRequest);
        expect(result).toBeTruthy();
        expect(requestOtherUser.user).toBe(null);
      });

      it('attaches the api user to the request', async () => {
        const result = await guard.canActivate(wrappedRequest);
        expect(result).toBeTruthy();
        expect(requestOtherUser.apiUser).toBe(apiUser);
      });
    });
  });

  describe('upshot api user', () => {
    let upshotUserRequest;
    let wrappedRequest;

    beforeEach(() => {
      upshotUserRequest = {
        headers: {
          'x-api-key': '123',
          authorization: 'Bearer 456',
        },
      };
      wrappedRequest = wrapRequest(upshotUserRequest);
      mockApiTier.isInternal.mockReturnValue(true);
    });

    it('returns true if the route is public', async () => {
      mockRouteService.isRoutePublic.mockReturnValue(true);
      const result = await guard.canActivate(wrappedRequest);
      expect(result).toBeTruthy();
    });

    describe('non public route', () => {
      it('returns true', async () => {
        expect(await guard.canActivate(wrappedRequest)).toBeTruthy();
      });

      it('attaches the validated user to the request', async () => {
        const result = await guard.canActivate(wrappedRequest);
        expect(result).toBeTruthy();
        expect(upshotUserRequest.user).toBe(validatedUser);
      });

      it('attaches the api user to the request', async () => {
        const result = await guard.canActivate(wrappedRequest);
        expect(result).toBeTruthy();
        expect(upshotUserRequest.apiUser).toBe(apiUser);
      });
    });
  });
});
