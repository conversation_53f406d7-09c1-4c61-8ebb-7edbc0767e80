import { ESignUpFrom } from '@app/core/domain/user';
import { IsEnum, IsNotEmpty, IsOptional, IsString } from 'class-validator';

export class LoginDTO {
  @IsString()
  @IsNotEmpty()
  address: string;

  @IsString()
  @IsNotEmpty()
  signature: string;

  @IsEnum(Object.values(ESignUpFrom), {
    message: `sign_up from must be one of ${Object.values(ESignUpFrom)}`,
  })
  @IsOptional()
  signup_from?: string;
}

export interface CreateNonceDTO {
  address: string;
}
