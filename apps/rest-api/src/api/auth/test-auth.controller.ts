import { User } from '@app/core/domain/user';
import { Controller, Get } from '@nestjs/common';
import { ApiExcludeController } from '@nestjs/swagger';

import { Authenticated, UserDecorator } from '../../common/decorator';

@Controller('/auth-test')
@ApiExcludeController()
export class TestAuthController {
  @Get('/default')
  async default() {
    return {
      message: 'default',
    };
  }

  @Get('/optional-user')
  async optionalUser(@UserDecorator() user: User | undefined) {
    if (!user) {
      return {
        message: 'user-not-found',
        user: null,
      };
    }

    return {
      message: 'user-found',
      user: {
        username: user.username,
      },
    };
  }

  @Authenticated()
  @Get('/authenticated')
  async authenticated(@UserDecorator() user: User) {
    return {
      message: 'authenticated',
      user: {
        username: user.username,
      },
    };
  }
}
