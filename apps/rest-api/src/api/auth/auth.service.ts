import {
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  FlatUser,
  FlatUserFactory,
  User,
  USER_REPOSITORY,
  UserId,
  UserRepository,
} from '@app/core/domain/user';
import { TraceClass } from '@app/core/infra/utils/tracing.decorator';
import {
  Inject,
  Injectable,
  Logger,
  NotFoundException,
  UnauthorizedException,
} from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { InjectModel } from '@nestjs/sequelize';
import { verifyMessage } from 'ethers/lib/utils';
import { v4 as UUID } from 'uuid';

import { UserAddress } from '../../model';
import { LoginDTO } from './auth.dtos';

@TraceClass
@Injectable()
export class AuthService {
  private readonly logger = new Logger(AuthService.name);
  private readonly flatUserFactory = new FlatUserFactory();

  constructor(
    @InjectModel(UserAddress) private userAddressModel: typeof UserAddress,
    private jwtService: JwtService,
    @Inject(USER_REPOSITORY) private userRepository: UserRepository,
  ) {}

  async getNonce(address: string): Promise<{ nonce: string }> {
    this.logger.log(this.getNonce.name, 'service function');
    const nonce = UUID();

    await this.userAddressModel.upsert({
      address: address.toLowerCase(),
      nonce,
    });

    return { nonce };
  }

  async login({
    address,
    signature,
    signup_from,
  }: LoginDTO): Promise<{ auth_token: string }> {
    this.logger.log(this.login.name, 'service function');
    // validate signature against signature
    await this.validateAddress({ address, signature });

    // find user address
    const userAddress = await this.userAddressModel.findByPk(
      address.toLowerCase(),
    );

    if (userAddress?.user_id) {
      // return existing user for address
      const user = await this.userRepository.getUserById(
        new UserId(userAddress.user_id),
      );

      if (!user)
        throw new NotFoundException(`User ${userAddress.user_id} not found`);
      return {
        auth_token: this.jwtService.sign(this.flatUserFactory.toFlat(user)),
      };
    } else {
      // create new user and user_address
      const user = new User({
        username: address.toLowerCase(),
        createdAt: new Date(),
        updatedAt: new Date(),
        id: await this.userRepository.getNextId(),
        signUpFrom: (signup_from as ESignUpFrom) ?? ESignUpFrom.UPSHOT, // TODO - revisit
        isUpshotUser: false,
      });
      const savedUser = await this.userRepository.persist(user);

      await this.userAddressModel.upsert({
        address: address.toLowerCase(),
        user_id: savedUser.id.value,
      });

      return {
        auth_token: this.jwtService.sign(this.flatUserFactory.toFlat(user)),
      };
    }
  }

  /**
   * Validates the signature against the nonce
   */
  public async validateAddress({
    address,
    signature,
  }: {
    address: string;
    signature: string;
  }) {
    this.logger.log(this.validateAddress.name, 'service function');
    const user_address = await this.userAddressModel.findByPk(
      address.toLowerCase(),
    );

    if (!user_address || !user_address.nonce) {
      throw new NotFoundException(`Nonce not found, request nonce first`);
    }

    const nonce = user_address.nonce;

    let validatedAddress: string;
    const message = `Welcome to Upshot!\n\nSign this message to log in.\n\nThis request will not trigger a blockchain transaction or cost any gas fees.\n\nWallet address:\n${address}\n\nNonce:\n${nonce}`;

    try {
      validatedAddress = verifyMessage(message, signature);
    } catch (error: any) {
      throw new UnauthorizedException(
        'Error verifying user signature, please check and retry',
      );
    }
    if (address !== validatedAddress) {
      throw new UnauthorizedException('Address does not match signature');
    }
  }

  public decodeUser(authToken: string): FlatUser {
    this.logger.log(this.decodeUser.name, 'service function');
    return this.jwtService.decode(authToken) as FlatUser;
  }
}
