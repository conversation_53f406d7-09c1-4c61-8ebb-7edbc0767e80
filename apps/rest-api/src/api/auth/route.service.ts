import { ExecutionContext, Injectable } from '@nestjs/common';
import { Reflector } from '@nestjs/core';

@Injectable()
export class RouteService {
  constructor(private readonly reflector: Reflector) {}

  /**
   * @returns true if the handler requires a user to be logged in
   */
  isAuthenticated(context: ExecutionContext): boolean {
    const isAuthenticated = this.reflector.get<boolean>(
      'isAuthenticated',
      context.getHandler(),
    );
    return isAuthenticated ?? false;
  }

  /**
   * @returns - true if the handler is open to public access
   */
  isRoutePublic(context: ExecutionContext): boolean {
    const isPublic = this.reflector.get<boolean>(
      'isPublic',
      context.getHandler(),
    );
    return isPublic ?? false;
  }
}
