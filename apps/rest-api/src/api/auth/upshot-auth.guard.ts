import { GetProxyUserUseCase } from '@app/core/app/use-case/api-user/get-proxy-user.use-case';
import { BYPASS_AUTH } from '@app/core/config/env';
import { ApiUser } from '@app/core/domain/api/user/api-user';
import {
  API_USER_REPOSITORY,
  ApiUserRepository,
} from '@app/core/domain/api/user/api-user.repository';
import {
  User,
  USER_REPOSITORY,
  UserId,
  UserRepository,
} from '@app/core/domain/user';
import { TraceClass } from '@app/core/infra/utils/tracing.decorator';
import {
  ArgumentsHost,
  CanActivate,
  ExecutionContext,
  Inject,
  Injectable,
  Logger,
  UnauthorizedException,
} from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';

import { AuthService } from './auth.service';
import { RouteService } from './route.service';

@TraceClass
@Injectable()
export class UpshotAuthGuard extends AuthGuard('jwt') implements CanActivate {
  private readonly logger = new Logger(UpshotAuthGuard.name);

  constructor(
    @Inject(API_USER_REPOSITORY)
    private readonly apiUserRepository: ApiUserRepository,
    @Inject(USER_REPOSITORY)
    private readonly userRepository: UserRepository,
    private authService: AuthService,
    private readonly routeService: RouteService,
    private readonly getProxyUserUseCase: GetProxyUserUseCase,
  ) {
    super();
  }

  async canActivate(
    context: ArgumentsHost & ExecutionContext,
  ): Promise<boolean> {
    // @Public
    // Check if this is an endpoint that is public
    if (this.routeService.isRoutePublic(context) || BYPASS_AUTH) {
      return true;
    }

    const { request, authToken, apiKey } = this.parseContext(context);

    if (!apiKey) {
      throw new UnauthorizedException(`No API key found`);
    }

    // load and attach the api user to the request
    const apiUser = await this.apiUserRepository.getByActiveApiKey(apiKey);
    if (!apiUser) {
      throw new UnauthorizedException(`No API user found for API key`);
    }

    request.apiUser = apiUser;

    let user: User | null = null;

    // attach user to the request
    if (apiUser?.tier.isInternal()) {
      this.logger.debug(`API user is internal, loading user from auth token`);
      user = await this.loadAuthenticatedUser(authToken, request);
    } else {
      this.logger.debug(`Loading proxy user from API user`);
      try {
        user = await this.loadProxyUser(apiUser);
      } catch (e) {
        this.logger.error(`Error loading proxy user`);
        this.logger.error(e);
      }
    }

    request.user = user;

    // @Authenticated
    // Check if route needs to be authenticated
    const isRouteAuthenticated: boolean =
      await this.routeService.isAuthenticated(context);
    if (isRouteAuthenticated) {
      this.logger.debug(`Route must be authenticated, checking user`);

      if (!user) {
        throw new UnauthorizedException('User not authenticated');
      }
    }

    return true;
  }

  /**
   *
   * @param apiUser - the api user to load the proxy user for
   * @returns the proxy user
   */
  private async loadProxyUser(apiUser: ApiUser): Promise<User> {
    return this.getProxyUserUseCase.execute(apiUser);
  }

  /**
   * return the authenticated user from auth token
   *
   * @param authToken - the auth token to decode
   * @param request - the request to decorate
   * @returns
   */
  private async loadAuthenticatedUser(
    authToken: string | undefined,
    request: any,
  ): Promise<User | null> {
    if (!authToken) {
      this.logger.debug(`No auth token, no user`);
      return null;
    }

    const decodedUser = this.authService.decodeUser(authToken);
    const userId = new UserId(decodedUser.id);

    const user: User | null =
      (await this.userRepository.getUserById(userId)) ?? null;
    this.logger.debug(`Decorating request with user: ${user}`);
    request.user = user;
    return user;
  }

  /**
   * Parse the context to get the request, api key, and auth token.
   * The location of these values depends on the request type.
   *
   * @param context - the execution context
   * @returns
   */
  private parseContext(context: ArgumentsHost & ExecutionContext) {
    const request = context.switchToHttp().getRequest();
    const apiKey = request.headers['x-api-key'];
    const authHeader = request.headers.authorization;
    const authToken = authHeader?.split(' ')[1] ?? undefined;

    return { request, apiKey, authToken };
  }
}
