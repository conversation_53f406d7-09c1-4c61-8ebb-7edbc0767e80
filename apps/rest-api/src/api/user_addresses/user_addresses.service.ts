import { User } from '@app/core/domain/user';
import {
  BadRequestException,
  Injectable,
  Logger,
  NotFoundException,
  UnauthorizedException,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { Op } from 'sequelize';

import { UserAddress } from '../../model';
import { AuthService } from '../auth/auth.service';
import {
  UserAddressCreateDTO,
  UserAddressGetDTO,
  UserAddressUpdatesDTO,
} from './user_addresses.dtos';

@Injectable()
export class UserAddressesService {
  private readonly logger = new Logger(UserAddressesService.name);

  constructor(
    @InjectModel(UserAddress) private userAddressModel: typeof UserAddress,
    private authService: AuthService,
  ) {}

  /**
   * Delete a user address
   *
   * @param address - The address to delete
   * @param user - The user trying to delete the address
   */
  async deleteUserAddress(address: string, user: any): Promise<void> {
    this.logger.log(this.deleteUserAddress.name, 'service function');
    const userAddress = await this.userAddressModel.findByPk(address);

    // if address is not found, throw not found error
    if (!userAddress) {
      throw new NotFoundException(`Address not found`);
    }
    // if user doesn't own the wallet, throw unauthorized error
    if (userAddress.user_id !== user.id) {
      throw new UnauthorizedException(`Only owner can delete the address.`);
    }
    // delete wallet
    return userAddress.destroy();
  }

  /**
   * Return a list of user addresses
   *
   * @param userGetAddressDTO - Query params for fetching addresses
   * @param user - The user trying to get addresses
   */
  getUserAddresses(userAddressGetDTO: UserAddressGetDTO, user?: User) {
    this.logger.log(this.getUserAddresses.name, 'service function');
    const { limit, offset, user_ids = [] } = userAddressGetDTO;

    // search for public address if user_ids provided
    if (user_ids.length > 0) {
      return this.userAddressModel.findAll({
        where: {
          user_id: { [Op.in]: user_ids },
          is_public: true,
        },
        limit,
        offset,
      });
    }

    // otherwise if user logged in, get all their address
    if (user?.id) {
      return this.userAddressModel.findAll({
        where: { user_id: user.id.value },
        limit,
        offset,
      });
    }

    // if no user logged in and no user_ids provided, throw bad request error
    throw new BadRequestException(
      'authenticated user or user_ids query required',
    );
  }

  /**
   * Update a user address if the authenticated user is the owner
   *
   * @param address - The address to update
   * @param userAddressUpdates - The updates to make
   * @param user - The user trying to update the address
   */
  async updateUserAddress(
    address: string,
    userAddressUpdates: UserAddressUpdatesDTO,
    user: User,
  ): Promise<UserAddress> {
    this.logger.log(this.updateUserAddress.name, 'service function');
    this.logger.log(`updateUserAddress(${address})`);

    // fetch the address
    const user_address = await this.userAddressModel.findByPk(address);

    // throw not found error if address is not found
    if (!user_address) {
      throw new NotFoundException(`Address not found`);
    }

    // Throw unauthorized error if user is not the owner of the address
    if (user_address.user_id !== user.id.value) {
      throw new UnauthorizedException(`Only owner can update the address.`);
    }

    return user_address.update(userAddressUpdates);
  }

  /**
   * Create a new user address
   * Throw if there is a validation issue
   *
   * @param userAddressCreateDTO - The address to create
   * @param user - The user trying to create the address
   */
  async createUserAddress(
    { address, signature }: UserAddressCreateDTO,
    user: User,
  ): Promise<UserAddress> {
    this.logger.log(this.createUserAddress.name, 'service function');
    // fetch the address
    const user_address = await this.userAddressModel.findByPk(
      address.toLowerCase(),
    );

    if (!user_address || user_address.nonce === null) {
      this.logger.log(`Creating new address ${address}`);
      throw new BadRequestException(
        `Can't assign address: request a nonce first`,
      );
    }

    // throw if address owned by another user
    if (
      user_address &&
      user_address.user_id !== null &&
      user_address.user_id !== user.id.value
    ) {
      throw new UnauthorizedException(`Address already owned by another user.`);
    }

    // return if the address is owned by the user
    this.logger.log(`Address ${user_address} is owned by user ${user}`);
    if (user_address && user_address.user_id === user.id.value) {
      return user_address;
    }

    // throw if address is not valid
    try {
      await this.authService.validateAddress({ address, signature });
    } catch (error) {
      throw new BadRequestException(error.message);
    }

    // create the address
    return user_address.update({ user_id: user.id.value });
  }
}
