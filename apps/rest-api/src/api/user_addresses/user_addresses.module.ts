import { Modu<PERSON> } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';

import { UserAddress } from '../../model';
import { AuthModule } from '../auth/auth.module';
import { UserAddressesController } from './user_addresses.controller';
import { UserAddressesService } from './user_addresses.service';

@Module({
  imports: [SequelizeModule.forFeature([UserAddress]), AuthModule],
  controllers: [UserAddressesController],
  providers: [UserAddressesService],
})
export class UserAddressesModule {}
