import { faker } from '@faker-js/faker';
import { Test, TestingModule } from '@nestjs/testing';

import { FAKE_CLS_PROVIDER } from '../../common/test/test.utils';
import { UserAddressesController } from './user_addresses.controller';
import { UserAddressesService } from './user_addresses.service';

describe('UserAddressesController', () => {
  let controller: UserAddressesController;
  const address = faker.finance.ethereumAddress();
  const getUserAddressesResult = [
    {
      address,
    },
    {
      address: faker.finance.ethereumAddress(),
    },
  ];
  let userAddressService;

  beforeEach(async () => {
    jest.resetAllMocks();

    userAddressService = {
      deleteUserAddress: jest.fn().mockReturnValue(void 0),
      getUserAddresses: jest.fn().mockResolvedValue(getUserAddressesResult),
      updateUserAddress: jest.fn().mockResolvedValue(getUserAddressesResult[0]),
      createUserAddress: jest.fn().mockResolvedValue(getUserAddressesResult[0]),
    };

    const module: TestingModule = await Test.createTestingModule({
      controllers: [UserAddressesController],
      providers: [
        {
          provide: UserAddressesService,
          useValue: userAddressService,
        },
        FAKE_CLS_PROVIDER,
      ],
    }).compile();

    controller = module.get<UserAddressesController>(UserAddressesController);
  });

  afterAll(() => {
    jest.restoreAllMocks();
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('deleteUserAddress', () => {
    it('throws if the user address service throws', async () => {
      userAddressService.deleteUserAddress.mockRejectedValueOnce(
        new Error('test'),
      );

      try {
        await controller.delete({ address }, { id: 'user-id' } as any);
        throw new Error('should not reach here');
      } catch (e) {
        expect(e.message).toEqual('test');
      }
    });

    it('returns the result of the user address service', async () => {
      userAddressService.deleteUserAddress.mockReturnValueOnce('result');
      const result = await controller.delete({ address }, {
        id: 'user-id',
      } as any);
      expect(result).toEqual(true);
    });
  });

  describe('search', () => {
    it('returns result of userAddressService query', async () => {
      const user_id = Math.ceil(Math.random() * 10_000);
      const user = { id: Math.ceil(Math.random() * 10_000) } as any;
      const result = await controller.search({ user_id } as any, user);

      expect(userAddressService.getUserAddresses).toHaveBeenCalledWith(
        { user_id },
        user,
      );
      expect(result).toEqual(getUserAddressesResult);
    });
  });

  describe('createAddress', () => {
    it('throws if the user address service throws', () => {
      userAddressService.createUserAddress.mockRejectedValueOnce(
        new Error('test'),
      );

      expect(
        controller.create(
          { address: faker.finance.ethereumAddress() } as any,
          { id: 'user-id' } as any,
        ),
      ).rejects.toThrowError('test');
    });

    it('returns the result of the user address service', async () => {
      userAddressService.createUserAddress.mockReturnValueOnce('result');
      const result = await controller.create(
        { address: faker.finance.ethereumAddress() } as any,
        { id: 'user-id' } as any,
      );
      expect(result).toEqual('result');
    });
  });

  describe('updateUserAddress', () => {
    it('throws if the user address service throws', async () => {
      userAddressService.updateUserAddress.mockRejectedValueOnce(
        new Error('test'),
      );

      try {
        await controller.update(
          { address },
          { is_public: true, name: 'test' },
          { id: 'user-id' } as any,
        );
        throw new Error('should not reach here');
      } catch (e) {
        expect(e.message).toEqual('test');
      }
    });

    it('returns the result of the user address service', async () => {
      userAddressService.updateUserAddress.mockReturnValueOnce('result');
      const result = await controller.update(
        { address },
        { is_public: true, name: 'test' },
        { id: 'user-id' } as any,
      );

      expect(result).toEqual('result');
    });
  });
});
