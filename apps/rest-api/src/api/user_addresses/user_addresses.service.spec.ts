import { User, UserId } from '@app/core/domain/user';
import { faker } from '@faker-js/faker';
import {
  BadRequestException,
  NotFoundException,
  UnauthorizedException,
} from '@nestjs/common';
import { getModelToken } from '@nestjs/sequelize';
import { Test, TestingModule } from '@nestjs/testing';
import { v4 } from 'uuid';

import { FAKE_CLS_PROVIDER } from '../../common/test/test.utils';
import { UserAddress } from '../../model';
import { AuthService } from '../auth/auth.service';
import { UserAddressesService } from './user_addresses.service';

describe('UserAddressesService', () => {
  let service: UserAddressesService;
  let authService;

  const publicAddresses = [
    {
      address: faker.finance.ethereumAddress(),
      is_public: true,
    },
    {
      address: faker.finance.ethereumAddress(),
      is_public: true,
    },
  ];

  const privateAddresses = [
    {
      address: faker.finance.ethereumAddress(),
      is_public: false,
    },
  ];

  const allAddresses = [...publicAddresses, ...privateAddresses];

  const userAddressModel = {
    update: jest.fn().mockRejectedValue(new Error('result not set')),
    findByPk: jest.fn().mockResolvedValue(null),
    findAll: jest.fn().mockImplementation(({ where }) => {
      if (where.is_public) {
        return Promise.resolve(publicAddresses);
      }

      return Promise.resolve(allAddresses);
    }),
  };

  const userAddressUpdated = {
    user_id: Math.ceil(Math.random() * 10_000),
    address: faker.finance.ethereumAddress(),
    name: faker.commerce.product(),
    is_public: false,
  };

  const user = new User({
    id: new UserId(Math.ceil(Math.random() * 10_000)),
    imageUrl: faker.image.abstract(),
    createdAt: new Date(),
    updatedAt: new Date(),
    isUpshotUser: false,
    email: faker.internet.email(),
    nonce: v4(),
    username: faker.internet.userName(),
  });

  const otherUser = new User({
    ...user,
    id: new UserId(user.id.value + 1),
  });

  const userAddress = {
    user_id: user.id.value,
    address: faker.finance.ethereumAddress(),
    name: faker.commerce.product(),
    is_public: false,
    destroy: jest.fn().mockResolvedValue(null),
    update: jest.fn().mockResolvedValue(userAddressUpdated),
  };

  const newUserAddress = {
    user_id: null,
    address: faker.finance.ethereumAddress(),
    name: faker.commerce.product(),
    is_public: false,
    update: jest.fn().mockResolvedValue(userAddressUpdated),
  };

  beforeEach(async () => {
    authService = {
      validateAddress: jest.fn().mockImplementation(() => {
        throw new Error('Invalid signature');
      }),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        UserAddressesService,
        { provide: getModelToken(UserAddress), useValue: userAddressModel },
        { provide: AuthService, useValue: authService },
        FAKE_CLS_PROVIDER,
      ],
    }).compile();

    service = module.get<UserAddressesService>(UserAddressesService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('delete', () => {
    it('throws NotFoundException if the address is not found', async () => {
      userAddressModel.findByPk.mockResolvedValue(null);

      try {
        await service.deleteUserAddress(userAddress.address, {
          id: userAddress.user_id,
        });
        throw new Error('should not reach here');
      } catch (e) {
        expect(e.message).toEqual('Address not found');
        expect(e instanceof NotFoundException).toBeTruthy();
      }
    });

    it('throws UnauthorizedException if the user does not own the address', async () => {
      userAddressModel.findByPk.mockResolvedValue(userAddress);

      try {
        await service.deleteUserAddress(userAddress.address, otherUser);
        throw new Error('should not reach here');
      } catch (e) {
        expect(e.message).toEqual('Only owner can delete the address.');
        expect(e instanceof UnauthorizedException).toBeTruthy();
      }
    });

    it('destroys the address if the user owns it', async () => {
      userAddressModel.findByPk.mockResolvedValue(userAddress);

      await service.deleteUserAddress(userAddress.address, {
        id: userAddress.user_id,
      });

      expect(userAddress.destroy).toHaveBeenCalledTimes(1);
    });
  });

  describe('getAddresses', () => {
    it('returns private addresses if user is logged in & user_ids is not provided', () => {
      expect(
        service.getUserAddresses(
          {
            limit: 0,
            offset: 0,
          },
          user,
        ),
      ).resolves.toEqual(allAddresses);
    });

    it('returns public addresses if user_ids is provided', () => {
      expect(
        service.getUserAddresses(
          {
            user_ids: [1777],
            limit: 0,
            offset: 0,
          },
          otherUser,
        ),
      ).resolves.toEqual(publicAddresses);
    });

    it('throws if user_ids is not provided and no user is logged in', async () => {
      try {
        await service.getUserAddresses({
          limit: 0,
          offset: 0,
        });
        throw new Error('should not reach here');
      } catch (e) {
        expect(e.message).toEqual(
          'authenticated user or user_ids query required',
        );
        expect(e instanceof BadRequestException).toBeTruthy();
      }
    });
  });

  describe('createUserAddress', () => {
    beforeEach(() => {
      userAddressModel.findByPk.mockResolvedValue(newUserAddress);
    });

    it('throws BadRequestException if the user has not requested a nonce', async () => {
      userAddressModel.findByPk.mockResolvedValue(null);

      try {
        await service.createUserAddress(
          {
            address: userAddress.address,
            signature: faker.random.alphaNumeric(64),
          },
          user,
        );
        throw new Error('should not reach here');
      } catch (e) {
        expect(e.status).toEqual(400);
        expect(e.message).toEqual(
          `Can't assign address: request a nonce first`,
        );
      }
    });

    it('throws BadRequestException if the address is already in use', async () => {
      userAddressModel.findByPk.mockResolvedValue(userAddress);

      try {
        await service.createUserAddress(
          {
            address: userAddress.address,
            signature: faker.random.alphaNumeric(64),
          },
          otherUser,
        );
        throw new Error('should not reach here');
      } catch (e) {
        expect(e.message).toEqual('Address already owned by another user.');
        expect(e.status).toEqual(401);
      }
    });

    it('returns the address if the address is owned by the current user', async () => {
      userAddressModel.findByPk.mockResolvedValue(userAddress);

      const result = await service.createUserAddress(
        {
          address: userAddress.address,
          signature: faker.random.alphaNumeric(64),
        },
        user,
      );
      expect(result).toEqual(userAddress);
    });

    it('throws if the auth service throws', async () => {
      try {
        await service.createUserAddress(
          {
            address: userAddress.address,
            signature: faker.random.alphaNumeric(64),
          },
          user,
        );
        throw new Error('should not reach here');
      } catch (e) {
        expect(e.message).toEqual('Invalid signature');
      }
    });

    it('creates the address if the auth service succeeds', async () => {
      authService.validateAddress.mockImplementation(() => void 0);

      const result = await service.createUserAddress(
        {
          address: userAddress.address,
          signature: faker.random.alphaNumeric(64),
        },
        user,
      );
      expect(newUserAddress.update).toHaveBeenCalledWith({
        user_id: userAddress.user_id,
      });
      expect(result).toEqual(userAddressUpdated);
    });
  });

  describe('updateUserAddress', () => {
    it('throws NotFoundException if the address is not found', async () => {
      userAddressModel.findByPk.mockResolvedValue(null);

      try {
        await service.updateUserAddress(
          userAddress.address,
          { is_public: true, name: 'new name' },
          user,
        );
        throw new Error('should not reach here');
      } catch (e) {
        expect(e.message).toEqual('Address not found');
        expect(e instanceof NotFoundException).toBeTruthy();
      }
    });

    it('throws UnauthorizedException if the user does not own the address', async () => {
      userAddressModel.findByPk.mockResolvedValue(userAddress);

      try {
        await service.updateUserAddress(
          userAddress.address,
          { is_public: true, name: 'new name' },
          otherUser,
        );
        throw new Error('should not reach here');
      } catch (e) {
        expect(e.message).toEqual('Only owner can update the address.');
        expect(e instanceof UnauthorizedException).toBeTruthy();
      }
    });

    it('updates the address if the user owns it', async () => {
      userAddressModel.findByPk.mockResolvedValue(userAddress);
      const userAddressUpdates = {
        is_public: true,
        name: faker.commerce.product(),
      };

      const result = await service.updateUserAddress(
        userAddress.address,
        userAddressUpdates,
        user,
      );

      expect(userAddress.update).toHaveBeenCalledWith(userAddressUpdates);
      expect(result).toEqual(userAddressUpdated);
    });
  });
});
