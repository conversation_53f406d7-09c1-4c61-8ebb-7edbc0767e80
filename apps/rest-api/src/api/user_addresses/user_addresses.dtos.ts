import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  ArrayMaxSize,
  IsArray,
  IsBoolean,
  IsNumber,
  IsOptional,
  IsString,
} from 'class-validator';

import { Paginated } from '../../common/dto/Paginated';

export class UserAddressCreateDTO {
  @IsString()
  @ApiProperty()
  @Type(() => String)
  address: string;
  @IsString()
  @ApiProperty()
  @Type(() => String)
  signature: string;
}

export class UserAddressGetDTO extends Paginated {
  @IsArray()
  @IsNumber({}, { each: true })
  @ArrayMaxSize(50)
  @ApiProperty({
    isArray: true,
    required: false,
    description:
      "Optional user id field. If not set search is for authenticated user's addresses",
  })
  @Type(() => Number)
  @IsOptional()
  user_ids?: number[];
}

export class UserAddressUpdatesDTO {
  @IsBoolean()
  @ApiProperty()
  @Type(() => Boolean)
  is_public: boolean;

  @IsString()
  @ApiProperty()
  @Type(() => String)
  name: string;
}
