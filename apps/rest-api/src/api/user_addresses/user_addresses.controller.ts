import { User } from '@app/core/domain/user';
import {
  Body,
  Controller,
  Delete,
  Get,
  Logger,
  Param,
  Post,
  Put,
  Query,
} from '@nestjs/common';
import { ApiOperation, ApiParam, ApiTags } from '@nestjs/swagger';

import {
  Authenticated,
  HideControllerOnlyInProd,
  UserDecorator,
} from '../../common/decorator';
import {
  UserAddressCreateDTO,
  UserAddressGetDTO,
  UserAddressUpdatesDTO,
} from './user_addresses.dtos';
import { UserAddressesService } from './user_addresses.service';

@ApiTags('ApiUser Wallets')
@HideControllerOnlyInProd()
@Controller('user-addresses')
export class UserAddressesController {
  private readonly logger = new Logger(UserAddressesController.name);

  constructor(private readonly userAddressesService: UserAddressesService) {}

  @Get()
  @ApiOperation({
    description: `Fetches public wallets for specified user. Fetches all wallets for authenticated user.`,
    summary: 'Fetch wallets owned by authenticated or requested user',
  })
  async search(
    @Query() userGetAddressDTO: UserAddressGetDTO,
    @UserDecorator() user: User | undefined,
  ) {
    this.logger.log(`user-addresses/search`);

    // get user addresses
    const user_addresses = await this.userAddressesService.getUserAddresses(
      userGetAddressDTO,
      user,
    );

    return user_addresses;
  }

  @Post()
  @Authenticated()
  async create(
    @UserDecorator() user: User,
    @Body() userAddressCreateDTO: UserAddressCreateDTO,
  ) {
    this.logger.log(`create(${userAddressCreateDTO})`);
    this.logger.log(`for user ${user}`);

    const user_address = await this.userAddressesService.createUserAddress(
      userAddressCreateDTO,
      user,
    );

    return user_address;
  }

  @Put(':address')
  @Authenticated()
  @ApiOperation({
    description: `Update wallet details (must belong to authenticated user).`,
    summary: 'Update Wallet',
  })
  @ApiParam({
    name: 'address',
    description: 'Address to update.',
    type: String,
  })
  async update(
    @Param() { address }: { address: string },
    @Body() userAddressUpdates: UserAddressUpdatesDTO,
    @UserDecorator() user: User,
  ) {
    this.logger.log(`updateUserAddress(${address})`);
    const user_address = await this.userAddressesService.updateUserAddress(
      address,
      userAddressUpdates,
      user,
    );

    return user_address;
  }

  @Delete(':address')
  @Authenticated()
  @ApiOperation({
    description: `Delete wallet if it belongs to authenticated user.`,
    summary: 'Delete Wallet',
  })
  @ApiParam({
    name: 'address',
    description: 'Address to delete.',
    type: String,
  })
  async delete(
    @Param() { address }: { address: string },
    @UserDecorator() user: User,
  ) {
    this.logger.log(`delete(${address})`);
    await this.userAddressesService.deleteUserAddress(address, user);

    return true;
  }
}
