import { GetBalancesModule } from '@app/core/app/use-case/evm-wallet-balance/get-balances.module';
import { RedisModule } from '@app/core/infra/redis/redis.module';
import { Module } from '@nestjs/common';
import { CqrsModule } from '@nestjs/cqrs';
import { ClsModule } from 'nestjs-cls';

import { WalletController } from './wallet.controller';

@Module({
  imports: [CqrsModule, RedisModule, ClsModule, GetBalancesModule],
  controllers: [WalletController],
})
export class WalletModule {}
