import { EvmWalletBalance } from '@app/core/domain/evm-wallet-balance/evm-wallet-balance';
import { ApiProperty } from '@nestjs/swagger';
import { IsString } from 'class-validator';

export class WalletBalancesResponse {
  @ApiProperty()
  @IsString()
  eth_balance: string;

  @ApiProperty()
  @IsString()
  weth_balance: string;

  @ApiProperty()
  @IsString()
  blur_balance: string;

  constructor(walletBalance: EvmWalletBalance) {
    this.eth_balance = walletBalance.ethBalance.toString();
    this.weth_balance = walletBalance.wethBalance.toString();
    this.blur_balance = walletBalance.blurBalance.toString();
  }
}
