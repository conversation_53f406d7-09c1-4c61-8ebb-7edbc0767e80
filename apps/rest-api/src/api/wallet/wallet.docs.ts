import { CustomApiOperation } from '@app/core/infra/utils/custom-api-operation.decorator';
import { Trace } from '@app/core/infra/utils/tracing.decorator';
import { applyDecorators } from '@nestjs/common';
import { ApiParam } from '@nestjs/swagger';

import { Api<PERSON>ey } from '../../common/decorator/api-key.decorator';
import { ApiResponses } from '../../common/decorator/api-responses.decorator';
import { WalletBalancesResponse } from './response/wallet-balances.response';

export const getWalletBalancesDecorator = () => {
  return applyDecorators(
    Trace,
    CustomApiOperation({
      description: 'Gets ETH, WETH & BLUR balances of a wallet',
      summary: 'Get wallet balances',
    }),
    ApiKey(),
    ApiParam({
      name: 'address',
      type: String,
      description: 'get balances of this wallet',
      example: '******************************************',
    }),
    ...ApiResponses([WalletBalancesResponse]),
  );
};
