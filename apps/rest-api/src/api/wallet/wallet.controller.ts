import { GetBalancesUseCase } from '@app/core/app/use-case/evm-wallet-balance/get-balances.use-case';
import { Controller, Get, Logger, Param } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';

import { WalletBalancesResponse } from './response/wallet-balances.response';
import * as Docs from './wallet.docs';

@Controller('/wallets')
@ApiTags('Wallet Data')
export class WalletController {
  private readonly logger = new Logger(WalletController.name);

  constructor(private readonly getBalancesUseCase: GetBalancesUseCase) {}

  @Get('/:address/balances')
  @Docs.getWalletBalancesDecorator()
  async getWalletBalance(@Param() param) {
    this.logger.log('wallets/:address/balances');
    const walletBalances = await this.getBalancesUseCase.execute(param.address);
    return new WalletBalancesResponse(walletBalances);
  }
}
