import { AddUserReferrerCommand } from '@app/core/app/use-case/allora/allora-user/command/add-user-referrer/add-user-referrer.command';
import { GetOrCreateUserQuery } from '@app/core/app/use-case/allora/allora-user/query/get-or-create-user.query';
import { ALLORA_WALLET_BALANCE_QUERY_REPOSITORY } from '@app/core/domain/allora/allora-wallet-balance/allora-wallet-balance.query.repository';
import {
  AlloraChainConfig,
  EAlloraChainId,
} from '@app/core/domain/allora/chain/allora-chain-config';
import {
  ALLORA_TOPIC_QUERY_REPOSITORY,
  TopicQueryRepository,
} from '@app/core/domain/allora/topic/topic.query.repository';
import { TopicSortingOptions } from '@app/core/domain/allora/topic/topic-details';
import { SequelizeAlloraWalletBalanceQueryRepository } from '@app/core/infra/repository/allora/allora-wallet/query/sequelize.wallet.query.repository';
import { Body, Controller, Get, Inject, Param, Post } from '@nestjs/common';
import { QueryBus } from '@nestjs/cqrs';
import { ApiTags } from '@nestjs/swagger';

import { TimeseriesDataPointResponse } from '../topics/response/topic.response';
import * as Docs from './allora-user.docs';
import {
  AlloraUserConnectDto,
  ConnectUserReferrerDTO,
} from './allora-user.dto';
import {
  AlloraUserDetailsResponse,
  AlloraUserResponse,
  ConnectReferrerResponse,
} from './allora-user.response';

@Controller('/allora/users')
@ApiTags('Allora User Endpoints')
export class AlloraUserController {
  constructor(
    private readonly queryBus: QueryBus,
    @Inject(ALLORA_WALLET_BALANCE_QUERY_REPOSITORY)
    private readonly walletBalanceQueryRepository: SequelizeAlloraWalletBalanceQueryRepository,
    @Inject(ALLORA_TOPIC_QUERY_REPOSITORY)
    private readonly topicQueryRepository: TopicQueryRepository,
  ) {}
  @Post('connect')
  @Docs.connectUserDecorators()
  async connect(@Body() query: AlloraUserConnectDto) {
    const response = await this.queryBus.execute(
      new GetOrCreateUserQuery(query.allora_address, query.evm_address),
    );
    return new AlloraUserResponse(response);
  }

  @Post('connect-referrer')
  @Docs.connectUserReferrer()
  async connectUserReferrer(@Body() query: ConnectUserReferrerDTO) {
    await this.queryBus.execute(
      new AddUserReferrerCommand(query.user_id, query.referrer_id),
    );
    return new ConnectReferrerResponse();
  }

  @Get(':chain_id/:allora_address')
  @Docs.getUserDetails()
  async getUserDetails(
    @Param('chain_id') chainId: EAlloraChainId,
    @Param('allora_address') alloraAddress: string,
  ) {
    const res =
      await this.topicQueryRepository.getReputerWorkerTopicsWithStatsByAddress(
        alloraAddress,
        new AlloraChainConfig(chainId),
        TopicSortingOptions.EPOCH_LENGTH,
      );
    const alloraUserDetails = new AlloraUserDetailsResponse({
      alloraAddress: alloraAddress,
      workersTopics: res.workerTopics,
      reputersTopics: res.reputerTopics,
    });
    return alloraUserDetails;
  }

  @Get(':chain_id/:allora_address/balance')
  @Docs.getUserBalance()
  async getUserBalance(
    @Param('chain_id') chainId: EAlloraChainId,
    @Param('allora_address') alloraAddress: string,
  ) {
    const walletBalances =
      await this.walletBalanceQueryRepository.getWalletBalances(
        alloraAddress,
        new AlloraChainConfig(chainId),
      );
    return {
      data: walletBalances.map(
        (d) =>
          new TimeseriesDataPointResponse({
            x: Number(d.timestamp) * 1000, // turn to milliseconds,
            y: Number(d.value),
          }),
      ),
      continuation_token: null,
    };
  }
}
