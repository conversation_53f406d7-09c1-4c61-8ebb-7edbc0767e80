import { EAlloraChainId } from '@app/core/domain/allora/chain/allora-chain-config';
import { Trace } from '@app/core/infra/utils/tracing.decorator';
import { applyDecorators } from '@nestjs/common';
import { ApiBody, ApiOperation, ApiParam } from '@nestjs/swagger';

import { ApiResponses } from '../../../common/decorator/api-responses.decorator';
import { TimeseriesDataPointResponse } from '../topics/response/topic.response';
import {
  AlloraUserConnectDto,
  ConnectUserReferrerDTO,
} from './allora-user.dto';
import {
  AlloraUserDetailsResponse,
  AlloraUserResponse,
} from './allora-user.response';

export const connectUserDecorators = () => {
  return applyDecorators(
    Trace,
    ApiOperation({
      operationId: 'connectUser',
      description:
        'This will connect an Allora user, creating a new user if necessary.',
      summary: 'Connect an Allora user',
    }),
    ApiBody({
      type: AlloraUserConnectDto,
      description: 'The data required to connect a user and handle a referral.',
    }),
    ...ApiResponses(AlloraUserResponse),
  );
};

export const connectUserReferrer = () => {
  return applyDecorators(
    Trace,
    ApiOperation({
      operationId: 'connectUserReferrer',
      description: 'This will associat an Allora user with a referrer.',
      summary: 'Connect an Allora user with a referrer.',
    }),
    ApiBody({
      type: ConnectUserReferrerDTO,
      description: 'The data required to connect a user and handle a referral.',
    }),
    ...ApiResponses(AlloraUserResponse),
  );
};

export const getUserDetails = () => {
  return applyDecorators(
    Trace,
    ApiOperation({
      operationId: 'getUserDetails',
      description: 'This will get the details of an Allora user.',
      summary: 'Get the details of an Allora user.',
    }),
    ApiParam({
      name: 'chain_id',
      enum: EAlloraChainId,
      description: 'The id of the Allora chain.',
    }),
    ApiParam({
      name: 'allora_address',
      type: String,
      description: 'The Allora address of the user.',
    }),
    ...ApiResponses(AlloraUserDetailsResponse),
  );
};

export const getUserBalance = () => {
  return applyDecorators(
    Trace,
    ApiOperation({
      operationId: 'getUserBalance',
      description:
        'This will get the uallo balance timeseries of an Allora user.',
      summary: 'Get the uallo balance timeseries of an Allora user.',
    }),
    ApiParam({
      name: 'chain_id',
      enum: EAlloraChainId,
      description: 'The id of the Allora chain.',
    }),
    ApiParam({
      name: 'allora_address',
      type: String,
      description: 'The Allora address of the user.',
    }),
    ...ApiResponses([TimeseriesDataPointResponse]),
  );
};
