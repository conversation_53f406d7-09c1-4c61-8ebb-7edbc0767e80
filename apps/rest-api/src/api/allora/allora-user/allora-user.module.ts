import { GetOrCreateUserModule } from '@app/core/app/use-case/allora/allora-user/query/get-or-create-user.module';
import { TopicQueryRepositoryModule } from '@app/core/domain/allora/topic/topic.query.repository.module';
import { AlloraWalletBalanceQueryRepositoryModule } from '@app/core/domain/evm-wallet-balance/wallet-allora.query.repository.module';
import { Module } from '@nestjs/common';
import { CqrsModule } from '@nestjs/cqrs';

import { AlloraUserController } from './allora-user.controller';

@Module({
  imports: [
    CqrsModule,
    GetOrCreateUserModule,
    AlloraWalletBalanceQueryRepositoryModule,
    TopicQueryRepositoryModule,
  ],
  controllers: [AlloraUserController],
})
export class AlloraUserModule {}
