import { AlloraUserOutput } from '@app/core/app/use-case/allora/allora-user/dto/allora-user-output';
import { ApiProperty } from '@nestjs/swagger';

import { TopicResponse } from '../topics/response/topic.response';

export class AlloraUserResponse {
  @ApiProperty({
    description: 'The user ID of the connected user',
    type: String,
    example: '1ee82456-2e72-4c66-98b7-xxxxxxxxxxxx',
  })
  id: string;

  @ApiProperty({
    description: 'The Privy ID of the connected user',
    type: String,
    example: '1',
  })
  privy_id: string | null;

  @ApiProperty({
    description: 'The EVM address of the connected user',
    type: String,
    example: '0x1234',
  })
  evm_address: string | null;

  @ApiProperty({
    description: 'The cosmos address of the connected user',
    type: String,
    example: 'allo1853zalc0tgqcn5ma4y8psg6h3kgk2vt68h2ejg',
  })
  cosmos_address: string | null;

  constructor(alloraUser: AlloraUserOutput) {
    this.id = alloraUser.id;
    this.privy_id = alloraUser.privyId;
    this.evm_address = alloraUser.evmAddress;
    this.cosmos_address = alloraUser.cosmosAddress;
  }
}

export class AlloraUserGeneralDetailsResponse {
  @ApiProperty({
    description: 'The Privy ID of the connected user',
    type: String,
    example: '1',
  })
  privy_id: string | null;

  @ApiProperty({
    description: 'The EVM address of the connected user',
    type: String,
    example: '0x1234',
  })
  evm_address: string | null;

  @ApiProperty({
    description: 'The cosmos address of the connected user',
    type: String,
    example: 'allo1853zalc0tgqcn5ma4y8psg6h3kgk2vt68h2ejg',
  })
  cosmos_address: string | null;

  constructor(alloraUser: AlloraUserOutput) {
    this.privy_id = alloraUser.privyId;
    this.evm_address = alloraUser.evmAddress;
    this.cosmos_address = alloraUser.cosmosAddress;
  }
}

export class ConnectReferrerResponse {}

export class AlloraUserDetailsResponse {
  @ApiProperty({
    description: 'Allora user cosmos address',
    type: String,
    example: 'allo1853zalc0tgqcn5ma4y8psg6h3kgk2vt68h2ejg',
  })
  allora_address: string;

  @ApiProperty({
    description: 'The topics list where the user runs workers on',
    type: [TopicResponse],
    example: '1',
  })
  workers_topics: TopicResponse[];

  @ApiProperty({
    description: 'The topics list where the user is a reputer',
    type: [TopicResponse],
    example: '1',
  })
  reputers_topics: TopicResponse[];

  constructor(alloraUserDetails: any) {
    this.allora_address = alloraUserDetails.alloraAddress;
    this.workers_topics = alloraUserDetails.workersTopics.map(
      (topic) => new TopicResponse(topic),
    );
    this.reputers_topics = alloraUserDetails.reputersTopics.map(
      (topic) => new TopicResponse(topic),
    );
  }
}
