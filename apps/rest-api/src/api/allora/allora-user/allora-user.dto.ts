import * as CustomValidators from '@app/core/app/validation/validators';
import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsOptional, IsString, Validate } from 'class-validator';

export class AlloraUserConnectDto {
  @IsString()
  @IsOptional()
  @ApiProperty({
    type: String,
    description: 'The cosmos address of the user connecting.',
  })
  @Validate(CustomValidators.AlloraAddress)
  readonly allora_address: string;

  @IsString()
  @IsOptional()
  @ApiProperty({
    type: String,
    description: 'The evm address of the user connecting.',
  })
  @Validate(CustomValidators.Address)
  readonly evm_address: string;
}

export class ConnectUserReferrerDTO {
  @IsString()
  @IsNotEmpty()
  @ApiProperty({
    description: 'The user ID of the connected user',
    type: String,
    example: '1ee82456-2e72-4c66-98b7-xxxxxxxxxxxx',
  })
  user_id: string;

  @IsString()
  @IsNotEmpty()
  @ApiProperty({
    description: 'The user ID of the referrer',
    type: String,
    example: '1ee82456-2e72-4c66-98b7-xxxxxxxxxxxx',
  })
  referrer_id: string;
}
