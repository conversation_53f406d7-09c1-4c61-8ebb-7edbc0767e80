import { ALLORA_WALLET_BALANCE_QUERY_REPOSITORY } from '@app/core/domain/allora/allora-wallet-balance/allora-wallet-balance.query.repository';
import { ALLORA_TOPIC_QUERY_REPOSITORY } from '@app/core/domain/allora/topic/topic.query.repository';
import { QueryBus } from '@nestjs/cqrs';
import { Test, TestingModule } from '@nestjs/testing';

import { AlloraUserController } from './allora-user.controller';

describe('AlloraUserController', () => {
  let controller: AlloraUserController;

  const mockQueryBus = { execute: jest.fn() };
  const sequelizeWalletQueryRepository = { getWalletBalances: jest.fn() };
  const topicQueryRepository = {
    getReputerWorkerTopicsWithStatsByAddress: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AlloraUserController,
        { provide: QueryBus, useValue: mockQueryBus },
        {
          provide: ALLORA_WALLET_BALANCE_QUERY_REPOSITORY,
          useValue: sequelizeWalletQueryRepository,
        },
        {
          provide: ALLORA_TOPIC_QUERY_REPOSITORY,
          useValue: topicQueryRepository,
        },
      ],
    }).compile();

    controller = module.get<AlloraUserController>(AlloraUserController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
});
