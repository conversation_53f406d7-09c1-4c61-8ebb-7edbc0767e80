import {
  BINANCE_WALLET_CAMPAIGN_QUERY_REPOSITORY,
  BinanceWalletCampaignQueryRepository,
} from '@app/core/domain/allora/binance/wallet-campaign/binance-wallet-campaign.query.repository';
import {
  PANCAKESWAP_POINT_TRANSACTION_QUERY_REPOSITORY,
  PancakeSwapPointTransactionQueryRepository,
} from '@app/core/domain/allora/pancakeswap/pancakeswap-point-transaction/pancakeswap-point-transaction.query.repository';
import { Test, TestingModule } from '@nestjs/testing';
import { ethers } from 'ethers';

import { RSAUtils } from '../../../common/utils/rsa.utils';
import {
  BinanceResponse,
  BinanceTasksController,
} from './binance-tasks.controller';

describe('BinanceTasksController', () => {
  let controller: BinanceTasksController;
  let binanceWalletCampaignQueryRepository: BinanceWalletCampaignQueryRepository;
  let pancakeSwapPointTransactionQueryRepository: PancakeSwapPointTransactionQueryRepository;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [BinanceTasksController],
      providers: [
        {
          provide: BINANCE_WALLET_CAMPAIGN_QUERY_REPOSITORY,
          useValue: {
            getUserCampaignWalletsDetails: jest.fn(),
          },
        },
        {
          provide: PANCAKESWAP_POINT_TRANSACTION_QUERY_REPOSITORY,
          useValue: {
            getUserPredictionTransactions: jest.fn(),
          },
        },
      ],
    }).compile();

    controller = module.get<BinanceTasksController>(BinanceTasksController);
    binanceWalletCampaignQueryRepository =
      module.get<BinanceWalletCampaignQueryRepository>(
        BINANCE_WALLET_CAMPAIGN_QUERY_REPOSITORY,
      );
    pancakeSwapPointTransactionQueryRepository =
      module.get<PancakeSwapPointTransactionQueryRepository>(
        PANCAKESWAP_POINT_TRANSACTION_QUERY_REPOSITORY,
      );
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('getServerTime', () => {
    it('should return the current server time', async () => {
      const result = await controller.getServerTime();
      expect(result.code).toBe(BinanceResponse.SUCCESS.code);
      expect(result.message).toBe(BinanceResponse.SUCCESS.message);
      // ensure the difference between the current time and the server time is less than 100ms
      expect(result.data).toBeCloseTo(Date.now(), -2);
    });
  });

  describe('getTaskCompletion', () => {
    ['walletAddress', 'task', 'recvWindow', 'timestamp'].forEach((param) => {
      it(`should return invalid argument error if ${param} is missing`, async () => {
        const reqQuery = {
          walletAddress: ethers.constants.AddressZero,
          task: '[]',
          recvWindow: 10000,
          timestamp: Date.now(),
        };
        const signature = 'test_signature';
        const req = {
          url: `/binance/v1/task/completion?walletAddress=${reqQuery.walletAddress}&task=${reqQuery.task}&recvWindow=${reqQuery.recvWindow}&timestamp=${reqQuery.timestamp}`,
        };
        delete reqQuery[param];

        const result = await controller.getTaskCompletion(
          reqQuery,
          signature,
          req as any,
        );
        expect(result.code).toBe(BinanceResponse.INVALID_ARGUMENT.code);
        expect(result.message).toBe(BinanceResponse.INVALID_ARGUMENT.message);
        expect(result.data).toBeNull();
      });
    });

    it('should return invalid argument if walletAddress is invalid', async () => {
      const reqQuery = {
        walletAddress: 'invalidAddress',
        task: '[]',
        recvWindow: 10000,
        timestamp: Date.now(),
      };
      const signature = 'test_signature';
      const req = {
        url: `/binance/v1/task/completion?walletAddress=${reqQuery.walletAddress}&task=${reqQuery.task}&recvWindow=${reqQuery.recvWindow}&timestamp=${reqQuery.timestamp}`,
      };

      const result = await controller.getTaskCompletion(
        reqQuery,
        signature,
        req as any,
      );
      expect(result.code).toBe(BinanceResponse.INVALID_ARGUMENT.code);
      expect(result.message).toBe(BinanceResponse.INVALID_ARGUMENT.message);
      expect(result.data).toBeNull();
    });

    it('should return invalid argument if task is invalid', async () => {
      const reqQuery = {
        walletAddress: ethers.constants.AddressZero,
        task: '["invalidTask"]',
        recvWindow: 10000,
        timestamp: Date.now(),
      };
      const signature = 'test_signature';
      const req = {
        url: `/binance/v1/task/completion?walletAddress=${reqQuery.walletAddress}&task=${reqQuery.task}&recvWindow=${reqQuery.recvWindow}&timestamp=${reqQuery.timestamp}`,
      };

      const result = await controller.getTaskCompletion(
        reqQuery,
        signature,
        req as any,
      );
      expect(result.code).toBe(BinanceResponse.INVALID_ARGUMENT.code);
      expect(result.message).toBe(BinanceResponse.INVALID_ARGUMENT.message);
      expect(result.data).toBeNull();
    });

    it('should return invalid recv. window if recvWindow is smaller than 1000ms', async () => {
      const reqQuery = {
        walletAddress: ethers.constants.AddressZero,
        task: '[]',
        recvWindow: 999,
        timestamp: Date.now(),
      };
      const signature = 'test_signature';
      const req = {
        url: `/binance/v1/task/completion?walletAddress=${reqQuery.walletAddress}&task=${reqQuery.task}&recvWindow=${reqQuery.recvWindow}&timestamp=${reqQuery.timestamp}`,
      };

      const result = await controller.getTaskCompletion(
        reqQuery,
        signature,
        req as any,
      );
      expect(result.code).toBe(BinanceResponse.INVALID_RECV_WINDOW.code);
      expect(result.message).toBe(BinanceResponse.INVALID_RECV_WINDOW.message);
      expect(result.data).toBeNull();
    });

    it('should return invalid timestamp if timestamp is lower than server time - 3000s', async () => {
      const reqQuery = {
        walletAddress: ethers.constants.AddressZero,
        task: '[]',
        recvWindow: 10000,
        timestamp: Date.now() - 4000,
      };
      const signature = 'test_signature';
      const req = {
        url: `/binance/v1/task/completion?walletAddress=${reqQuery.walletAddress}&task=${reqQuery.task}&recvWindow=${reqQuery.recvWindow}&timestamp=${reqQuery.timestamp}`,
      };

      const result = await controller.getTaskCompletion(
        reqQuery,
        signature,
        req as any,
      );
      expect(result.code).toBe(BinanceResponse.INVALID_TIMESTAMP.code);
      expect(result.message).toBe(BinanceResponse.INVALID_TIMESTAMP.message);
      expect(result.data).toBeNull();
    });

    it('should return invalid timestamp if timestamp is greater than server time + recvWindow', async () => {
      const reqQuery = {
        walletAddress: ethers.constants.AddressZero,
        task: '[]',
        recvWindow: 5000,
        timestamp: Date.now() + 20000,
      };
      const signature = 'test_signature';
      const req = {
        url: `/binance/v1/task/completion?walletAddress=${reqQuery.walletAddress}&task=${reqQuery.task}&recvWindow=${reqQuery.recvWindow}&timestamp=${reqQuery.timestamp}`,
      };

      const result = await controller.getTaskCompletion(
        reqQuery,
        signature,
        req as any,
      );
      expect(result.code).toBe(BinanceResponse.INVALID_TIMESTAMP.code);
      expect(result.message).toBe(BinanceResponse.INVALID_TIMESTAMP.message);
      expect(result.data).toBeNull();
    });

    it('should return invalid signature if signature is incorrect', async () => {
      const reqQuery = {
        walletAddress: ethers.constants.AddressZero,
        task: '[]',
        recvWindow: 10000,
        timestamp: Date.now(),
      };
      const signature = 'invalid_signature';
      const req = {
        url: `/binance/v1/task/completion?walletAddress=${reqQuery.walletAddress}&task=${reqQuery.task}&recvWindow=${reqQuery.recvWindow}timestamp=${reqQuery.timestamp}`,
      };

      const result = await controller.getTaskCompletion(
        reqQuery,
        signature,
        req as any,
      );
      expect(result.code).toBe(BinanceResponse.INVALID_SIGNATURE.code);
      expect(result.message).toBe(BinanceResponse.INVALID_SIGNATURE.message);
    });

    it('should return valid task completion status for the specified tasks', async () => {
      const reqQuery = {
        walletAddress: ethers.constants.AddressZero,
        task: '["robonet_deposit", "pcs_ai_prediction"]',
        recvWindow: 10000,
        timestamp: Date.now(),
      };
      const signature = 'valid_signature';
      const req = {
        url: `/binance/v1/task/completion?walletAddress=${reqQuery.walletAddress}&task=${reqQuery.task}&recvWindow=${reqQuery.recvWindow}&timestamp=${reqQuery.timestamp}`,
      };

      jest.spyOn(RSAUtils, 'verify').mockReturnValue(true);

      jest
        .spyOn(
          binanceWalletCampaignQueryRepository,
          'getUserCampaignWalletsDetails',
        )
        .mockResolvedValue(new Array(1).fill({}));
      jest
        .spyOn(
          pancakeSwapPointTransactionQueryRepository,
          'getUserPredictionTransactions',
        )
        .mockResolvedValue(new Array(5).fill({}));

      const result: any = await controller.getTaskCompletion(
        reqQuery,
        signature,
        req as any,
      );
      expect(result.code).toBe(BinanceResponse.SUCCESS.code);
      expect(result.message).toBe(BinanceResponse.SUCCESS.message);
      expect(result.data.robonet_deposit).toBe(true);
      expect(result.data.pcs_ai_prediction).toBe(true);
    });

    it('should return valid task completion status for all tasks if no tasks are specified', async () => {
      const reqQuery = {
        walletAddress: ethers.constants.AddressZero,
        task: '[]',
        recvWindow: 10000,
        timestamp: Date.now(),
      };
      const signature = 'valid_signature';
      const req = {
        url: `/binance/v1/task/completion?walletAddress=${reqQuery.walletAddress}&task=${reqQuery.task}&recvWindow=${reqQuery.recvWindow}&timestamp=${reqQuery.timestamp}`,
      };

      jest.spyOn(RSAUtils, 'verify').mockReturnValue(true);

      jest
        .spyOn(
          binanceWalletCampaignQueryRepository,
          'getUserCampaignWalletsDetails',
        )
        .mockResolvedValue(new Array(1).fill({}));
      jest
        .spyOn(
          pancakeSwapPointTransactionQueryRepository,
          'getUserPredictionTransactions',
        )
        .mockResolvedValue(new Array(5).fill({}));

      const result: any = await controller.getTaskCompletion(
        reqQuery,
        signature,
        req as any,
      );
      expect(result.code).toBe(BinanceResponse.SUCCESS.code);
      expect(result.message).toBe(BinanceResponse.SUCCESS.message);
      expect(result.data.robonet_deposit).toBe(true);
      expect(result.data.pcs_ai_prediction).toBe(true);
    });

    it('should return valid task completion status for robonet_deposit task', async () => {
      const reqQuery = {
        walletAddress: ethers.constants.AddressZero,
        task: '["robonet_deposit"]',
        recvWindow: 10000,
        timestamp: Date.now(),
      };
      const signature = 'valid_signature';
      const req = {
        url: `/binance/v1/task/completion?walletAddress=${reqQuery.walletAddress}&task=${reqQuery.task}&recvWindow=${reqQuery.recvWindow}&timestamp=${reqQuery.timestamp}`,
      };

      jest.spyOn(RSAUtils, 'verify').mockReturnValue(true);

      jest
        .spyOn(
          binanceWalletCampaignQueryRepository,
          'getUserCampaignWalletsDetails',
        )
        .mockResolvedValue(new Array(1).fill({}));

      const result: any = await controller.getTaskCompletion(
        reqQuery,
        signature,
        req as any,
      );
      expect(result.code).toBe(BinanceResponse.SUCCESS.code);
      expect(result.message).toBe(BinanceResponse.SUCCESS.message);
      expect(result.data.robonet_deposit).toBe(true);
      expect(result.data.pcs_ai_prediction).toBeUndefined();
    });

    it('should return valid task completion status for pcs_ai_prediction task', async () => {
      const reqQuery = {
        walletAddress: ethers.constants.AddressZero,
        task: '["pcs_ai_prediction"]',
        recvWindow: 10000,
        timestamp: Date.now(),
      };
      const signature = 'valid_signature';
      const req = {
        url: `/binance/v1/task/completion?walletAddress=${reqQuery.walletAddress}&task=${reqQuery.task}&recvWindow=${reqQuery.recvWindow}&timestamp=${reqQuery.timestamp}`,
      };

      jest.spyOn(RSAUtils, 'verify').mockReturnValue(true);

      jest
        .spyOn(
          pancakeSwapPointTransactionQueryRepository,
          'getUserPredictionTransactions',
        )
        .mockResolvedValue(new Array(5).fill({}));

      const result: any = await controller.getTaskCompletion(
        reqQuery,
        signature,
        req as any,
      );
      expect(result.code).toBe(BinanceResponse.SUCCESS.code);
      expect(result.message).toBe(BinanceResponse.SUCCESS.message);
      expect(result.data.robonet_deposit).toBeUndefined();
      expect(result.data.pcs_ai_prediction).toBe(true);
    });
  });
});
