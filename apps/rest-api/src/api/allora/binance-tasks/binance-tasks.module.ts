import { BinanceWalletCampaignQueryRepositoryModule } from '@app/core/domain/allora/binance/wallet-campaign/binance-wallet-campaign.query.repository.module';
import { PancakeSwapPointTransactionQueryRepositoryModule } from '@app/core/domain/allora/pancakeswap/pancakeswap-point-transaction/pancakeswap-point-transaction.query.repository.module';
import { Module } from '@nestjs/common';

import { BinanceTasksController } from './binance-tasks.controller';

@Module({
  imports: [
    BinanceWalletCampaignQueryRepositoryModule,
    PancakeSwapPointTransactionQueryRepositoryModule,
  ],
  controllers: [BinanceTasksController],
})
export class BinanceTasksModule {}
