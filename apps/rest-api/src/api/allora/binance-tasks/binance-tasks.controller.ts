import { isEvmAddress } from '@app/core/app/validation/validation.utils';
import { BINANCE_PARTNER_PUBLIC_KEY } from '@app/core/config/env';
import {
  BINANCE_WALLET_CAMPAIGN_QUERY_REPOSITORY,
  BinanceWalletCampaignQueryRepository,
} from '@app/core/domain/allora/binance/wallet-campaign/binance-wallet-campaign.query.repository';
import {
  PANCAKESWAP_POINT_TRANSACTION_QUERY_REPOSITORY,
  PancakeSwapPointTransactionQueryRepository,
} from '@app/core/domain/allora/pancakeswap/pancakeswap-point-transaction/pancakeswap-point-transaction.query.repository';
import { Address } from '@app/core/domain/value-object/address.evm';
import {
  Controller,
  Get,
  Headers,
  Inject,
  Logger,
  Query,
  Req,
} from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { Request } from 'express';

import { Public } from '../../../../../api-portal/src/decorator/public.decorator';
import { UnwrappedResponse } from '../../../common/decorator/unwrapped-response.decorator';
import { RSAUtils } from '../../../common/utils/rsa.utils';
import { GetTaskCompletionQueryDto } from './binance-tasks.dto';

const MIN_PCS_AI_PREDICTION_COUNT = 5;
const BINANCE_WALLET_ID = 'binance';

export const BinanceResponse = {
  SUCCESS: {
    code: '000000',
    message: 'success',
  },
  TOO_MANY_REQUESTS: {
    code: '000001',
    message: 'too many requests',
  },
  SYSTEM_BUSY: {
    code: '000002',
    message: 'System busy',
  },
  INVALID_SIGNATURE: {
    code: '000003',
    message: 'Invalid signature',
  },
  INVALID_RECV_WINDOW: {
    code: '000004',
    message: 'Invalid recv window',
  },
  INVALID_TIMESTAMP: {
    code: '000005',
    message: 'Invalid timestamp',
  },
  INVALID_ARGUMENT: {
    code: '000006',
    message: 'Invalid argument',
  },
};

export const BinanceTask = {
  ROBONET_DEPOSIT: 'robonet_deposit',
  PCS_AI_PREDICTION: 'pcs_ai_prediction',
};

@Controller('/binance/v1/')
@ApiTags('Binance Tasks')
export class BinanceTasksController {
  private readonly logger = new Logger(BinanceTasksController.name);

  constructor(
    @Inject(BINANCE_WALLET_CAMPAIGN_QUERY_REPOSITORY)
    private readonly binanceWalletCampaignQueryRepository: BinanceWalletCampaignQueryRepository,
    @Inject(PANCAKESWAP_POINT_TRANSACTION_QUERY_REPOSITORY)
    private readonly pancakeSwapPointTransactionQueryRepository: PancakeSwapPointTransactionQueryRepository,
  ) {}

  @Get('/time')
  @Public()
  @UnwrappedResponse()
  async getServerTime() {
    return {
      code: BinanceResponse.SUCCESS.code,
      message: BinanceResponse.SUCCESS.message,
      data: Date.now(),
    };
  }

  @Get('/task/completion')
  @Public()
  @UnwrappedResponse()
  async getTaskCompletion(
    @Query() reqQuery: any,
    @Headers('signature') signature: string,
    @Req() req: Request,
  ) {
    const { walletAddress, task, recvWindow, timestamp } = reqQuery;

    // Manually parse and validate the query parameters
    const query: GetTaskCompletionQueryDto = {
      walletAddress: walletAddress,
      task: !!task ? JSON.parse(task) : undefined, // Ensure task is an array
      recvWindow: parseInt(recvWindow, 10),
      timestamp: parseInt(timestamp, 10),
    };

    // validate query params
    const validationError = this.validateQueryParams(query);

    if (validationError) {
      return validationError;
    }

    // validate signature
    const parameters = decodeURI(req.url.split('?')[1]);
    const isValidSignature = RSAUtils.verify(
      parameters,
      BINANCE_PARTNER_PUBLIC_KEY,
      signature,
    );

    if (!isValidSignature) {
      this.logger.error('Invalid signature');
      return {
        code: BinanceResponse.INVALID_SIGNATURE.code,
        message: BinanceResponse.INVALID_SIGNATURE.message,
        data: null,
      };
    }

    // get completion status by task
    const checkAllTasks = query.task.length === 0;
    const data = {};

    if (checkAllTasks || query.task.includes(BinanceTask.ROBONET_DEPOSIT)) {
      // get deposit
      const userBinanceDepositActions =
        await this.binanceWalletCampaignQueryRepository.getUserCampaignWalletsDetails(
          new Address(query.walletAddress),
          BINANCE_WALLET_ID,
        );
      data[BinanceTask.ROBONET_DEPOSIT] = userBinanceDepositActions.length > 0;
    }

    if (checkAllTasks || query.task.includes(BinanceTask.PCS_AI_PREDICTION)) {
      // get prediction completion status
      const userPredictionActions =
        await this.pancakeSwapPointTransactionQueryRepository.getUserPredictionTransactions(
          new Address(query.walletAddress),
        );
      data[BinanceTask.PCS_AI_PREDICTION] =
        userPredictionActions.length >= MIN_PCS_AI_PREDICTION_COUNT;
    }

    return {
      code: BinanceResponse.SUCCESS.code,
      message: BinanceResponse.SUCCESS.message,
      data: data,
    };
  }

  validateQueryParams(query: GetTaskCompletionQueryDto) {
    this.logger.log('Validating query params');
    // validate all the required query params are specified
    if (
      !query.walletAddress ||
      !query.task ||
      !query.recvWindow ||
      !query.timestamp
    ) {
      this.logger.error('Missing required query params');
      return {
        code: BinanceResponse.INVALID_ARGUMENT.code,
        message: BinanceResponse.INVALID_ARGUMENT.message,
        data: null,
      };
    }

    // validate wallet address
    if (!isEvmAddress(query.walletAddress)) {
      this.logger.error('Invalid walletAddress');
      return {
        code: BinanceResponse.INVALID_ARGUMENT.code,
        message: BinanceResponse.INVALID_ARGUMENT.message,
        data: null,
      };
    }

    // validate task array
    const unacceptedTasks = query.task.filter(
      (task: string) => !Object.values(BinanceTask).includes(task),
    );

    if (unacceptedTasks.length > 0) {
      this.logger.error(`Invalid tasks: ${unacceptedTasks}`);
      return {
        code: BinanceResponse.INVALID_ARGUMENT.code,
        message: BinanceResponse.INVALID_ARGUMENT.message,
        data: null,
      };
    }

    // validate recvWindow
    const serverTime = Date.now();

    if (query.recvWindow <= 1000) {
      this.logger.error(
        'Invalid recvWindow: recvWindow must be greater than 1000',
      );
      return {
        code: BinanceResponse.INVALID_RECV_WINDOW.code,
        message: BinanceResponse.INVALID_RECV_WINDOW.message,
        data: null,
      };
    }

    // validate timestamp
    const isValidTimestamp =
      serverTime - 3000 < query.timestamp &&
      query.timestamp < serverTime + query.recvWindow;
    if (!isValidTimestamp) {
      this.logger.error('Invalid timestamp');
      return {
        code: BinanceResponse.INVALID_TIMESTAMP.code,
        message: BinanceResponse.INVALID_TIMESTAMP.message,
        data: null,
      };
    }

    return null;
  }
}
