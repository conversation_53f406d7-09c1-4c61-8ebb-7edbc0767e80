import { AddUserReferrerUseCaseModule } from '@app/core/app/use-case/allora/allora-user/command/add-user-referrer/add-user-referrer.module';
import { GetUserPointsModule } from '@app/core/app/use-case/allora/allora-user/query/get-user-points.module';
import { GetCampaignModule } from '@app/core/app/use-case/allora/campaign/query/get-campaign/get-campaign.module';
import { GetCampaignsModule } from '@app/core/app/use-case/allora/campaign/query/get-campaigns/get-campaigns.module';
import { GetEvmLeaderboardModule } from '@app/core/app/use-case/allora/evm-leaderboard/get-evm-leaderboard.module';
import { GetLeaderboardModule } from '@app/core/app/use-case/allora/leaderboard/get-leaderboard.module';
import { Module } from '@nestjs/common';
import { CqrsModule } from '@nestjs/cqrs';

import { PointController } from './point.controller';

@Module({
  imports: [
    CqrsModule,
    GetLeaderboardModule,
    GetEvmLeaderboardModule,
    GetCampaignsModule,
    GetCampaignModule,
    GetUserPointsModule,
    AddUserReferrerUseCaseModule,
  ],
  controllers: [PointController],
})
export class PointModule {}
