import { GetUserPointsQuery } from '@app/core/app/use-case/allora/allora-user/query/get-user-points.query';
import { GetCampaignQuery } from '@app/core/app/use-case/allora/campaign/query/get-campaign/get-campaign.query';
import { GetCampaignsQuery } from '@app/core/app/use-case/allora/campaign/query/get-campaigns/get-campaigns.query';
import { GetEvmLeaderboardQuery } from '@app/core/app/use-case/allora/evm-leaderboard/get-evm-leaderboard.query';
import { GetLeaderboardQuery } from '@app/core/app/use-case/allora/leaderboard/get-leaderboard.query';
import { ContinuationToken } from '@app/core/domain/continuation-token/continuation-token';
import { Controller, Get, Param, Query } from '@nestjs/common';
import { QueryBus } from '@nestjs/cqrs';
import { ApiTags } from '@nestjs/swagger';

import { CampaignsQueryDTO } from './dto/campaigns.dto';
import { LeaderboardQueryDTO } from './dto/leaderboard.dto';
import * as Docs from './point.docs';
import { CampaignResponse } from './response/campaign.response';
import { UserLeaderboardEntryResponse } from './response/leaderboard.response';
import { AlloraUserPointsResponse } from './response/user-points.response';

@Controller('/allora/points')
@ApiTags('Allora Points')
export class PointController {
  constructor(private readonly queryBus: QueryBus) {}

  @Get('/campaigns')
  async getCampaigns(@Query() query: CampaignsQueryDTO) {
    const pagination = query.continuation_token
      ? { continuationToken: new ContinuationToken(query.continuation_token) }
      : {};

    const res = await this.queryBus.execute(new GetCampaignsQuery(pagination));

    return {
      campaigns: res.campaigns.map(
        (campaign) => new CampaignResponse(campaign),
      ),
      continuation_token: res.continuationToken,
    };
  }

  @Get('/campaigns/:campaign_id')
  async getCampaign(@Param('campaign_id') slug: string) {
    const res = await this.queryBus.execute(new GetCampaignQuery(slug));

    return new CampaignResponse(res);
  }

  @Get('/leaderboard')
  @Docs.getLeaderboardDecorators()
  async getLeaderboard(@Query() query: LeaderboardQueryDTO) {
    const pagination = query.continuation_token
      ? { continuationToken: new ContinuationToken(query.continuation_token) }
      : {};

    const res = await this.queryBus.execute(
      new GetLeaderboardQuery(pagination),
    );

    return {
      leaderboard: res.leaderboard.map(
        (entry) => new UserLeaderboardEntryResponse(entry),
      ),
      continuation_token: res.continuationToken?.value,
    };
  }

  @Get('/evm-leaderboard')
  @Docs.getEvmLeaderboardDecorators()
  async getEvmLeaderboard(@Query() query: LeaderboardQueryDTO) {
    const pagination = query.continuation_token
      ? { continuationToken: new ContinuationToken(query.continuation_token) }
      : {};

    const res = await this.queryBus.execute(
      new GetEvmLeaderboardQuery(pagination),
    );

    return {
      leaderboard: res.leaderboard.map(
        (entry) => new UserLeaderboardEntryResponse(entry),
      ),
      continuation_token: res.continuationToken?.value,
    };
  }

  @Get('/:user_id')
  @Docs.getPointsDecorators()
  async getPoints(@Param('user_id') userId: string) {
    const res = await this.queryBus.execute(new GetUserPointsQuery(userId));

    return new AlloraUserPointsResponse(res);
  }
}
