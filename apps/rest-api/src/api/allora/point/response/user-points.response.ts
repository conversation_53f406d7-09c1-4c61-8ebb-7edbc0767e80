import {
  AlloraUserLeaderboardStatsOutput,
  AlloraUserPointsOutput,
} from '@app/core/app/use-case/allora/allora-user/dto/allora-user-points-output';
import { ApiProperty } from '@nestjs/swagger';

import { AlloraUserResponse } from '../../allora-user/allora-user.response';

export class LeaderboardStatsResponse {
  @ApiProperty({
    type: Number,
    description: 'Total points of the user across all campaigns.',
    example: 1000,
  })
  total_points: number;

  @ApiProperty({
    type: Number,
    description: 'Rank of the user in the leaderboard.',
    example: 1,
  })
  rank: number;

  constructor(stats: AlloraUserLeaderboardStatsOutput) {
    this.total_points = stats.totalPoints;
    this.rank = stats.rank;
  }
}

class UserPointsPerCampaignResponse {
  @ApiProperty({
    type: String,
    description: 'Campaign slug.',
    example: 'test-campaign',
  })
  campaign_slug: string;

  @ApiProperty({
    type: Number,
    description: 'Points earned by the user in the campaign.',
    example: 100,
  })
  points: number;

  constructor(campaignSlug: string, points: number) {
    this.campaign_slug = campaignSlug;
    this.points = points;
  }
}

export class AlloraUserPointsResponse extends AlloraUserResponse {
  @ApiProperty({
    type: LeaderboardStatsResponse,
    description: 'Allora leaderboard stats of the user.',
    nullable: true,
  })
  allora_leaderboard_stats: LeaderboardStatsResponse | null;

  @ApiProperty({
    type: LeaderboardStatsResponse,
    description: 'Allora evm leaderboard stats of the user.',
    nullable: true,
  })
  evm_leaderboard_stats: LeaderboardStatsResponse | null;

  @ApiProperty({
    type: [UserPointsPerCampaignResponse],
    description: 'Points earned by the user in each campaign.',
  })
  campaign_points: UserPointsPerCampaignResponse[];

  constructor(userPointsOutput: AlloraUserPointsOutput) {
    super(userPointsOutput);

    this.allora_leaderboard_stats = userPointsOutput.alloraLeaderboardStats
      ? new LeaderboardStatsResponse(userPointsOutput.alloraLeaderboardStats)
      : null;
    this.evm_leaderboard_stats = userPointsOutput.evmLeaderboardStats
      ? new LeaderboardStatsResponse(userPointsOutput.evmLeaderboardStats)
      : null;
    this.campaign_points = userPointsOutput.campaignPoints.map(
      (userCampaignPoints) =>
        new UserPointsPerCampaignResponse(
          userCampaignPoints.campaignSlug,
          userCampaignPoints.points,
        ),
    );
  }
}
