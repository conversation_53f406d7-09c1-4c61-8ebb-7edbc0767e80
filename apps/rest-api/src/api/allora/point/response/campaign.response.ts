import { CampaignOutput } from '@app/core/app/use-case/allora/campaign/dto/campaign-output';
import { ApiProperty } from '@nestjs/swagger';

export class CampaignResponse {
  @ApiProperty({
    type: String,
    description: 'Campaign slug',
  })
  slug: string;

  @ApiProperty({
    type: String,
    description: 'Campaign name',
    example: 1000,
  })
  name: string;

  @ApiProperty({
    type: String,
    description: 'Campaign description',
    example: 'Campaign description',
    nullable: true,
  })
  description: string | null;

  @ApiProperty({
    type: [Number],
    description: 'Metadata tags associated to the campaign',
    example: ['Tag1', 'Tag2'],
    nullable: true,
  })
  tags: string[] | null;

  @ApiProperty({
    type: [Number],
    description: 'The topic ids associated to the campaign',
    example: [1, 2],
    nullable: true,
  })
  topic_ids: number[] | null;

  @ApiProperty({
    type: String,
    description: 'Campaign points',
    example: 1000,
    nullable: true,
  })
  points: number | null;

  @ApiProperty({
    type: Date,
    description: 'Start date of the campaign',
    nullable: true,
  })
  start_date: Date;

  @ApiProperty({
    type: Date,
    description: 'End date of the campaign',
    nullable: true,
  })
  end_date: Date;

  @ApiProperty({
    type: String,
    description: 'Type of the campaign',
    example: 'type',
    nullable: true,
  })
  type: string | null;

  constructor(campaign: CampaignOutput) {
    this.slug = campaign.slug;
    this.name = campaign.name;
    this.description = campaign.description;
    this.tags = campaign.tags;
    this.topic_ids = campaign.topicIds;
    this.points = campaign.points;
    this.start_date = campaign.startDate;
    this.end_date = campaign.endDate;
    this.type = campaign.type;
  }
}
