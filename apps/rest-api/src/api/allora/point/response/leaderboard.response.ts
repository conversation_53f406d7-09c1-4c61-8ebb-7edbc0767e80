import { LeaderboardEntry } from '@app/core/domain/allora/leaderboard/leaderboard';
import { ApiProperty } from '@nestjs/swagger';

import { AlloraUserGeneralDetailsResponse } from '../../allora-user/allora-user.response';

export class UserLeaderboardEntryResponse {
  @ApiProperty({
    type: AlloraUserGeneralDetailsResponse,
    description: 'Allora user details.',
  })
  user: AlloraUserGeneralDetailsResponse;

  @ApiProperty({
    type: Number,
    description: 'Total points of the user across all campaigns.',
    example: 1000,
  })
  total_points: number;

  @ApiProperty({
    type: Number,
    description: 'Rank of the user in the leaderboard.',
    example: 1,
  })
  rank: number;

  constructor(leaderboardEntry: LeaderboardEntry) {
    this.user = new AlloraUserGeneralDetailsResponse(leaderboardEntry.user);
    this.total_points = leaderboardEntry.totalPoints;
    this.rank = leaderboardEntry.rank;
  }
}
