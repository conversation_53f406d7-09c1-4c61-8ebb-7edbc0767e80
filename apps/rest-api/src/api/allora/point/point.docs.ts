import { Trace } from '@app/core/infra/utils/tracing.decorator';
import { applyDecorators } from '@nestjs/common';
import { ApiOperation, ApiParam } from '@nestjs/swagger';

import { ApiResponses } from '../../../common/decorator/api-responses.decorator';
import { ContinuationTokenDocs } from '../../../common/decorator/continuation-token.decorator';
import { UserLeaderboardEntryResponse } from './response/leaderboard.response';
import { AlloraUserPointsResponse } from './response/user-points.response';

export const getPointsDecorators = () => {
  return applyDecorators(
    Trace,
    ApiOperation({
      operationId: 'getPoints',
      description:
        'This will return the points breakdown for the user with the given user_id.',
      summary: 'Get Allora user points',
    }),
    ApiParam({
      name: 'user_id',
      type: String,
      description:
        'The id of the user whose points details is to be retrieved.',
    }),
    ...ApiResponses(AlloraUserPointsResponse),
  );
};

export const getLeaderboardDecorators = () => {
  return applyDecorators(
    Trace,
    ApiOperation({
      operationId: 'getLeaderboard',
      description:
        'Retrieves the Allora points leaderboard including cosmos-only users.',
      summary: 'Allora Leaderboard',
    }),
    ContinuationTokenDocs(),
    ...ApiResponses([UserLeaderboardEntryResponse]),
  );
};

export const getEvmLeaderboardDecorators = () => {
  return applyDecorators(
    Trace,
    ApiOperation({
      operationId: 'getEvmLeaderboard',
      description:
        'Retrieves the Allora points leaderboard including evm-only users.',
      summary: 'Allora Leaderboard',
    }),
    ContinuationTokenDocs(),
    ...ApiResponses([UserLeaderboardEntryResponse]),
  );
};
