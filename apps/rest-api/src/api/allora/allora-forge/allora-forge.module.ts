import { AlloraForgeCommandRepositoryModule } from '@app/core/domain/allora/allora-forge/allora-forge.command.repository.module';
import { AlloraForgeQueryRepositoryModule } from '@app/core/domain/allora/allora-forge/allora-forge.query.repository.module';
import { Module } from '@nestjs/common';
import { CqrsModule } from '@nestjs/cqrs';

import { AlloraForgeController } from './allora-forge.controller';

@Module({
  imports: [
    CqrsModule,
    AlloraForgeQueryRepositoryModule,
    AlloraForgeCommandRepositoryModule,
  ],
  controllers: [AlloraForgeController],
})
export class AlloraForgeModule {}
