import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsNotEmpty,
  IsN<PERSON>ber,
  IsString,
  ValidateIf,
  ValidateNested,
} from 'class-validator';

export enum ETypeformFieldRef {
  USERNAME = 'f29a40e8-85b2-45ba-820a-210dcd060d70',
  FULL_NAME = '7a0e2bdd-fdac-4ef1-8d61-12ea75c93f1a',
  COSMOS_ADDRESS = 'fd3e1d6a-d24e-4d0e-be99-8938d86623e1',
  EMAIL = 'cc59a9c7-cea1-4128-b7da-2891809c16ad',
  DISCORD = 'de052597-6a68-4acb-ab34-51ba54b89ef6',
  TELEGRAM = '16e0bced-95cc-46fb-88d9-7b5f8e78c6b8',
  LINKEDIN = 'd5478c15-a56a-4861-b551-703afb720e2f',
  X_TWITTER = 'd0ebe604-df5d-4a77-945c-02b16dada361',
  HUGGING_FACE = 'a79240a1-3a54-4eeb-949b-c8c08fceb650',
  KAGGLE = '53ac30e2-a661-46f5-85b6-cd134cc04fc2',
  GITHUB = 'fca676f6-8e8e-4e98-812c-52293f0f8b0c',
}

export class AlloraForgeRegisterDTO {
  @IsString()
  @IsNotEmpty()
  @ApiProperty({
    description: 'The user cosmos address of the participant',
    type: String,
    example: 'allo1ldrrmwzq4xacfz9slfeld0zvafr0kva7d6438y',
  })
  cosmos_address: string;

  @IsNumber()
  @IsNotEmpty()
  @ApiProperty({
    description: 'The competition ID',
    type: Number,
    example: 1,
  })
  competition_id: number;
}

export class AlloraForgeSignUpDTO {
  @IsString()
  @IsNotEmpty()
  @ApiProperty({
    description: 'The user cosmos address of the participant',
    type: String,
  })
  cosmos_address: string;

  @IsString()
  @IsNotEmpty()
  @ApiProperty({
    description: 'The username of the participant',
    type: String,
  })
  username: string;

  @IsString()
  @IsNotEmpty()
  @ApiProperty({
    description: 'The email of the participant',
    type: String,
  })
  email: string;

  @IsString()
  @IsNotEmpty()
  @ApiProperty({
    description: 'fist name',
    type: String,
  })
  first_name: string;

  @IsString()
  @IsNotEmpty()
  @ApiProperty({
    description: 'last name',
    type: String,
  })
  last_name: string;
}

export class AlloraForgeGetCompetitionDTO {
  @IsNumber()
  @Type(() => Number)
  @IsNotEmpty()
  @ApiProperty({
    description: 'The competition ID',
    type: Number,
    example: 1,
  })
  id: number;
}

export class LeaderboardQueryDTO {
  @IsString()
  @ValidateIf(
    (o) => o.continuation_token !== undefined && o.continuation_token !== null,
  )
  @ApiProperty({
    description: 'Continuation token for pagination',
    type: String,
    required: false,
    example: 'eyJwYWdlIjoyLCJsaW1pdCI6MTB9',
  })
  continuation_token?: string;
}

class TypeformField {
  @IsString()
  id: string;

  @IsString()
  type: string;

  @IsString()
  ref: string;
}

class TypeformAnswer {
  @IsString()
  type: string;

  @IsString()
  @Type(() => String)
  @ValidateIf((o) => o.type === 'text')
  text?: string;

  @IsString()
  @Type(() => String)
  @ValidateIf((o) => o.type === 'email')
  email?: string;

  @ValidateNested()
  @Type(() => TypeformField)
  field: TypeformField;
}

class TypeformFieldDefinition {
  @IsString()
  id: string;

  @IsString()
  ref: string;

  @IsString()
  type: string;

  @IsString()
  title: string;

  properties: Record<string, unknown>;
}

class TypeformEnding {
  @IsString()
  id: string;

  @IsString()
  ref: string;

  title?: string;

  type?: string;
  properties?: Record<string, unknown>;
}

class TypeformDefinition {
  @IsString()
  id: string;

  @IsString()
  title: string;

  @ValidateNested({ each: true })
  @Type(() => TypeformFieldDefinition)
  fields: TypeformFieldDefinition[];

  @ValidateNested({ each: true })
  @Type(() => TypeformEnding)
  endings: TypeformEnding[];
}

class TypeformFormResponse {
  @IsString()
  form_id: string;

  @IsString()
  token: string;

  @IsString()
  landed_at: string;

  @IsString()
  submitted_at: string;

  hidden: Record<string, string>;

  @ValidateNested()
  @Type(() => TypeformDefinition)
  definition: TypeformDefinition;

  @ValidateNested({ each: true })
  @Type(() => TypeformAnswer)
  answers: TypeformAnswer[];

  @ValidateNested()
  @Type(() => TypeformEnding)
  ending: TypeformEnding;
}

export class TypeformWebhookDTO {
  @IsString()
  @IsNotEmpty()
  @ApiProperty({
    description: 'The Typeform event ID',
    type: String,
  })
  event_id: string;

  @IsString()
  @IsNotEmpty()
  @ApiProperty({
    description: 'The event type',
    type: String,
    example: 'form_response',
  })
  event_type: string;

  @ValidateNested()
  @Type(() => TypeformFormResponse)
  @ApiProperty({
    description: 'The form response data',
    type: TypeformFormResponse,
  })
  form_response: TypeformFormResponse;
}
