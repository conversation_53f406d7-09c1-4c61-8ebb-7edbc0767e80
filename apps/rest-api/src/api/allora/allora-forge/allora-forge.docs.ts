import { Trace } from '@app/core/infra/utils/tracing.decorator';
import { applyDecorators } from '@nestjs/common';
import { ApiBody, ApiOperation, ApiParam } from '@nestjs/swagger';

import { ApiResponses } from '../../../common/decorator/api-responses.decorator';
import {
  AlloraForgeRegisterDTO,
  AlloraForgeSignUpDTO,
} from './allora-forge.dto';
import {
  AlloraForgeCompetitionLeaderboardPaginatedResponse,
  AlloraForgeCompetitionResponse,
  AlloraForgeUserResponse,
  AlloraForgeUserWithCompetitionsResponse,
  AlloraForgeUserWorkerStatsResponse,
} from './allora-forge.response';

export const signUpDecorators = () => {
  return applyDecorators(
    Trace,
    ApiOperation({
      operationId: 'signUp',
      description: 'This will create a new Allora Forge user.',
      summary: 'Sign up for Allora Forge',
    }),
    ApiBody({
      type: AlloraForgeSignUpDTO,
      description: 'The data required to create a new Allora Forge user.',
    }),
    ...ApiResponses(AlloraForgeUserResponse),
  );
};

export const registerCompetitionDecorators = () => {
  return applyDecorators(
    Trace,
    ApiOperation({
      operationId: 'registerCompetition',
      description: 'This will register a user for an Allora Forge competition.',
      summary: 'Register for an Allora Forge competition',
    }),
    ApiBody({
      type: AlloraForgeRegisterDTO,
      description: 'The data required to participate in a competition.',
    }),
    ...ApiResponses(AlloraForgeCompetitionResponse),
  );
};

export const getCompetitionDecorators = () => {
  return applyDecorators(
    Trace,
    ApiOperation({
      operationId: 'getCompetition',
      description: 'This will retrieve details of an Allora Forge competition.',
      summary: 'Get Allora Forge competition details',
    }),
    ...ApiResponses(AlloraForgeCompetitionResponse),
  );
};

export const getCompetitionsDecorators = () => {
  return applyDecorators(
    Trace,
    ApiOperation({
      operationId: 'getCompetitions',
      description:
        'This will retrieve a list of all Allora Forge competitions.',
      summary: 'Get all Allora Forge competitions',
    }),
    ...ApiResponses([AlloraForgeCompetitionResponse]),
  );
};

export const getUserDecorators = () => {
  return applyDecorators(
    Trace,
    ApiOperation({
      operationId: 'getUser',
      description: 'Get user details and participating competitions',
      summary: 'Get Allora Forge user details',
    }),
    ApiParam({
      name: 'cosmos_address',
      type: String,
      description: 'The cosmos address of the user',
      required: true,
      example: 'allo1853zalc0tgqcn5ma4y8psg6h3kgk2vt68h2ejg',
    }),
    ...ApiResponses(AlloraForgeUserWithCompetitionsResponse),
  );
};

export const getCompetitionLeaderboardDecorators = () => {
  return applyDecorators(
    Trace,
    ApiOperation({
      operationId: 'getCompetitionLeaderboard',
      description: 'Get the leaderboard for a specific competition',
      summary: 'Get Competition Leaderboard',
    }),
    ApiParam({
      name: 'competition_id',
      type: Number,
      description: 'The ID of the competition',
      required: true,
      example: 1,
    }),
    ...ApiResponses(AlloraForgeCompetitionLeaderboardPaginatedResponse),
  );
};

export const getUserWorkerStatsDecorators = () => {
  return applyDecorators(
    Trace,
    ApiOperation({
      operationId: 'getUserWorkerStats',
      description: 'Get worker statistics for a specific user',
      summary: 'Get User Worker Stats',
    }),
    ApiParam({
      name: 'cosmos_address',
      type: String,
      description: 'The cosmos address of the user',
      required: true,
      example: 'allo1853zalc0tgqcn5ma4y8psg6h3kgk2vt68h2ejg',
    }),
    ...ApiResponses([AlloraForgeUserWorkerStatsResponse]),
  );
};
