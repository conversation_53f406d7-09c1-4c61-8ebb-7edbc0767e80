import {
  AlloraForgeApprovedUserCompetition,
  AlloraForgeUser,
  AlloraForgeUserRanking,
  AlloraForgeUserWorkerStats,
} from '@app/core/domain/allora/allora-forge/allora-forge';
import { ApiProperty } from '@nestjs/swagger';

export class AlloraForgeCompetitionWithRankingResponse {
  @ApiProperty({
    description: 'The competition ID',
    type: Number,
    example: 1,
  })
  id: number;

  @ApiProperty({
    description: 'The competition name',
    type: String,
    example: 'Winter Competition 2024',
  })
  name: string;

  @ApiProperty({
    description: 'The competition preview image URL',
    type: String,
    nullable: true,
    example: 'https://example.com/preview.jpg',
  })
  preview_image_url: string | null;

  @ApiProperty({
    description: 'Brief description of the competition',
    type: String,
    nullable: true,
    example: 'A short competition description',
  })
  description: string | null;

  @ApiProperty({
    description: 'Detailed description of the competition',
    type: String,
    nullable: true,
    example: 'A more detailed explanation of the competition rules and goals',
  })
  detailed_description: string | null;

  @ApiProperty({
    description: 'The topic ID associated with this competition',
    type: Number,
    example: 1,
  })
  topic_id: number;

  @ApiProperty({
    description: 'The total prize pool for the competition',
    type: Number,
    example: 10000,
  })
  prize_pool: number;

  @ApiProperty({
    description: 'The competition start date',
    type: Date,
    example: '2024-01-01T00:00:00Z',
  })
  start_date: Date;

  @ApiProperty({
    description: 'The competition end date',
    type: Date,
    example: '2024-03-31T23:59:59Z',
  })
  end_date: Date;

  @ApiProperty({
    description: 'The season ID this competition belongs to',
    type: Number,
    example: 1,
  })
  season_id: number;

  @ApiProperty({
    description: 'Tags associated with the competition',
    type: [String],
    example: ['AI', 'Machine Learning', 'Blockchain'],
  })
  tags: string[];

  @ApiProperty({
    description: 'The competition submitted registration',
    type: Boolean,
    example: true,
  })
  submitted_registration: boolean;

  @ApiProperty({
    description: 'The competition approved registration',
    type: Boolean,
    example: true,
  })
  approved_registration: boolean;

  @ApiProperty({
    description: 'The user ranking',
    type: Number,
    example: 1,
  })
  ranking: number;

  @ApiProperty({
    description: 'Total points earned',
    type: Number,
    example: 1000,
  })
  points: number;

  @ApiProperty({
    description: 'Worker score',
    type: Number,
    example: 0.95,
  })
  score: number | null;

  @ApiProperty({
    description: 'Worker loss',
    type: Number,
    example: 0.05,
  })
  loss: number | null;

  @ApiProperty({
    description: 'Worker status',
    type: Boolean,
    example: true,
  })
  is_active: boolean | null;

  constructor(competition: any) {
    this.id = Number(competition.id);
    this.name = competition.name;
    this.preview_image_url = competition.previewImageUrl;
    this.description = competition.description;
    this.detailed_description = competition.detailedDescription;
    this.topic_id = competition.topicId;
    this.prize_pool = Number(competition.prizePool);
    this.start_date = competition.startDate;
    this.end_date = competition.endDate;
    this.season_id = competition.seasonId;
    this.tags = competition.tags;
    this.submitted_registration = competition.submittedRegistration;
    this.approved_registration = competition.approvedRegistration;
    this.ranking = Number(competition.ranking);
    this.points = Number(competition.totalPoints);
    this.score = competition.score ? Number(competition.score) : null;
    this.loss = competition.loss ? Number(competition.loss) : null;
    this.is_active = competition.isActive ? competition.isActive : null;
  }
}

export class AlloraForgeCompetitionResponse {
  @ApiProperty({
    description: 'The competition ID',
    type: Number,
    example: 1,
  })
  id: number;

  @ApiProperty({
    description: 'The competition name',
    type: String,
    example: 'Winter Competition 2024',
  })
  name: string;

  @ApiProperty({
    description: 'The competition preview image URL',
    type: String,
    nullable: true,
    example: 'https://example.com/preview.jpg',
  })
  preview_image_url: string | null;

  @ApiProperty({
    description: 'Brief description of the competition',
    type: String,
    nullable: true,
    example: 'A short competition description',
  })
  description: string | null;

  @ApiProperty({
    description: 'Detailed description of the competition',
    type: String,
    nullable: true,
    example: 'A more detailed explanation of the competition rules and goals',
  })
  detailed_description: string | null;

  @ApiProperty({
    description: 'The topic ID associated with this competition',
    type: Number,
    example: 1,
  })
  topic_id: number;

  @ApiProperty({
    description: 'The total prize pool for the competition',
    type: Number,
    example: 10000,
  })
  prize_pool: number;

  @ApiProperty({
    description: 'The competition start date',
    type: Date,
    example: '2024-01-01T00:00:00Z',
  })
  start_date: Date;

  @ApiProperty({
    description: 'The competition end date',
    type: Date,
    example: '2024-03-31T23:59:59Z',
  })
  end_date: Date;

  @ApiProperty({
    description: 'The season ID this competition belongs to',
    type: Number,
    example: 1,
  })
  season_id: number;

  @ApiProperty({
    description: 'Tags associated with the competition',
    type: [String],
    example: ['AI', 'Machine Learning', 'Blockchain'],
  })
  tags: string[];

  @ApiProperty({
    description: 'The competition submitted registration',
    type: Boolean,
    example: true,
  })
  submitted_registration: boolean;

  @ApiProperty({
    description: 'The competition approved registration',
    type: Boolean,
    example: true,
  })
  approved_registration: boolean;

  constructor(competition: any) {
    this.id = Number(competition.id);
    this.name = competition.name;
    this.preview_image_url = competition.previewImageUrl;
    this.description = competition.description;
    this.detailed_description = competition.detailedDescription;
    this.topic_id = competition.topicId;
    this.prize_pool = Number(competition.prizePool);
    this.start_date = competition.startDate;
    this.end_date = competition.endDate;
    this.season_id = competition.seasonId;
    this.tags = competition.tags;
    this.submitted_registration = competition.submittedRegistration;
    this.approved_registration = competition.approvedRegistration;
  }
}

export class AlloraForgeUserResponse {
  @ApiProperty({
    description: 'The user ID',
    type: String,
    example: '1ee82456-2e72-4c66-98b7-xxxxxxxxxxxx',
  })
  id: string;

  @ApiProperty({
    description: 'The user privy ID',
    type: String,
    example: '1',
  })
  privy_id: string;

  @ApiProperty({
    description: 'The user EVM address',
    type: String,
    example: '0x1234',
  })
  evm_address: string;

  @ApiProperty({
    description: 'The user cosmos address',
    type: String,
    example: 'allo1853zalc0tgqcn5ma4y8psg6h3kgk2vt68h2ejg',
  })
  cosmos_address: string;

  @ApiProperty({
    description: 'The user referrer ID',
    type: String,
    example: '1ee82456-2e72-4c66-98b7-xxxxxxxxxxxx',
  })
  referrer_id: string;

  @ApiProperty({
    description: 'The user username',
    type: String,
    example: 'john_doe',
  })
  username: string;

  @ApiProperty({
    description: 'The user email',
    type: String,
    example: '<EMAIL>',
  })
  email: string;

  @ApiProperty({
    description: 'The user first name',
    type: String,
    example: 'John',
  })
  first_name: string;

  @ApiProperty({
    description: 'The user last name',
    type: String,
    example: 'Doe',
  })
  last_name: string;

  constructor(forgeUser: AlloraForgeUser) {
    this.id = forgeUser.id!;
    this.privy_id = forgeUser.privyId!;
    this.evm_address = forgeUser.evmAddress!;
    this.cosmos_address = forgeUser.cosmosAddress!;
    this.referrer_id = forgeUser.referrerId!;
    this.username = forgeUser.username!;
    this.email = forgeUser.email!;
    this.first_name = forgeUser.firstName!;
    this.last_name = forgeUser.lastName!;
  }
}

export class AlloraForgeUserWithCompetitionsResponse {
  @ApiProperty({
    description: 'The user first name',
    type: String,
    example: 'John',
  })
  first_name: string;

  @ApiProperty({
    description: 'The user last name',
    type: String,
    example: 'Doe',
  })
  last_name: string;

  @ApiProperty({
    description: 'The username',
    type: String,
    example: 'john_doe',
  })
  username: string;

  @ApiProperty({
    description: 'The user cosmos address',
    type: String,
    example: 'allo1853zalc0tgqcn5ma4y8psg6h3kgk2vt68h2ejg',
  })
  cosmos_address: string;

  @ApiProperty({
    description: 'Total points earned',
    type: Number,
    example: 1000,
  })
  total_points: number;

  @ApiProperty({
    description: 'User ranking',
    type: Number,
    example: 1,
  })
  ranking: number;

  @ApiProperty({
    description: 'Badge percentile',
    type: Number,
    example: 1,
  })
  badge_percentile: number;

  @ApiProperty({
    description: 'Badge name',
    type: String,
    example: 'Badge Name',
  })
  badge_name: string;

  @ApiProperty({
    description: 'Badge description',
    type: String,
    example: 'Badge Description',
  })
  badge_description: string;

  @ApiProperty({
    description: 'Badge image',
    type: String,
    example: 'Badge Image',
  })
  badge_image: string;

  @ApiProperty({
    description: 'Competitions the user is participating in',
    type: [AlloraForgeCompetitionResponse],
  })
  competitions: AlloraForgeCompetitionResponse[];

  constructor(
    userRanking: AlloraForgeUserRanking,
    competitions: AlloraForgeApprovedUserCompetition[],
  ) {
    this.first_name = userRanking.firstName;
    this.last_name = userRanking.lastName;
    this.username = userRanking.username;
    this.cosmos_address = userRanking.cosmosAddress;
    this.total_points = userRanking.totalPoints;
    this.ranking = Number(userRanking.ranking);
    this.badge_percentile = Number(userRanking.badgePercentile);
    this.badge_name = userRanking.badgeName;
    this.badge_description = userRanking.badgeDescription;
    this.badge_image = userRanking.badgeImage;
    this.competitions = competitions.map(
      (c) => new AlloraForgeCompetitionWithRankingResponse(c),
    );
  }
}

export class AlloraForgeCompetitionLeaderboardResponse {
  @ApiProperty({
    description: 'User ranking position',
    type: Number,
    example: 1,
  })
  rank: number;

  @ApiProperty({
    description: 'User cosmos address',
    type: String,
    example: 'allo1853zalc0tgqcn5ma4y8psg6h3kgk2vt68h2ejg',
  })
  cosmos_address: string;

  @ApiProperty({
    description: 'Username',
    type: String,
    example: 'john_doe',
  })
  username: string;

  @ApiProperty({
    description: 'User first name',
    type: String,
    example: 'John',
  })
  first_name: string;

  @ApiProperty({
    description: 'User last name',
    type: String,
    example: 'Doe',
  })
  last_name: string;

  @ApiProperty({
    description: 'Total points earned',
    type: Number,
    example: 1000,
  })
  points: number;

  @ApiProperty({
    description: 'Worker score',
    type: Number,
    example: 0.95,
  })
  score: number | null;

  @ApiProperty({
    description: 'Worker loss',
    type: Number,
    example: 0.05,
  })
  loss: number | null;

  @ApiProperty({
    description: 'Worker status',
    type: Boolean,
    example: true,
  })
  is_active: boolean | null;

  constructor(entry: any) {
    this.rank = entry.rank;
    this.cosmos_address = entry.cosmosAddress;
    this.username = entry.username;
    this.first_name = entry.firstName;
    this.last_name = entry.lastName;
    this.points = Number(entry.points);
    this.score = entry.score ? Number(entry.score) : null;
    this.loss = entry.loss ? Number(entry.loss) : null;
    this.is_active = entry.isActive ? entry.isActive : null;
  }
}

export class AlloraForgeCompetitionLeaderboardPaginatedResponse {
  @ApiProperty({
    description: 'List of leaderboard entries',
    type: [AlloraForgeCompetitionLeaderboardResponse],
  })
  leaderboard: AlloraForgeCompetitionLeaderboardResponse[];

  @ApiProperty({
    description: 'Continuation token for next page',
    type: String,
    required: false,
    example: 'eyJwYWdlIjoyLCJsaW1pdCI6MTB9',
  })
  continuation_token?: string;
}

export class AlloraForgeUserWorkerStatsResponse {
  @ApiProperty({
    description: 'Competition ID',
    type: Number,
    example: 1,
  })
  id: number;

  @ApiProperty({
    description: 'Competition name',
    type: String,
    example: 'Winter Competition 2024',
  })
  name: string;

  @ApiProperty({
    description: 'Competition preview image URL',
    type: String,
    nullable: true,
    example: 'https://example.com/preview.jpg',
  })
  preview_image_url: string | null;

  @ApiProperty({
    description: 'Competition description',
    type: String,
    nullable: true,
    example: 'A short competition description',
  })
  description: string | null;

  @ApiProperty({
    description: 'Detailed competition description',
    type: String,
    nullable: true,
    example: 'A more detailed explanation of the competition',
  })
  detailed_description: string | null;

  @ApiProperty({
    description: 'Topic ID',
    type: Number,
    example: 1,
  })
  topic_id: number;

  @ApiProperty({
    description: 'Worker address',
    type: String,
    example: 'allo1853zalc0tgqcn5ma4y8psg6h3kgk2vt68h2ejg',
  })
  worker_address: string;

  @ApiProperty({
    description: 'Worker score',
    type: Number,
    example: 0.95,
  })
  worker_score: number;

  @ApiProperty({
    description: 'Worker status (active/inactive)',
    type: Boolean,
    example: true,
  })
  worker_active: boolean;

  @ApiProperty({
    description: 'Worker loss value',
    type: Number,
    example: 0.05,
  })
  worker_loss: number;

  constructor(stats: AlloraForgeUserWorkerStats) {
    this.id = Number(stats.id);
    this.name = stats.name;
    this.preview_image_url = stats.previewImageUrl;
    this.topic_id = Number(stats.topicId);
    this.worker_address = stats.workerAddress;
    this.worker_score = Number(stats.workerScore);
    this.worker_active = stats.workerStatus;
    this.worker_loss = Number(stats.workerLoss);
  }
}
