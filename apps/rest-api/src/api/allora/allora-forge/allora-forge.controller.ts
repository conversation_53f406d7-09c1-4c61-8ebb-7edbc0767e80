import {
  ALLORA_FORGE_COMMAND_REPOSITORY,
  AlloraForgeCommandRepository,
} from '@app/core/domain/allora/allora-forge/allora-forge.command.repository';
import {
  ALLORA_FORGE_QUERY_REPOSITORY,
  AlloraForgeQueryRepository,
} from '@app/core/domain/allora/allora-forge/allora-forge.query.repository';
import { ContinuationToken } from '@app/core/domain/continuation-token/continuation-token';
import {
  BadRequestException,
  Body,
  Controller,
  Get,
  Inject,
  NotFoundException,
  Param,
  Post,
  Query,
} from '@nestjs/common';
import { QueryBus } from '@nestjs/cqrs';
import { ApiTags } from '@nestjs/swagger';
import { Public } from 'apps/rest-api/src/common/decorator';

import * as Docs from './allora-forge.docs';
import {
  AlloraForgeGetCompetitionDTO,
  AlloraForgeRegisterDTO,
  ETypeformFieldRef,
  LeaderboardQueryDTO,
  TypeformWebhookDTO,
} from './allora-forge.dto';
import {
  AlloraForgeCompetitionLeaderboardPaginatedResponse,
  AlloraForgeCompetitionLeaderboardResponse,
  AlloraForgeUserResponse,
  AlloraForgeUserWithCompetitionsResponse,
  AlloraForgeUserWorkerStatsResponse,
} from './allora-forge.response';
import { AlloraForgeCompetitionResponse } from './allora-forge.response';

@Controller('/allora/forge')
@ApiTags('Allora Forge')
export class AlloraForgeController {
  constructor(
    private readonly queryBus: QueryBus,
    @Inject(ALLORA_FORGE_QUERY_REPOSITORY)
    private readonly alloraForgeQueryRepository: AlloraForgeQueryRepository,
    @Inject(ALLORA_FORGE_COMMAND_REPOSITORY)
    private readonly alloraForgeCommandRepository: AlloraForgeCommandRepository,
  ) {}

  @Get('competition/:competition_id/leaderboard')
  @Docs.getCompetitionLeaderboardDecorators()
  async getCompetitionLeaderboard(
    @Param('competition_id') competitionId: number,
    @Query() query: LeaderboardQueryDTO,
  ): Promise<AlloraForgeCompetitionLeaderboardPaginatedResponse> {
    const pagination = query.continuation_token
      ? { continuationToken: new ContinuationToken(query.continuation_token) }
      : {};

    const leaderboard =
      await this.alloraForgeQueryRepository.getCompetitionLeaderboard(
        competitionId,
        pagination,
      );

    return {
      leaderboard: leaderboard.leaderboard.map(
        (entry) => new AlloraForgeCompetitionLeaderboardResponse(entry),
      ),
      continuation_token: leaderboard.continuationToken?.value ?? undefined,
    };
  }

  @Get('user/:cosmos_address/worker-stats')
  @Docs.getUserWorkerStatsDecorators()
  async getUserWorkerStats(
    @Param('cosmos_address') workerAddress: string,
  ): Promise<AlloraForgeUserWorkerStatsResponse[]> {
    const userWorkerStats =
      await this.alloraForgeQueryRepository.getUserWorkerStats(workerAddress);

    return userWorkerStats.map(
      (s) => new AlloraForgeUserWorkerStatsResponse(s),
    );
  }

  @Get('/user/:cosmos_address')
  @Docs.getUserDecorators()
  async getUser(@Param('cosmos_address') cosmosAddress: string) {
    // get user
    const user =
      await this.alloraForgeQueryRepository.getForgeUserRankingByCosmosAddress(
        cosmosAddress,
      );
    if (!user) {
      throw new NotFoundException('User not found');
    }

    // get competitions user is participating in
    const participatingCompetitions =
      await this.alloraForgeQueryRepository.getApprovedCompetitionsByUserId(
        user.userId,
      );

    return new AlloraForgeUserWithCompetitionsResponse(
      user,
      participatingCompetitions,
    );
  }

  @Get('competitions')
  @Docs.getCompetitionsDecorators()
  async getCompetitions() {
    const competitions =
      await this.alloraForgeQueryRepository.getAllCompetitions();

    return competitions.map((c) => new AlloraForgeCompetitionResponse(c));
  }

  @Get('competition')
  @Docs.getCompetitionDecorators()
  async getCompetition(@Query() query: AlloraForgeGetCompetitionDTO) {
    const competition =
      await this.alloraForgeQueryRepository.getCompetitionById(query.id);

    if (!competition) {
      throw new NotFoundException('Competition not found');
    }

    return new AlloraForgeCompetitionResponse(competition);
  }

  @Public()
  @Post('sign-up')
  @Docs.signUpDecorators()
  async signUp(@Body() webhook: TypeformWebhookDTO) {
    if (webhook.event_type !== 'form_response') {
      throw new BadRequestException('Invalid event type');
    }

    const answers = webhook.form_response.answers;

    // Find the required fields using their refs
    const username = answers.find(
      (a) => a.field.ref === ETypeformFieldRef.USERNAME,
    )?.text;
    const email = answers.find(
      (a) => a.field.ref === ETypeformFieldRef.EMAIL,
    )?.email;
    let cosmosAddress = answers.find(
      (a) => a.field.ref === ETypeformFieldRef.COSMOS_ADDRESS,
    )?.text;

    // Required fields check
    if (!username || !email || !cosmosAddress) {
      throw new BadRequestException(
        'Missing required fields: username, email, and cosmos address',
      );
    }

    // Optional fields
    const fullName = answers.find(
      (a) => a.field.ref === ETypeformFieldRef.FULL_NAME,
    )?.text;

    // Process name if provided
    let firstName: string | null = null;
    let lastName: string | null = null;
    if (fullName && fullName.trim().length > 0) {
      const nameParts = fullName.trim().split(' ');
      lastName = nameParts.pop() || ''; // Remove and get the last word
      firstName = nameParts.join(' '); // Join everything else with spaces
    }

    // trim and make lowercase
    cosmosAddress = cosmosAddress.trim().toLowerCase();

    // check if user already exists
    const existingUser =
      await this.alloraForgeQueryRepository.getForgeUserByCosmosAddress(
        cosmosAddress,
      );

    // if user already exists and has a username and email, throw error
    if (existingUser && existingUser?.username && existingUser?.email) {
      throw new BadRequestException(
        'User already signed up. Please sign up with a different cosmos address.',
      );
    }

    // create user
    const alloraUser =
      await this.alloraForgeCommandRepository.createAlloraForgeUser(
        cosmosAddress,
        username,
        email,
        firstName,
        lastName,
      );

    if (alloraUser) {
      return new AlloraForgeUserResponse(alloraUser);
    } else {
      throw new BadRequestException('Failed to create allora forge user');
    }
  }

  @Post('register-competition')
  @Docs.registerCompetitionDecorators()
  async registerCompetition(@Body() query: AlloraForgeRegisterDTO) {
    // get allora user
    const alloraUser =
      await this.alloraForgeQueryRepository.getForgeUserByCosmosAddress(
        query.cosmos_address,
      );

    // confirm that all required fields are present (usernanme, email)
    const isForgeRegistered =
      alloraUser && alloraUser.username && alloraUser.email;
    if (!isForgeRegistered) {
      throw new NotFoundException(
        'Allora user not found. You must sign up before participating in the competition.',
      );
    }

    const competition =
      await this.alloraForgeQueryRepository.getCompetitionById(
        query.competition_id,
      );

    if (!competition) {
      throw new NotFoundException('Competition not found');
    }

    // validate that competition is active
    const competitionEndDate = new Date(competition.endDate);
    const today = new Date();
    if (today > competitionEndDate) {
      throw new BadRequestException('Competition has ended');
    }

    // register user in competition
    await this.alloraForgeCommandRepository.registerUserInCompetition(
      alloraUser.id,
      competition.id,
    );
  }
}
