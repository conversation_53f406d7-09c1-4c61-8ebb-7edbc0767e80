import { GetTokenPriceInferenceResponse } from '@app/core/app/use-case/allora/get-token-price-inferences/get-token-price-inferences.dto';
import { ApiProperty } from '@nestjs/swagger';
import { ethers } from 'ethers';

class NetworkInferenceDataForAlloraConsumerResponseProps {
  @ApiProperty({
    type: String,
    isArray: true,
    description: 'The network inference for the topic',
    example: '[776494600000000000000]',
  })
  network_inference: string;

  @ApiProperty({
    type: String,
    isArray: true,
    description: 'The normalized value of the network inference for the topic',
    example: '776.4946',
  })
  network_inference_normalized: string;

  @ApiProperty({
    type: String,
    isArray: true,
    description: 'The confidence intervals, denominated with 18 decimals.',
    example:
      '[2280000000000000000, 15870000000000000000, 84130000000000000000, 97720000000000000000]',
  })
  confidence_interval_percentiles: string[];

  @ApiProperty({
    type: String,
    isArray: true,
    description: 'The normalized confidence intervals values.',
    example: '[2.28, 15.87, 84.13, 97.72]',
  })
  confidence_interval_percentiles_normalized: string[];

  @ApiProperty({
    type: String,
    isArray: true,
    description:
      'The confidence interval values defined for the confidence_interval_percentiles above, denominated with 18 decimals.',
    example:
      '[3390000000000000000, 34920000000000000000, 89450000000000000000, 193040000000000000000]',
  })
  confidence_interval_values: string[];

  @ApiProperty({
    type: String,
    isArray: true,
    description: 'The normalized confidence interval values.',
    example: '[3.39, 34.92, 89.45, 193.04]',
  })
  confidence_interval_values_normalized: string[];

  @ApiProperty({
    type: String,
    description: 'Id of the topic on the chain.',
    example: '1',
  })
  topic_id: string;

  @ApiProperty({
    type: Number,
    description: 'Unix timestamp at which the numeric value was calculated.',
    example: 1683922671,
  })
  timestamp: number;

  @ApiProperty({
    type: String,
    description:
      'Any extra bytes that is signed alongside the other response e.g. the asset id of an appraisal that is signed for a topic dedicated to providing a time series of appraisals for a collection of NFTs.',
    example: '0x',
  })
  extra_data: Uint8Array | string;
}

class InferenceConsumerEndpointResponseProps {
  @ApiProperty({
    type: NetworkInferenceDataForAlloraConsumerResponseProps,
    description: 'The inference data.',
  })
  inference_data: NetworkInferenceDataForAlloraConsumerResponseProps;

  @ApiProperty({
    type: String,
    description:
      'Signature for the inference data according to EIP-712. This signature signs the digest consisting of `topic_id, network_inference, confidence_interval_percentiles, confidence_interval_values, timestamp, extraData` and other data to conform to EIP-712.',
    example:
      '0x4269e8ddb80dbfc34ac9c0f5fca02da67560aadff7516e44100f8f1ee6ca4a104584baf7f841febd2443ccd7eab793fa7532bfb51e21e7e90b77d0531d8d46821c',
  })
  signature: string;

  @ApiProperty({
    type: Number,
    description: 'The number of decimals of the token.',
    example: 18,
  })
  token_decimals: number;
}

export class ConsumerInferenceDataEndpointResponse extends InferenceConsumerEndpointResponseProps {
  constructor(props: InferenceConsumerEndpointResponseProps) {
    super();
    Object.assign(this, props);
  }
}

export class ConsumerInferenceDataEndpointResponseFactory {
  static fromGetConsumerInferenceDataResponse(
    res: GetTokenPriceInferenceResponse,
  ): ConsumerInferenceDataEndpointResponse {
    const signedPrice = res.signedNumericData;
    const tokenDecimals = res.tokenDecimals?.value ?? 0;
    const props: InferenceConsumerEndpointResponseProps = {
      signature: signedPrice.signature,
      token_decimals: tokenDecimals,
      inference_data: {
        network_inference:
          signedPrice.inferenceData.networkInference.toString(),
        network_inference_normalized: ethers.utils.formatUnits(
          signedPrice.inferenceData.networkInference,
          tokenDecimals,
        ),
        confidence_interval_percentiles:
          signedPrice.inferenceData.confidenceIntervalPercentiles.map((e) =>
            e.toString(),
          ),
        confidence_interval_percentiles_normalized:
          signedPrice.inferenceData.confidenceIntervalPercentiles.map((e) =>
            ethers.utils.formatUnits(e, tokenDecimals),
          ),
        confidence_interval_values:
          signedPrice.inferenceData.confidenceIntervalValues.map((e) =>
            e.toString(),
          ),
        confidence_interval_values_normalized:
          signedPrice.inferenceData.confidenceIntervalValues.map((e) =>
            ethers.utils.formatUnits(e, tokenDecimals),
          ),
        topic_id: signedPrice.inferenceData.topicId.toString(),
        timestamp: parseInt(signedPrice.inferenceData.timestamp.toString()),
        extra_data: signedPrice.inferenceData.extraData.toString(),
      },
    };
    return new ConsumerInferenceDataEndpointResponse(props);
  }
}
