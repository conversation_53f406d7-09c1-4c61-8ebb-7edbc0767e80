import { ADAPTER_SENDER_PUBLIC_KEY } from '@app/core/config/env';
import { CustomApiOperation } from '@app/core/infra/utils/custom-api-operation.decorator';
import { applyDecorators } from '@nestjs/common';
import { ApiParam, ApiQuery } from '@nestjs/swagger';

import { ApiKey } from '../../../common/decorator/api-key.decorator';
import { ApiResponses } from '../../../common/decorator/api-responses.decorator';
import { ConsumerNumericDataEndpointResponse } from './consumer-numeric-value.response';

export const relayInferencesForConsumerDecorators = () => {
  return applyDecorators(
    CustomApiOperation({
      description: `Query the active inference worker nodes of the Allora network for their newest inferences,
      sign them, then format them to be onboarded to a Consumer contract on a given target chain.
      The timestamp will be set to 5 minutes before request time to prevent this response from being 
      erroneously reverted (for being too early).
      The signer on Sepolia is ${ADAPTER_SENDER_PUBLIC_KEY}`,
      summary: 'Solicit Inferences for Consumer',
    }),
    Api<PERSON><PERSON>(),
    ApiParam({
      name: 'chain_slug',
      type: String,
      required: true,
      description:
        'Slug of the chain on which the target adapter contract is deployed',
      example: 'ethereum-11155111',
    }),
    ApiQuery({
      name: 'allora_topic_id',
      type: String,
      required: true,
      description: 'Id of the topic in the Allora L1',
      example: `2`,
    }),
    ApiQuery({
      name: 'extra_data',
      type: String,
      required: false,
      description: `Useful for specifying subtopics e.g. to get the appraisal of asset \`0x.../1465\` of an NFT collection.
      Pass in any arbitrary string. This string will be hashed with keccak256 in the returned result.`,
      example: '******************************************/1465',
    }),
    ...ApiResponses([ConsumerNumericDataEndpointResponse]),
  );
};

export const getTokenInferencesForConsumerDecorators = () => {
  return applyDecorators(
    ApiKey(),
    ApiParam({
      name: 'chain_slug',
      type: String,
      required: true,
      description:
        'Slug of the chain on which the target adapter contract is deployed',
      example: 'ethereum-11155111',
    }),
    ApiParam({
      name: 'token',
      type: String,
      required: true,
      description: 'Ticker for the token to predict',
      example: 'ETH',
    }),
    ApiParam({
      name: 'duration',
      type: String,
      required: true,
      description:
        'ISO 8601 shorthand (e.g. 3s, 10m, 5h, 2d, 7M, 1y) time duration for which to predict the token price this far in the future',
      example: '5m',
    }),
    ...ApiResponses(ConsumerNumericDataEndpointResponse),
  );
};
