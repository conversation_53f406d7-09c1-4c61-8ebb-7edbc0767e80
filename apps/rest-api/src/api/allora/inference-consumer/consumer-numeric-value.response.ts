import { ApiProperty } from '@nestjs/swagger';

class NumericDataForConsumerEndpointResponseProps {
  @ApiProperty({
    type: String,
    description: 'Id of the topic on the chain.',
    example: '1',
  })
  topic_id: string;

  @ApiProperty({
    type: String,
    isArray: true,
    description:
      'Fixed-point numeric values as a string array. If this represents a price, it is denominated in USD with 18 decimals.',
    example: '[776494600000000000000]',
  })
  numeric_values: string[];

  @ApiProperty({
    type: Number,
    description: 'Unix timestamp at which the numeric value was calculated.',
    example: 1683922671,
  })
  timestamp: number;

  @ApiProperty({
    type: String,
    description:
      'Any extra bytes that is signed alongside the other response e.g. the asset id of an appraisal that is signed for a topic dedicated to provding a time series of appraisals for a collection of NFTs.',
    example: '0x',
  })
  extra_data: Uint8Array | string;
}

class ConsumerNumericDataEndpointResponseProps {
  @ApiProperty({
    type: String,
    description: `signature for the numeric data according to EIP-712. This signature signs the digest consisting of \`topic_id, numeric_value, timestamp, extraData\` and other data to conform to EIP-712.`,
    example:
      '0x4269e8ddb80dbfc34ac9c0f5fca02da67560aadff7516e44100f8f1ee6ca4a104584baf7f841febd2443ccd7eab793fa7532bfb51e21e7e90b77d0531d8d46821c',
  })
  signature: string;

  @ApiProperty({
    type: NumericDataForConsumerEndpointResponseProps,
    description: `the signed digest.`,
  })
  numeric_data: NumericDataForConsumerEndpointResponseProps;
}

export class ConsumerNumericDataEndpointResponse extends ConsumerNumericDataEndpointResponseProps {
  constructor(props: ConsumerNumericDataEndpointResponseProps) {
    super();
    Object.assign(this, props);
  }
}
