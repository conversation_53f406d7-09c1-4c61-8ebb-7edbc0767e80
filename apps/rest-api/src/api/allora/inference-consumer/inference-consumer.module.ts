import { GetTokenPriceInferenceUseCaseModule } from '@app/core/app/use-case/allora/get-token-price-inferences/get-token-price-inferences.use-case.module';
import { RelayInferencesForConsumerUseCaseModule } from '@app/core/app/use-case/allora/relay-inferences-for-consumer/relay-inferences-for-consumer.use-case.module';
import { Module } from '@nestjs/common';

import { ConsumerController } from './inference-consumer.controller';

@Module({
  imports: [
    RelayInferencesForConsumerUseCaseModule,
    GetTokenPriceInferenceUseCaseModule,
  ],
  controllers: [ConsumerController],
})
export class ConsumerModule {}
