import { GetTokenPriceInferenceUseCase } from '@app/core/app/use-case/allora/get-token-price-inferences/get-token-price-inferences.use-case';
import { RelayInferencesForConsumerUseCase } from '@app/core/app/use-case/allora/relay-inferences-for-consumer/relay-inferences-for-consumer.use-case';
import { TopicId } from '@app/core/domain/allora/topic/topic-id';
import { ChainSlug } from '@app/core/domain/value-object/chain-slug';
import { IsoDuration } from '@app/core/domain/value-object/duration-iso';
import { NonNegativeNumber } from '@app/core/domain/value-object/nonnegative.number';
import { Ticker } from '@app/core/domain/value-object/ticket';
import { Controller, Get, Inject, Logger, Param, Query } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';

import {
  ConsumerInferenceDataEndpointResponse,
  ConsumerInferenceDataEndpointResponseFactory,
} from './consumer-inference-data.response';
import * as Docs from './inference-consumer.docs';
import * as DTO from './inference-consumer.dto';

@Controller('/allora/consumer')
@ApiTags('Allora Consumer')
export class ConsumerController {
  private readonly logger = new Logger(ConsumerController.name);
  DEBUG = false;

  constructor(
    @Inject(RelayInferencesForConsumerUseCase)
    private readonly relayInferencesUseCase: RelayInferencesForConsumerUseCase,
    @Inject(GetTokenPriceInferenceUseCase)
    private readonly getTokenPriceInferenceUseCase: GetTokenPriceInferenceUseCase,
  ) {}

  @Get(':chain_slug')
  @Docs.relayInferencesForConsumerDecorators()
  async relayInferencesForConsumer(
    @Param() param: DTO.RelayInferencesForConsumerParamDto,
    @Query() query: DTO.RelayInferencesForConsumerQueryDto,
  ): Promise<ConsumerInferenceDataEndpointResponse> {
    this.logger.log('allora/consumer/:chain_slug');

    const signed = await this.relayInferencesUseCase.execute({
      chainSlug: param.chain_slug,
      alloraTopicId: new TopicId(query.allora_topic_id),
      extraData: query.extra_data,
    });

    return ConsumerInferenceDataEndpointResponseFactory.fromGetConsumerInferenceDataResponse(
      { ...signed, tokenDecimals: new NonNegativeNumber(18) },
    );
  }

  @Get('price/:chain_slug/:token/:duration')
  @Docs.getTokenInferencesForConsumerDecorators()
  async getTokenPriceInferencesForConsumer(
    @Param() param: DTO.GetTokenInferencesForConsumerParamDto,
  ): Promise<ConsumerInferenceDataEndpointResponse> {
    this.logger.log('allora/consumer/price/:chain_slug/:token/:duration');

    const signed = await this.getTokenPriceInferenceUseCase.execute({
      chainSlug: new ChainSlug(param.chain_slug),
      tokenTicker: new Ticker(param.token.toUpperCase()),
      isoDuration: new IsoDuration(param.duration),
    });

    return ConsumerInferenceDataEndpointResponseFactory.fromGetConsumerInferenceDataResponse(
      signed,
    );
  }

  @Get('volatility/:chain_slug/:token/:duration')
  @Docs.getTokenInferencesForConsumerDecorators()
  async getTokenVolatilityInferencesForConsumer(
    @Param() param: DTO.GetTokenInferencesForConsumerParamDto,
  ): Promise<ConsumerInferenceDataEndpointResponse> {
    this.logger.log('allora/consumer/volatility/:chain_slug/:token/:duration');

    const signed = await this.getTokenPriceInferenceUseCase.execute({
      chainSlug: new ChainSlug(param.chain_slug),
      tokenTicker: new Ticker(param.token.toUpperCase()),
      isoDuration: new IsoDuration(param.duration),
    });

    return ConsumerInferenceDataEndpointResponseFactory.fromGetConsumerInferenceDataResponse(
      signed,
    );
  }
}
