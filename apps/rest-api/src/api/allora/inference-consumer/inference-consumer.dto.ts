import * as CustomValidators from '@app/core/app/validation/validators';
import { IsString, Validate } from 'class-validator';

import * as CommonDto from '../../../common/dto/adapter';

export class RelayInferencesForConsumerParamDto extends CommonDto.GetDataForAdapterParamDto {}

export class RelayInferencesForConsumerQueryDto extends CommonDto.GetDataForAdapterQueryDto {}

export class GetTokenInferencesForConsumerParamDto extends CommonDto.GetDataForAdapterParamDto {
  @IsString()
  @Validate(CustomValidators.Alphanumeric)
  token: string;

  @IsString()
  @Validate(CustomValidators.Iso8601ShorthandDuration)
  duration: string;
}
