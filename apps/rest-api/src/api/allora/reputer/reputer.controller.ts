import {
  Allora<PERSON>hainConfig,
  EAlloraChainId,
} from '@app/core/domain/allora/chain/allora-chain-config';
import { ALLORA_REPUTER_QUERY_REPOSITORY } from '@app/core/domain/allora/reputer/reputer.query.repository';
import { EReputerSortOption } from '@app/core/domain/allora/reputer/reputer-details';
import { ContinuationToken } from '@app/core/domain/continuation-token/continuation-token';
import { SequelizeReputerQueryRepository } from '@app/core/infra/repository/allora/reputer/query/sequelize.reputer.query.repository';
import {
  BadRequestException,
  Controller,
  Get,
  Inject,
  Param,
  Query,
} from '@nestjs/common';
import { QueryBus } from '@nestjs/cqrs';
import { ApiTags } from '@nestjs/swagger';

import * as Docs from './reputer.docs';
import { ReputerDelegatorStatsResponse } from './response/reputer-delegator-stats.response';
import { ReputerStatsResponse } from './response/reputer-stats.response';

@Controller('/allora/:chain_id/reputers')
@ApiTags('Allora Reputer')
export class ReputerDataController {
  constructor(
    private readonly queryBus: QueryBus,
    @Inject(ALLORA_REPUTER_QUERY_REPOSITORY)
    private readonly sequelizeReputerQueryRepository: SequelizeReputerQueryRepository,
  ) {}

  @Get('/top-reputers-by-delegator')
  @Docs.getTopReputersByDelegatorDecorators()
  async getTopReputersByDelegator(
    @Param('chain_id') chainId: EAlloraChainId,
    @Query('delegator_address') delegatorAddress: string,
    @Query('reputer_address') reputerAddress?: string,
    @Query('continuation_token') continuation_token?: string,
  ) {
    const pagination = continuation_token
      ? { continuationToken: new ContinuationToken(continuation_token) }
      : {};
    const alloraChainConfig = new AlloraChainConfig(chainId);
    if (!delegatorAddress || delegatorAddress.length < 40) {
      throw new BadRequestException('Invalid delegator address');
    }
    // if reputer address is provided, get all delegations for that reputer
    // else get all reputers for that delegator
    let res;
    if (reputerAddress) {
      res = await this.sequelizeReputerQueryRepository.getReputerByDelegator(
        delegatorAddress,
        reputerAddress,
        alloraChainConfig,
        pagination,
      );
    } else {
      res =
        await this.sequelizeReputerQueryRepository.getTopReputersByDelegator(
          delegatorAddress,
          alloraChainConfig,
          pagination,
        );
    }

    return {
      reputers: res.reputers.map(
        (reputer) => new ReputerDelegatorStatsResponse(reputer),
      ),
      continuation_token: res.continuationToken?.value ?? null,
    };
  }

  @Get('/top-reputers')
  @Docs.getTopReputersDecorators()
  async getTopReputers(
    @Param('chain_id') chainId: EAlloraChainId,
    @Query('sort_option') sortOption: EReputerSortOption,
    @Query('sort_direction') sortDirection: 'ASC' | 'DESC',
    @Query('continuation_token') continuation_token?: string,
  ) {
    const pagination = continuation_token
      ? { continuationToken: new ContinuationToken(continuation_token) }
      : {};
    const alloraChainConfig = new AlloraChainConfig(chainId);
    const res = await this.sequelizeReputerQueryRepository.getTopReputers(
      alloraChainConfig,
      sortOption,
      sortDirection,
      pagination,
    );

    return {
      reputers: res.reputers.map(
        (reputer) => new ReputerStatsResponse(reputer),
      ),
      continuation_token: res.continuationToken?.value ?? null,
    };
  }
}
