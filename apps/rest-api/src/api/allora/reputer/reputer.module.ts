import { ALLORA_REPUTER_QUERY_REPOSITORY } from '@app/core/domain/allora/reputer/reputer.query.repository';
import { ContinuationTokenRepositoryModule } from '@app/core/domain/continuation-token/continuation-token.repository.module';
import { SequelizeReputerQueryRepository } from '@app/core/infra/repository/allora/reputer/query/sequelize.reputer.query.repository';
import { Module } from '@nestjs/common';
import { CqrsModule } from '@nestjs/cqrs';

import { ReputerDataController } from './reputer.controller';

@Module({
  imports: [CqrsModule, ContinuationTokenRepositoryModule],
  providers: [
    {
      useClass: SequelizeReputerQueryRepository,
      provide: ALLORA_REPUTER_QUERY_REPOSITORY,
    },
  ],
  controllers: [ReputerDataController],
})
export class ReputerDataModule {}
