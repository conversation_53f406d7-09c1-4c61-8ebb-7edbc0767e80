import { EAlloraChainId } from '@app/core/domain/allora/chain/allora-chain-config';
import { EReputerSortOption } from '@app/core/domain/allora/reputer/reputer-details';
import { Trace } from '@app/core/infra/utils/tracing.decorator';
import { applyDecorators } from '@nestjs/common';
import { ApiOperation, ApiParam, ApiQuery } from '@nestjs/swagger';

import { ApiResponses } from '../../../common/decorator/api-responses.decorator';
import { ContinuationTokenDocs } from '../../../common/decorator/continuation-token.decorator';
import { ReputerDelegatorStatsResponse } from './response/reputer-delegator-stats.response';
import { ReputerStatsResponse } from './response/reputer-stats.response';

export const getTopReputersDecorators = () => {
  return applyDecorators(
    Trace,
    ApiOperation({
      operationId: 'getTopReputerStakes',
      description: 'Get top reputers across all topics with their stats.',
      summary: 'Get top reputers',
    }),
    ContinuationTokenDocs(),
    ApiParam({
      name: 'chain_id',
      enum: EAlloraChainId,
      description: 'The id of the Allora chain.',
    }),
    ApiQuery({
      name: 'sort_option',
      enum: EReputerSortOption,
      description: 'The option to sort reputers by.',
      required: true,
    }),
    ...ApiResponses([ReputerStatsResponse]),
  );
};

export const getTopReputersByDelegatorDecorators = () => {
  return applyDecorators(
    Trace,
    ApiOperation({
      operationId: 'getTopReputersByDelegator',
      description: 'Get top reputers by delegator with their stats.',
      summary: 'Get top reputers by delegator',
    }),
    ContinuationTokenDocs(),
    ApiParam({
      name: 'chain_id',
      enum: EAlloraChainId,
      description: 'The id of the Allora chain.',
    }),
    ApiQuery({
      name: 'delegator_address',
      description: 'The address of the delegator.',
      required: true,
    }),
    ApiQuery({
      name: 'reputer_address',
      description: 'The address of the reputer.',
      required: false,
    }),
    ...ApiResponses([ReputerDelegatorStatsResponse]),
  );
};
