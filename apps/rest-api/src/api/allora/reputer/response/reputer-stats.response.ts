import { ReputersWithStats } from '@app/core/domain/allora/reputer/reputer-details';
import { ApiProperty } from '@nestjs/swagger';

export class ReputerStatsResponse {
  @ApiProperty({
    type: String,
    description: 'Reputer address',
    example: 'allo1lqsw2azx8s42jkgxerzf4nagvqe4a3jzx5qrud5',
  })
  address: string;

  @ApiProperty({
    type: Number,
    description: 'Reputer topic id',
    example: 1,
  })
  topic_id: number;

  @ApiProperty({
    type: Boolean,
    description: 'Reputer topic is active',
    example: true,
  })
  is_active: boolean;

  @ApiProperty({
    type: Number,
    description: 'Reputer ema score',
    example: 0.1,
  })
  score: number;

  @ApiProperty({
    type: Number,
    description: 'Reputer total staked',
    example: 100000000,
  })
  total_staked: number;

  @ApiProperty({
    type: Number,
    description: 'Reputer total earned',
    example: 100000000,
  })
  total_earned: number;

  @ApiProperty({
    type: Number,
    description: 'Reputer total earned per day',
    example: 100000000,
  })
  total_earned_per_day: number;

  constructor(reputer: ReputersWithStats) {
    this.address = reputer.address;
    this.topic_id = reputer.topicId;
    this.is_active = reputer.isActive;
    this.score = reputer.score;
    this.total_staked = reputer.totalStaked;
    this.total_earned = reputer.totalEarned;
    this.total_earned_per_day = reputer.totalEarnedPerDay;
  }
}
