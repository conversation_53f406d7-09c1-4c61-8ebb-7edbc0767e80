import { EAlloraChainId } from '@app/core/domain/allora/chain/allora-chain-config';
import { Trace } from '@app/core/infra/utils/tracing.decorator';
import { applyDecorators } from '@nestjs/common';
import { ApiOperation, ApiParam, ApiQuery } from '@nestjs/swagger';

import { ApiResponses } from '../../../common/decorator/api-responses.decorator';
import { ContinuationTokenDocs } from '../../../common/decorator/continuation-token.decorator';
import { ReputerResponse } from './response/reputer.response';
import {
  TimeseriesDataPointResponse,
  TopicResponse,
} from './response/topic.response';
import { WorkerResponse } from './response/worker.response';

export const getTopicsDecorators = () => {
  return applyDecorators(
    Trace,
    ApiOperation({
      operationId: 'getTopics',
      description:
        'This will return the topics available in the Allora platform.',
      summary: 'Get Allora topics',
    }),
    ContinuationTokenDocs(),
    ApiParam({
      name: 'chain_id',
      enum: EAlloraChainId,
      description: 'The id of the Allora chain to get the topics for.',
    }),
    ApiQuery({
      name: 'user_id',
      type: String,
      description: 'The id of the user whose topics are to be retrieved.',
      required: false,
      example: '0x12353b14a22-e49e-43ce-a3b4-4b4c66dcfb58',
    }),
    ...ApiResponses([TopicResponse]),
  );
};

export const getTopicByIdDecorators = () => {
  return applyDecorators(
    Trace,
    ApiOperation({
      operationId: 'getTopicById',
      description: 'Get topic details by id',
      summary: 'Get topic details by id',
    }),
    ApiParam({
      name: 'chain_id',
      enum: EAlloraChainId,
      description: 'The id of the Allora chain to get the topic for.',
    }),
    ApiParam({
      name: 'topic_id',
      type: Number,
      description: 'The id of the topic to be retrieved.',
    }),
    ...ApiResponses([TopicResponse]),
  );
};

export const getTopWorkersDecorators = () => {
  return applyDecorators(
    Trace,
    ApiOperation({
      operationId: 'getTopWorkers',
      description: 'Get top workers by topic id',
      summary: 'Get top workers by topic id',
    }),
    ApiParam({
      name: 'chain_id',
      enum: EAlloraChainId,
      description: 'The id of the Allora chain.',
    }),
    ApiParam({
      name: 'topic_id',
      type: Number,
      description: 'The id of the topic to get top workers for.',
    }),
    ApiQuery({
      name: 'limit',
      type: Number,
      description: 'The number of top reputers to get.',
      required: true,
    }),
    ...ApiResponses([WorkerResponse]),
  );
};

export const getTopReputersDecorators = () => {
  return applyDecorators(
    Trace,
    ApiOperation({
      operationId: 'getTopReputers',
      description: 'Get top reputers by topic id',
      summary: 'Get top reputers by topic id',
    }),
    ApiParam({
      name: 'chain_id',
      enum: EAlloraChainId,
      description: 'The id of the Allora chain.',
    }),
    ApiParam({
      name: 'topic_id',
      type: Number,
      description: 'The id of the topic to get top reputers for.',
    }),
    ApiQuery({
      name: 'limit',
      type: Number,
      description: 'The number of top reputers to get.',
      required: true,
    }),
    ...ApiResponses([ReputerResponse]),
  );
};

export const getGeneralStatsDecorators = () => {
  return applyDecorators(
    Trace,
    ApiOperation({
      operationId: 'getGeneralStats',
      description: 'Get general stats by topic id',
      summary: 'Get general stats by topic id',
    }),
    ApiParam({
      name: 'chain_id',
      enum: EAlloraChainId,
      description: 'The id of the Allora chain.',
    }),
    ApiParam({
      name: 'topic_id',
      type: Number,
      description: 'The id of the topic to get general stats for.',
    }),
    ApiQuery({
      name: 'stat_type',
      type: String,
      description: 'The type of stats to get.',
      required: true,
    }),
    ...ApiResponses([TimeseriesDataPointResponse]),
  );
};

export const getWorkerLossesDecorators = () => {
  return applyDecorators(
    Trace,
    ApiOperation({
      operationId: 'getWorkerLosses',
      description: 'Get worker losses by worker address and topic id',
      summary: 'Get worker losses by worker address and topic id',
    }),
    ApiParam({
      name: 'chain_id',
      enum: EAlloraChainId,
      description: 'The id of the Allora chain.',
    }),
    ApiParam({
      name: 'topic_id',
      type: Number,
      description: 'The id of the topic to get worker losses for.',
    }),
    ApiQuery({
      name: 'worker_addresses',
      description: 'The addresses of the workers to get losses for.',
      schema: {
        type: 'array',
        items: {
          type: 'string',
          example: 'allo1qs6tt7gy5nyvnn5l0l6u2zh2t5phfsxzdd4ymu',
        },
      },
      required: true,
    }),
    ...ApiResponses([TimeseriesDataPointResponse]),
  );
};

export const getReputerLossesDecorators = () => {
  return applyDecorators(
    Trace,
    ApiOperation({
      operationId: 'getReputerLosses',
      description: 'Get reputer losses by reputer addresses and topic id',
      summary: 'Get reputer losses by reputer addresses and topic id',
    }),
    ApiParam({
      name: 'chain_id',
      enum: EAlloraChainId,
      description: 'The id of the Allora chain.',
    }),
    ApiParam({
      name: 'topic_id',
      type: Number,
      description: 'The id of the topic to get reputer ground truth for.',
    }),
    ApiQuery({
      name: 'reputer_addresses',
      description: 'The addresses of the reputers to get losses for.',
      schema: {
        type: 'array',
        items: {
          type: 'string',
          example: 'allo1qs6tt7gy5nyvnn5l0l6u2zh2t5phfsxzdd4ymu',
        },
      },
      required: true,
    }),
    ...ApiResponses([TimeseriesDataPointResponse]),
  );
};

export const getTopicGroundTruthTimeseriesDecorators = () => {
  return applyDecorators(
    Trace,
    ApiOperation({
      operationId: 'getTopicGroundTruthTimeseries',
      description: 'Get ground truth timeseries by topic id',
      summary: 'Get ground truth timeseries by topic id',
    }),
    ApiParam({
      name: 'chain_id',
      enum: EAlloraChainId,
      description: 'The id of the Allora chain.',
    }),
    ApiParam({
      name: 'topic_id',
      type: Number,
      description: 'The id of the topic to get ground truth timeseries for.',
    }),
    ApiQuery({
      name: 'from_timestamp',
      type: String,
      description: 'The timestamp to get ground truth timeseries from.',
      required: false,
    }),
    ContinuationTokenDocs(),
    ...ApiResponses([TimeseriesDataPointResponse]),
  );
};

export const getNetworkInferencesDecorators = () => {
  return applyDecorators(
    Trace,
    ApiOperation({
      operationId: 'getNetworkInferences',
      description: 'Get network inferences by topic id',
      summary: 'Get network inferences by topic id',
    }),
    ApiParam({
      name: 'chain_id',
      enum: EAlloraChainId,
      description: 'The id of the Allora chain.',
    }),
    ApiParam({
      name: 'topic_id',
      type: Number,
      description: 'The id of the topic to get network inferences for.',
    }),
    ContinuationTokenDocs(),
    ...ApiResponses([TimeseriesDataPointResponse]),
  );
};
