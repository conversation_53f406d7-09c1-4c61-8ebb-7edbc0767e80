import { TopicWorkerStats } from '@app/core/domain/allora/topic/topic-details';
import { ApiProperty } from '@nestjs/swagger';

export class WorkerResponse {
  @ApiProperty({
    type: String,
    description: 'Worker address',
    example: 'allo1lqsw2azx8s42jkgxerzf4nagvqe4a3jzx5qrud5',
  })
  worker_address: string;

  @ApiProperty({
    type: Number,
    description: 'Worker loss',
    example: 0.1,
  })
  loss: number;

  @ApiProperty({
    type: Number,
    description: 'Worker weight',
    example: 0.1,
  })
  score: number;

  @ApiProperty({
    type: Number,
    description: 'Worker allo earned',
    example: 100000000,
  })
  allo_earned: number;

  @ApiProperty({
    type: Number,
    description: 'Worker allo earned in the last 1 day',
    example: 1000000,
  })
  allo_earned_1d: number;

  @ApiProperty({
    type: Number,
    description: 'Worker allo earned in the last 3 days',
    example: 5000000,
  })
  allo_earned_3d: number;

  @ApiProperty({
    type: Number,
    description: 'Worker allo earned in the last 1 week',
    example: 10000000,
  })
  allo_earned_7d: number;

  constructor(worker: TopicWorkerStats) {
    this.worker_address = worker.address;
    this.loss = worker.loss;
    this.score = Number(worker.score);
    this.allo_earned = Number(worker.totalEarned);
    this.allo_earned_1d = Number(worker.alloEarned1d);
    this.allo_earned_3d = Number(worker.alloEarned3d);
    this.allo_earned_7d = Number(worker.alloEarned7d);
  }
}
