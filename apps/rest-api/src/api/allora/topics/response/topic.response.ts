import { TopicDetails } from '@app/core/domain/allora/topic/topic-details';
import { ApiProperty } from '@nestjs/swagger';

export type TimeseriesDataPoint = {
  x: number;
  y: number;
};

/* eslint-disable local-rules/snake-case-rest-params */
export class TimeseriesDataPointResponse {
  @ApiProperty({
    type: Number,
    description: 'x axis value',
    example: 1714857600,
  })
  x_value: number;
  @ApiProperty({
    type: Number,
    description: 'y axis value',
    example: 0.1,
  })
  y_value: number;

  constructor(dataPoint: TimeseriesDataPoint) {
    this.x_value = dataPoint.x;
    this.y_value = dataPoint.y;
  }
}

export class TopicResponse {
  @ApiProperty({
    type: Number,
    description: 'Topic id',
    example: 1,
  })
  topic_id: number;

  @ApiProperty({
    type: String,
    description: 'Topic name',
    example: 'Topic name',
  })
  topic_name: string;

  @ApiProperty({
    type: String,
    description: 'Topic description',
    example: 'Topic description',
  })
  description?: string | null;

  @ApiProperty({
    type: Number,
    description: "Topic's epoch length",
    example: 120,
  })
  epoch_length: number;

  @ApiProperty({
    type: Number,
    description: "Topic's ground truth lag",
    example: 120,
  })
  ground_truth_lag: number;

  @ApiProperty({
    type: String,
    description: 'Topic loss method',
    example: 'mean_squared_error',
  })
  loss_method: string;

  @ApiProperty({
    type: Number,
    description: 'Topic worker submission window',
    example: 120,
  })
  worker_submission_window: number;

  @ApiProperty({
    type: Number,
    description: 'Total number of workers providing inferences on the topic',
    example: 133,
  })
  worker_count: number;

  @ApiProperty({
    type: Number,
    description:
      'Total number of reputers providing reputation data on the topic',
    example: 20,
  })
  reputer_count: number;

  @ApiProperty({
    type: Number,
    description: 'Total amount of allo staked on the topic',
    example: '8.324',
  })
  total_staked_allo: number;

  @ApiProperty({
    type: Number,
    description: 'Total amount of topic funds emitted denominated in allo',
    example: '12.34',
  })
  total_emissions_allo: number;

  @ApiProperty({
    type: Boolean,
    description: 'Topic is active',
    example: true,
  })
  is_active: boolean | null;

  @ApiProperty({
    type: Boolean,
    description: 'Topic is endorsed',
    example: true,
  })
  is_endorsed: boolean;

  @ApiProperty({
    type: Number,
    description: 'Forge competition id',
    example: 1,
  })
  forge_competition_id: number | null;

  @ApiProperty({
    type: String,
    description: 'Forge competition start date (ISO-8601)',
    example: '2024-01-01T00:00:00.000Z',
  })
  forge_competition_start_date: string | null;

  @ApiProperty({
    type: String,
    description: 'Forge competition end date (ISO-8601)',
    example: '2024-01-10T00:00:00.000Z',
  })
  forge_competition_end_date: string | null;

  @ApiProperty({
    type: String,
    description: 'Last updated timestamp (ISO-8601)',
    example: '2024-03-01T11:22:33.000Z',
  })
  updated_at: string;

  constructor(topic: TopicDetails) {
    this.topic_id = topic.topicId;
    this.topic_name = topic.topicName;
    this.description = topic.description;
    this.epoch_length = topic.epochLength;
    this.ground_truth_lag = topic.groundTruthLag;
    this.loss_method = topic.lossMethod;
    this.worker_submission_window = topic.workerSubmissionWindow;
    this.worker_count = topic.workerCount;
    this.reputer_count = topic.reputerCount;
    this.total_staked_allo = Number(topic.totalStakedAllo);
    this.total_emissions_allo = Number(topic.totalEmissionsAllo);
    this.is_active = topic.isActive;
    this.is_endorsed = topic.isEndorsed;
    this.forge_competition_id = topic.forgeCompetitionId;
    this.forge_competition_start_date = topic.forgeCompetitionStartDate;
    this.forge_competition_end_date = topic.forgeCompetitionEndDate;
    this.updated_at = topic.updatedAt;
  }
}
