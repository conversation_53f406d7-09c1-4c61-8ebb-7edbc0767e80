import { NetworkInferenceData } from '@app/core/domain/allora/network-inference/network-inference';
import { ApiProperty } from '@nestjs/swagger';

export class NetworkInferenceTimeseriesPoint {
  @ApiProperty({
    type: String,
    description: 'Network combined inference value',
    example: '0.1',
  })
  combined_value: string;

  @ApiProperty({
    type: String,
    description: 'Network naive inference value',
    example: '0.1',
  })
  naive_value: string;

  @ApiProperty({
    type: Number,
    description: 'Inference timestamp in milliseconds',
    example: 1714857600000,
  })
  timestamp: number;

  constructor(networkInference: NetworkInferenceData) {
    this.combined_value = networkInference.combinedValue;
    this.naive_value = networkInference.naiveValue;
    this.timestamp = networkInference.timestamp * 1000;
  }
}
