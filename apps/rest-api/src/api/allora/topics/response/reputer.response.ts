import { TopicWorkerStats } from '@app/core/domain/allora/topic/topic-details';
import { ApiProperty } from '@nestjs/swagger';

export class ReputerResponse {
  @ApiProperty({
    type: String,
    description: 'Reputer address',
    example: 'allo1lqsw2azx8s42jkgxerzf4nagvqe4a3jzx5qrud5',
  })
  reputer_address: string;

  @ApiProperty({
    type: Number,
    description: 'Reputer provided ground truth',
    example: 0.1,
  })
  loss: number;

  @ApiProperty({
    type: Number,
    description: 'Reputer weight',
    example: 0.1,
  })
  score: number;

  @ApiProperty({
    type: Number,
    description: 'Reputer Allo earned',
    example: 100,
  })
  allo_earned: number;

  @ApiProperty({
    type: Number,
    description: 'Reputer allo earned in the last 1 day',
    example: 10,
  })
  allo_earned_1d: number;

  @ApiProperty({
    type: Number,
    description: 'Reputer allo earned in the last 3 days',
    example: 50,
  })
  allo_earned_3d: number;

  @ApiProperty({
    type: Number,
    description: 'Reputer allo earned in the last 1 week',
    example: 100,
  })
  allo_earned_7d: number;

  constructor(reputer: TopicWorkerStats) {
    this.reputer_address = String(reputer.address);
    this.loss = Number(reputer.loss);
    this.score = Number(reputer.score);
    this.allo_earned = Number(reputer.totalEarned);
    this.allo_earned_1d = Number(reputer.alloEarned1d);
    this.allo_earned_3d = Number(reputer.alloEarned3d);
    this.allo_earned_7d = Number(reputer.alloEarned7d);
  }
}
