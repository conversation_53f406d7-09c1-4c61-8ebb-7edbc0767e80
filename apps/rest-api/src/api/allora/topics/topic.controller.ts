import {
  ALLOR<PERSON>_USER_QUERY_REPOSITORY,
  AlloraUserQueryRepository,
} from '@app/core/domain/allora/allora-user/allora-user.query.repository';
import {
  AlloraChainConfig,
  EAlloraChainId,
} from '@app/core/domain/allora/chain/allora-chain-config';
import { ALLORA_GROUND_TRUTH_QUERY_REPOSITORY } from '@app/core/domain/allora/ground-truth/ground-truth.query.repository';
import { ALLORA_NETWORK_INFERENCE_QUERY_REPOSITORY } from '@app/core/domain/allora/network-inference/network-inference.query.repository';
import { ALLORA_TOPIC_QUERY_REPOSITORY } from '@app/core/domain/allora/topic/topic.query.repository';
import {
  TopicsContinuationPageOutput,
  TopicSortingOptions,
} from '@app/core/domain/allora/topic/topic-details';
import { ContinuationToken } from '@app/core/domain/continuation-token/continuation-token';
import { CursorPagination } from '@app/core/domain/value-object/cursor-pagination';
import { SequelizeGroundTruthQueryRepository } from '@app/core/infra/repository/allora/ground-truth/query/sequelize.ground-truth.query.repository';
import { SequelizeNetworkInferenceQueryRepository } from '@app/core/infra/repository/allora/network-inference/query/sequelize.network-inference.query.repository';
import { SequelizeTopicQueryRepository } from '@app/core/infra/repository/allora/topic/query/sequelize.topic.query.repository';
import {
  Controller,
  Get,
  Inject,
  NotFoundException,
  Param,
  Query,
} from '@nestjs/common';
import { ParseArrayPipe } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';

import {
  ETopicStatType,
  GetGroundTruthTimeseriesQueryDTO,
  GetNetworkInferencesQueryDTO,
  GetTopicsQueryDTO,
} from './dto/topics.dto';
import { NetworkInferenceTimeseriesPoint } from './response/networkInference.response';
import { ReputerResponse } from './response/reputer.response';
import {
  TimeseriesDataPointResponse,
  TopicResponse,
} from './response/topic.response';
import { WorkerResponse } from './response/worker.response';
import * as Docs from './topic.docs';

@Controller('/allora/:chain_id/topics')
@ApiTags('Allora Topics')
export class TopicController {
  constructor(
    @Inject(ALLORA_USER_QUERY_REPOSITORY)
    private readonly alloraUserQueryRepository: AlloraUserQueryRepository,
    @Inject(ALLORA_TOPIC_QUERY_REPOSITORY)
    private readonly sequelizeTopicQueryRepository: SequelizeTopicQueryRepository,
    @Inject(ALLORA_GROUND_TRUTH_QUERY_REPOSITORY)
    private readonly sequelizeGroundTruthQueryRepository: SequelizeGroundTruthQueryRepository,
    @Inject(ALLORA_NETWORK_INFERENCE_QUERY_REPOSITORY)
    private readonly sequelizeNetworkInferenceQueryRepository: SequelizeNetworkInferenceQueryRepository,
  ) {}

  @Get()
  @Docs.getTopicsDecorators()
  async getTopics(
    @Param('chain_id') chainId: EAlloraChainId,
    @Query() query: GetTopicsQueryDTO,
  ) {
    const filters = query.continuation_token
      ? {
          continuationToken: new ContinuationToken(query.continuation_token),
          userId: query.user_id,
        }
      : { userId: query.user_id };

    const pagination: CursorPagination = {
      continuationToken: filters.continuationToken,
    };

    // use a default sorting option
    const sortingOption = TopicSortingOptions.EPOCH_LENGTH;

    let res: TopicsContinuationPageOutput;
    if (filters.userId) {
      // get all topics this user is participating in
      const alloraUser = await this.alloraUserQueryRepository.getAlloraUserById(
        filters.userId,
      );
      if (!alloraUser || !alloraUser.cosmosAddress) {
        throw new NotFoundException('User not found');
      }
      res =
        await this.sequelizeTopicQueryRepository.getUserParticipatingTopicsWithStats(
          alloraUser.cosmosAddress,
          new AlloraChainConfig(chainId),
          sortingOption,
          pagination,
        );
    } else {
      res = await this.sequelizeTopicQueryRepository.getAllTopicsWithStats(
        new AlloraChainConfig(chainId),
        sortingOption,
        pagination,
      );
    }

    // map to response
    return {
      topics: res.topics.map((topic) => new TopicResponse(topic)),
      continuation_token: res.continuationToken?.value ?? null,
    };
  }

  @Get('/:topic_id')
  @Docs.getTopicByIdDecorators()
  async getTopic(
    @Param('chain_id') chainId: EAlloraChainId,
    @Param('topic_id') topicId: number,
  ) {
    const res = await this.sequelizeTopicQueryRepository.getTopicWithStatsById(
      topicId,
      new AlloraChainConfig(chainId),
    );
    if (!res) {
      throw new NotFoundException('Topic not found');
    }
    return new TopicResponse(res);
  }

  @Get('/:topic_id/top-workers')
  @Docs.getTopWorkersDecorators()
  async getTopicTopWorkers(
    @Param('chain_id') chainId: EAlloraChainId,
    @Param('topic_id') topicId: number,
    @Query('limit') limit: number,
  ) {
    const queryLimit = limit ?? 5;
    const res = await this.sequelizeTopicQueryRepository.getTopicWorkerStats(
      topicId,
      new AlloraChainConfig(chainId),
      queryLimit,
    );
    const workers = res.map((worker) => new WorkerResponse(worker));

    return {
      workers: workers,
      continuation_token: null,
    };
  }

  @Get('/:topic_id/top-reputers')
  @Docs.getTopReputersDecorators()
  async getTopicTopReputers(
    @Param('chain_id') chainId: EAlloraChainId,
    @Param('topic_id') topicId: number,
    @Query('limit') limit: number,
  ) {
    const queryLimit = limit ?? 5;
    const res = await this.sequelizeTopicQueryRepository.getTopicReputerStats(
      topicId,
      new AlloraChainConfig(chainId),
      queryLimit,
    );
    return {
      reputers: res.map((reputer) => new ReputerResponse(reputer)),
      continuation_token: null,
    };
  }

  @Get('/:topic_id/general-stats')
  @Docs.getGeneralStatsDecorators()
  async getTopicGeneralStats(
    @Param('chain_id') chainId: EAlloraChainId,
    @Param('topic_id') topicId: number,
    @Query('stat_type') statType: ETopicStatType,
  ) {
    // currently only network losses is supported
    if (statType === ETopicStatType.NETWORK_LOSS) {
      const losses =
        await this.sequelizeTopicQueryRepository.getNetworkLossesByTopicId(
          topicId,
          new AlloraChainConfig(chainId),
        );
      // map to response
      return {
        data: losses.map(
          (dataPoint) =>
            new TimeseriesDataPointResponse({
              x: Number(dataPoint.timestamp) * 1000, // turn to milliseconds
              y: Number(dataPoint.value),
            }),
        ),
        continuation_token: null,
      };
    } else if (statType === ETopicStatType.WORKERS_COUNT) {
      const workerCounts =
        await this.sequelizeTopicQueryRepository.getWorkerCountsByTopicId(
          topicId,
          new AlloraChainConfig(chainId),
        );
      // map to response
      return {
        data: workerCounts.map(
          (dataPoint) =>
            new TimeseriesDataPointResponse({
              x: Number(dataPoint.timestamp) * 1000, // turn to milliseconds
              y: Number(dataPoint.value),
            }),
        ),
        continuation_token: null,
      };
    } else if (statType === ETopicStatType.EMISSIONS) {
      const emissions =
        await this.sequelizeTopicQueryRepository.getEmissionsByTopicId(
          topicId,
          new AlloraChainConfig(chainId),
        );
      // map to response
      return {
        data: emissions.map(
          (dataPoint) =>
            new TimeseriesDataPointResponse({
              x: Number(dataPoint.timestamp) * 1000, // turn to milliseconds
              y: Number(dataPoint.value),
            }),
        ),
        continuation_token: null,
      };
    } else if (statType === ETopicStatType.TOTAL_STAKED) {
      const total_staked =
        await this.sequelizeTopicQueryRepository.getTotalStakedByTopicId(
          topicId,
          new AlloraChainConfig(chainId),
        );
      // map to response
      return {
        data: total_staked.map(
          (dataPoint) =>
            new TimeseriesDataPointResponse({
              x: Number(dataPoint.timestamp) * 1000, // turn to milliseconds
              y: Number(dataPoint.value),
            }),
        ),
        continuation_token: null,
      };
    } else {
      throw new NotFoundException('Stat type not supported');
    }
  }

  @Get('/:topic_id/worker-losses')
  @Docs.getWorkerLossesDecorators()
  async getTopicWorkerLosses(
    @Param('chain_id') chainId: EAlloraChainId,
    @Param('topic_id') topicId: number,
    @Query(
      'worker_addresses',
      new ParseArrayPipe({ items: String, separator: ',' }),
    )
    workerAddresses: string[],
  ) {
    const losses =
      await this.sequelizeTopicQueryRepository.getWorkerLossesByTopicId(
        topicId,
        workerAddresses,
        new AlloraChainConfig(chainId),
      );
    // categorize by worker address
    const workerLosses = new Map<string, TimeseriesDataPointResponse[]>();
    for (const loss of losses) {
      const address = loss.address;
      if (!workerLosses.has(address)) {
        workerLosses.set(address, []);
      }
      workerLosses.get(address)?.push(
        new TimeseriesDataPointResponse({
          x: Number(loss.timestamp) * 1000, // turn to milliseconds
          y: Number(loss.value),
        }),
      );
    }
    // map to response
    const data = Array.from(workerLosses.entries());
    return data.map(([address, data]) => ({
      address,
      data: data,
    }));
  }

  @Get('/:topic_id/reputer-losses')
  @Docs.getReputerLossesDecorators()
  async getTopicReputerLosses(
    @Param('chain_id') chainId: EAlloraChainId,
    @Param('topic_id') topicId: number,
    @Query(
      'reputer_addresses',
      new ParseArrayPipe({ items: String, separator: ',' }),
    )
    reputerAddresses: string[],
  ) {
    const losses =
      await this.sequelizeTopicQueryRepository.getReputerLossesByTopicId(
        topicId,
        reputerAddresses,
        new AlloraChainConfig(chainId),
      );
    // categorize by reputer address
    const reputerLosses = new Map<string, TimeseriesDataPointResponse[]>();
    for (const loss of losses) {
      const address = loss.address;
      if (!reputerLosses.has(address)) {
        reputerLosses.set(address, []);
      }
      reputerLosses.get(address)?.push(
        new TimeseriesDataPointResponse({
          x: Number(loss.timestamp) * 1000, // turn to milliseconds
          y: Number(loss.value),
        }),
      );
    }
    // map to response
    const data = Array.from(reputerLosses.entries());
    return data.map(([address, data]) => ({
      address,
      data: data,
    }));
  }

  @Get('/:topic_id/ground-truths')
  @Docs.getTopicGroundTruthTimeseriesDecorators()
  async getTopicGroundTruthTimeseries(
    @Param('chain_id') chainId: EAlloraChainId,
    @Param('topic_id') topicId: number,
    @Query() query: GetGroundTruthTimeseriesQueryDTO,
  ) {
    const pagination: CursorPagination = {
      continuationToken: query.continuation_token
        ? new ContinuationToken(query.continuation_token)
        : undefined,
    };

    const res =
      await this.sequelizeGroundTruthQueryRepository.getGroundTruthsByTopicId(
        topicId,
        new AlloraChainConfig(chainId),
        pagination,
        {
          fromTimestamp: query.from_timestamp
            ? Math.floor(Number(query.from_timestamp) / 1000) // turn to seconds
            : undefined,
        },
      );

    // map to response
    return {
      data: res.data.map(
        (data) =>
          new TimeseriesDataPointResponse({
            x: Number(data.reputationTimestamp) * 1000, // turn to milliseconds
            y: Number(data.gtValue),
          }),
      ),
      continuation_token: res.continuationToken?.value ?? null,
    };
  }

  @Get('/:topic_id/network-inferences')
  @Docs.getNetworkInferencesDecorators()
  async getNetworkInferences(
    @Param('chain_id') chainId: EAlloraChainId,
    @Param('topic_id') topicId: number,
    @Query() query: GetNetworkInferencesQueryDTO,
  ) {
    const pagination: CursorPagination = {
      continuationToken: query.continuation_token
        ? new ContinuationToken(query.continuation_token)
        : undefined,
    };

    const res =
      await this.sequelizeNetworkInferenceQueryRepository.getNetworkInferencesByTopicId(
        topicId,
        new AlloraChainConfig(chainId),
        pagination,
        {
          fromTimestamp: query.from_timestamp
            ? Math.floor(Number(query.from_timestamp) / 1000) // turn to seconds
            : undefined,
        },
      );

    return {
      data: res.data.map((data) => new NetworkInferenceTimeseriesPoint(data)),
      continuation_token: res.continuationToken?.value ?? null,
    };
  }
}
