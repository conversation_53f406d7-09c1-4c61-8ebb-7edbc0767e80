import { ALLORA_USER_QUERY_REPOSITORY } from '@app/core/domain/allora/allora-user/allora-user.query.repository';
import { ALLORA_GROUND_TRUTH_QUERY_REPOSITORY } from '@app/core/domain/allora/ground-truth/ground-truth.query.repository';
import { ALLORA_INDEXER_QUERY_REPOSITORY } from '@app/core/domain/allora/indexer/indexer.query.repository';
import { ALLORA_NETWORK_INFERENCE_QUERY_REPOSITORY } from '@app/core/domain/allora/network-inference/network-inference.query.repository';
import { ALLORA_TOPIC_QUERY_REPOSITORY } from '@app/core/domain/allora/topic/topic.query.repository';
import { Test, TestingModule } from '@nestjs/testing';

import { TopicController } from './topic.controller';

describe('TopicController', () => {
  let controller: TopicController;

  const mockSequelizeIndexerQueryRepository = {
    getTopics: jest.fn(),
    getUserParticipatingTopics: jest.fn(),
  };

  const mockAlloraUserQueryRepository = {
    getAlloraUserById: jest.fn(),
  };

  const mockSequelizeTopicQueryRepositoryModule = {
    getWorkerLossesByTopicId: jest.fn(),
    getReputerLossesByTopicId: jest.fn(),
    getNetworkLossesByTopicId: jest.fn(),
  };

  const mockSequelizeGroundTruthQueryRepository = {
    getGroundTruthsByTopicId: jest.fn(),
  };

  const mockSequelizeNetworkInferenceQueryRepository = {
    getNetworkInferencesByTopicId: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [TopicController],
      providers: [
        {
          provide: ALLORA_INDEXER_QUERY_REPOSITORY,
          useValue: mockSequelizeIndexerQueryRepository,
        },
        {
          provide: ALLORA_USER_QUERY_REPOSITORY,
          useValue: mockAlloraUserQueryRepository,
        },
        {
          provide: ALLORA_TOPIC_QUERY_REPOSITORY,
          useValue: mockSequelizeTopicQueryRepositoryModule,
        },
        {
          provide: ALLORA_GROUND_TRUTH_QUERY_REPOSITORY,
          useValue: mockSequelizeGroundTruthQueryRepository,
        },
        {
          provide: ALLORA_NETWORK_INFERENCE_QUERY_REPOSITORY,
          useValue: mockSequelizeNetworkInferenceQueryRepository,
        },
      ],
    }).compile();
    controller = module.get<TopicController>(TopicController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
});
