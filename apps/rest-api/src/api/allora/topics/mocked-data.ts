//TODO: !!remove @visx/mock-data package when removing the mocked data!!
import bitcoinPrice, {
  BitcoinPrice,
} from '@visx/mock-data/lib/mocks/bitcoinPrice';
import cityTemperature, {
  CityTemperature,
} from '@visx/mock-data/lib/mocks/cityTemperature';
import { ethers } from 'ethers';
import { v4 } from 'uuid';

import { ETopicStatType } from './dto/topics.dto';

const generateMockedUserDetails = (alloraAddress: string) => {
  const randomDate = () => {
    const now = new Date();
    const oneDay = 24 * 60 * 60 * 1000;
    const randomTime = Math.floor(Math.random() * (7 * oneDay)); // 7 days ago
    return new Date(now.getTime() - randomTime);
  };

  const workersTopics =
    Array.from({ length: Math.floor(Math.random() * 20) }, (_, i) => ({
      topicId: i + 1,
      topicName: `Sample Topic ${i + 1}`,
      epochLength: Math.floor(Math.random() * 1000) + 100,
      topicArg: Math.floor(Math.random() * 10) + 1,
      workerCount: Math.floor(Math.random() * 100) + 1,
      reputerCount: Math.floor(Math.random() * 10) + 1,
      totalStakedAllo: Math.floor(Math.random() * 1000),
      totalEmissionsAllo: Math.floor(Math.random() * 1000),
      updatedAt: randomDate().toISOString(),
      createdAt: randomDate().toISOString(),
    })) ?? [];
  const reputersTopics =
    Array.from({ length: Math.floor(Math.random() * 20) }, (_, i) => ({
      topicId: i + 1,
      topicName: `Sample Topic ${i + 1}`,
      topicArg: Math.floor(Math.random() * 10) + 1,
      reputerCount: Math.floor(Math.random() * 10) + 1,
      totalStakedAllo: Math.floor(Math.random() * 1000),
      totalEmissionsAllo: (Math.random() * 1000).toFixed(2),
      updatedAt: randomDate().toISOString(),
      createdAt: randomDate().toISOString(),
    })) ?? [];

  return {
    user: {
      id: v4(),
      cosmosAddress: alloraAddress,
      evmAddress: null,
      privyId: null,
      createdAt: new Date().toISOString(),
    },
    workersTopics,
    reputersTopics,
  };
};

const getMockedTimeseriesValues = (timeseriesType: ETopicStatType) => {
  switch (timeseriesType) {
    case ETopicStatType.TOTAL_STAKED:
      return {
        data: bitcoinPrice.prices.map((d: BitcoinPrice) => {
          const price = d.price;
          return {
            x: new Date().setDate(
              new Date().getDate() -
                (bitcoinPrice.prices.length -
                  bitcoinPrice.prices.indexOf(d) -
                  1),
            ),
            y: ethers.utils.parseEther(`${price}`).toString(),
          };
        }),
        continuation_token: null,
      };
    case ETopicStatType.EMISSIONS:
      return {
        data: cityTemperature.map((d: CityTemperature) => {
          const value = Number(d['New York']);
          return {
            x: new Date(d.date).getTime(),
            y: ethers.utils.parseEther(`${value}`).toString(),
          };
        }),
        continuation_token: null,
      };
    case ETopicStatType.WORKERS_COUNT:
      return {
        data: cityTemperature.map((d: CityTemperature) => {
          const value = Number(d['Austin']);
          return {
            x: new Date(
              new Date().setDate(
                new Date().getDate() -
                  (cityTemperature.length - cityTemperature.indexOf(d) - 1),
              ),
            ),
            y: ethers.utils.parseEther(`${Math.floor(value)}`).toString(),
          };
        }),
        continuation_token: null,
      };
    default:
      return {
        data: [],
        continuation_token: null,
      };
  }
};

export const mockedData = {
  topic_general_stats: (statType: ETopicStatType) =>
    getMockedTimeseriesValues(statType),
  mocked_user_details: (alloraAddress: string) =>
    generateMockedUserDetails(alloraAddress),
  mocked_user_balance: (alloraAddress: string) => {
    return {
      data: Array.from({ length: 10 }, (_, i) => ({
        x: Date.now() - (i + 1) * 86400000,
        y: ethers.utils.parseEther(Math.random().toString()).toString(),
      })).sort((a, b) => a.x - b.x),
      continuation_token: null,
    };
  },
};
