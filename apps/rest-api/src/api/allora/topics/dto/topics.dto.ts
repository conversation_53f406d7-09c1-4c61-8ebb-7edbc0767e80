import { IsOptional, IsString } from 'class-validator';

import { CursorPaginated } from '../../../../common/dto/Paginated';

export class GetTopicsQueryDTO extends CursorPaginated {
  @IsOptional()
  @IsString()
  user_id?: string;
}

export class GetGroundTruthTimeseriesQueryDTO extends CursorPaginated {
  @IsOptional()
  @IsString()
  from_timestamp?: string;
}

export class GetNetworkInferencesQueryDTO extends CursorPaginated {
  @IsOptional()
  @IsString()
  from_timestamp?: string;
}

export enum ETopicStatType {
  TOTAL_STAKED = 'total_staked',
  EMISSIONS = 'emissions',
  WORKERS_COUNT = 'workers_count',
  NETWORK_LOSS = 'network_loss',
}
