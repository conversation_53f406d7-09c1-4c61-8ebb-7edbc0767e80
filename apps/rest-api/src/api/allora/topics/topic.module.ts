import { AlloraUserQueryRepositoryModule } from '@app/core/domain/allora/allora-user/allora-user.query.repository.module';
import { GroundTruthQueryRepositoryModule } from '@app/core/domain/allora/ground-truth/ground-truth.query.repository.module';
import { NetworkInferenceQueryRepositoryModule } from '@app/core/domain/allora/network-inference/network-inference.query.repository.module';
import { TopicQueryRepositoryModule } from '@app/core/domain/allora/topic/topic.query.repository.module';
import { ContinuationTokenRepositoryModule } from '@app/core/domain/continuation-token/continuation-token.repository.module';
import { Module } from '@nestjs/common';

import { TopicController } from './topic.controller';

@Module({
  imports: [
    AlloraUserQueryRepositoryModule,
    TopicQueryRepositoryModule,
    GroundTruthQueryRepositoryModule,
    NetworkInferenceQueryRepositoryModule,
    ContinuationTokenRepositoryModule,
  ],
  controllers: [TopicController],
  exports: [],
})
export class TopicModule {}
