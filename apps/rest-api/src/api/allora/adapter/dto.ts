import * as CustomValidators from '@app/core/app/validation/validators';
import { IsOptional, IsString, Validate } from 'class-validator';

class GetDataForAdapterParamDto {
  @IsString()
  @Validate(CustomValidators.AlphanumericOrLimitedSet)
  chain_slug: string;
}

class GetDataForAdapterQueryDto {
  @IsString()
  @Validate(CustomValidators.AlphanumericOrLimitedSet)
  @IsOptional()
  extra_data?: string;

  @IsString()
  @Validate(CustomValidators.NumericString)
  allora_topic_id: string;
}

export class SolicitInferencesForAdapterParamDto extends GetDataForAdapterParamDto {}

export class SolicitInferencesForAdapterQueryDto extends GetDataForAdapterQueryDto {
  @IsString()
  @Validate(CustomValidators.Alphanumeric)
  inference_value_type = 'uint256'; // default value = uint256
}
