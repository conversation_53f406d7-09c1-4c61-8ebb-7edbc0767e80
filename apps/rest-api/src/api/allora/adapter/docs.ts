import { ADAPTER_SENDER_PUBLIC_KEY } from '@app/core/config/env';
import { CustomApiOperation } from '@app/core/infra/utils/custom-api-operation.decorator';
import { applyDecorators } from '@nestjs/common';
import { ApiParam, ApiQuery } from '@nestjs/swagger';
import { ApiKey } from 'apps/rest-api/src/common/decorator/api-key.decorator';
import { ApiResponses } from 'apps/rest-api/src/common/decorator/api-responses.decorator';

import { AdapterNumericDataEndpointResponse } from './response';

export const solicitInferencesForAdapterDecorators = () => {
  return applyDecorators(
    CustomApiOperation({
      description: `Query the active inference worker nodes of the Allora network for their newest inferences,
      sign them, then format them to be onboarded to an Adapter contract on a given target chain.
      The timestamp will be set to 5 minutes before request time to prevent this response from being erroneously reverted (for being too early).
      The signer on Sepolia is ${ADAPTER_SENDER_PUBLIC_KEY}`,
      summary: 'Solicit Inferences for Adapter',
    }),
    Api<PERSON>ey(),
    ApiParam({
      name: 'chain_slug',
      type: String,
      required: true,
      description:
        'Slug of the chain on which the target adapter contract is deployed',
      example: 'ethereum-11155111',
    }),
    ApiQuery({
      name: 'allora_topic_id',
      type: String,
      required: true,
      description: 'Id of the topic in the Allora L1',
      example: '8',
    }),
    ApiQuery({
      name: 'inference_value_type',
      type: String,
      required: true,
      description: `Data type of the value of the inference. Default: \`uint256\`.`,
      example: 'uint256',
    }),
    ApiQuery({
      name: 'extra_data',
      type: String,
      required: false,
      description: `Useful for specifying subtopics e.g. to get the appraisal of asset \`0x.../1465\` of an NFT collection.
      Pass in any arbitrary string. This string will be hashed with keccak256 in the returned result.`,
      example: 'ETH',
    }),
    ...ApiResponses([AdapterNumericDataEndpointResponse]),
  );
};
