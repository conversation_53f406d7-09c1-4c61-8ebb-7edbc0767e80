import { SolicitInferencesForAdapterUseCase } from '@app/core/app/use-case/allora/solicit-inferences-for-adapter/use-case';
import { TopicId } from '@app/core/domain/allora/topic/topic-id';
import { Controller, Get, Inject, Logger, Param, Query } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';

import * as Docs from './docs';
import * as DTO from './dto';
import {
  AdapterNumericDataEndpointResponse,
  AdapterNumericDataEndpointResponseFactory,
} from './response';

@Controller('/allora/adapter')
@ApiTags('Allora Adapter - Deprecated - just for PancakeSwap')
export class AdapterController {
  private readonly logger = new Logger(AdapterController.name);
  DEBUG = false;

  constructor(
    @Inject(SolicitInferencesForAdapterUseCase)
    private readonly solicitInferencesUseCase: SolicitInferencesForAdapterUseCase,
  ) {}

  @Get(':chain_slug')
  @Docs.solicitInferencesForAdapterDecorators()
  async solicitInferencesForAdapter(
    @Param() param: DTO.SolicitInferencesForAdapterParamDto,
    @Query() query: DTO.SolicitInferencesForAdapterQueryDto,
  ): Promise<AdapterNumericDataEndpointResponse> {
    this.logger.log('allora/adapter/:chain_slug');
    const signed = await this.solicitInferencesUseCase.execute({
      chainSlug: param.chain_slug,
      alloraTopicId: new TopicId(query.allora_topic_id),
      inferenceValueType: query.inference_value_type,
      extraData: query.extra_data,
    });
    /**
     * Later: implement chain of responsibility or strategy pattern to get the right response factory based on inferenceValueType
     */
    return AdapterNumericDataEndpointResponseFactory.fromGetAdapterNumericDataResponse(
      signed.signedNumericData,
    );
  }
}
