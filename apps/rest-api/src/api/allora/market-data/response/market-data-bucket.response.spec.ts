import { MarketDataBucket } from '@app/core/domain/allora/market-data/market-data-bucket';

import { MarketDataBucketResponse } from './market-data-bucket.response';

describe('MarketDataBucketResponse', () => {
  test('build response [pending]', () => {
    const bucket = new MarketDataBucket({
      id: BigInt(11),
      ticker: 'btcusd',
      start: '2025-01-01',
      end: '2025-01-31',
      state: 'pending',
      availability: 0,
      periodicity: 'month',
      s3ObjectKey: null,
    });

    const response = new MarketDataBucketResponse(bucket, null);

    expect(response).toHaveProperty('ticker', 'btcusd');
    expect(response).toHaveProperty('start', '2025-01-01');
    expect(response).toHaveProperty('end', '2025-01-31');
    expect(response).toHaveProperty('state', 'processing');
    expect(response).toHaveProperty('availability', 0);
    expect(response).toHaveProperty('file_format', 'csv');
    expect(response).toHaveProperty('download_url', null);
  });

  test('build response [available]', () => {
    const bucket = new MarketDataBucket({
      id: BigInt(11),
      ticker: 'btcusd',
      start: '2025-01-01',
      end: '2025-01-31',
      state: 'available',
      availability: 0.95,
      periodicity: 'month',
      s3ObjectKey: null,
    });

    const response = new MarketDataBucketResponse(bucket, null);

    expect(response).toHaveProperty('ticker', 'btcusd');
    expect(response).toHaveProperty('start', '2025-01-01');
    expect(response).toHaveProperty('end', '2025-01-31');
    expect(response).toHaveProperty('state', 'processing');
    expect(response).toHaveProperty('availability', 0.95);
    expect(response).toHaveProperty('file_format', 'csv');
    expect(response).toHaveProperty('download_url', null);
  });

  test('build response [paused]', () => {
    const bucket = new MarketDataBucket({
      id: BigInt(11),
      ticker: 'btcusd',
      start: '2025-01-01',
      end: '2025-01-31',
      state: 'paused',
      availability: 0,
      periodicity: 'month',
      s3ObjectKey: null,
    });

    const response = new MarketDataBucketResponse(bucket, null);

    expect(response).toHaveProperty('ticker', 'btcusd');
    expect(response).toHaveProperty('start', '2025-01-01');
    expect(response).toHaveProperty('end', '2025-01-31');
    expect(response).toHaveProperty('state', 'processing');
    expect(response).toHaveProperty('availability', 0);
    expect(response).toHaveProperty('file_format', 'csv');
    expect(response).toHaveProperty('download_url', null);
  });

  test('build response [unavailable]', () => {
    const bucket = new MarketDataBucket({
      id: BigInt(11),
      ticker: 'btcusd',
      start: '2025-01-01',
      end: '2025-01-31',
      state: 'unavailable',
      availability: 0,
      periodicity: 'month',
      s3ObjectKey: null,
    });

    const response = new MarketDataBucketResponse(bucket, null);

    expect(response).toHaveProperty('ticker', 'btcusd');
    expect(response).toHaveProperty('start', '2025-01-01');
    expect(response).toHaveProperty('end', '2025-01-31');
    expect(response).toHaveProperty('state', 'unavailable');
    expect(response).toHaveProperty('availability', 0);
    expect(response).toHaveProperty('file_format', 'csv');
    expect(response).toHaveProperty('download_url', null);
  });

  test('build response [ready]', () => {
    const bucket = new MarketDataBucket({
      id: BigInt(11),
      ticker: 'btcusd',
      start: '2025-01-01',
      end: '2025-01-31',
      state: 'ready',
      availability: 1,
      periodicity: 'month',
      s3ObjectKey: null,
    });

    const downloadUrl = 'https://foo.bar.example.com/download.csv';

    const response = new MarketDataBucketResponse(bucket, downloadUrl);

    expect(response).toHaveProperty('ticker', 'btcusd');
    expect(response).toHaveProperty('start', '2025-01-01');
    expect(response).toHaveProperty('end', '2025-01-31');
    expect(response).toHaveProperty('state', 'ready');
    expect(response).toHaveProperty('availability', 1);
    expect(response).toHaveProperty('file_format', 'csv');
    expect(response).toHaveProperty('download_url', downloadUrl);
  });
});
