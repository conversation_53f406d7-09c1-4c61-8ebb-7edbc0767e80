import { MarketData } from '@app/core/domain/allora/market-data/market-data';
import { ApiProperty } from '@nestjs/swagger';

export class MarketDataResponse {
  @ApiProperty({
    type: String,
    description: 'Ticker symbol of the market data',
    example: 'ETHUSD',
  })
  ticker: string;

  @ApiProperty({
    type: String,
    description: 'Exchange code where the data was fetched',
    example: 'GDAX',
  })
  exchange_code: string;

  @ApiProperty({
    type: String,
    description: 'Date and time of the market data',
    example: '2024-10-24T15:05:00+00:00',
  })
  date: string;

  @ApiProperty({
    type: Number,
    description: 'Opening price',
    example: 2521.8,
  })
  open: number;

  @ApiProperty({
    type: Number,
    description: 'Highest price',
    example: 2521.81,
  })
  high: number;

  @ApiProperty({
    type: Number,
    description: 'Lowest price',
    example: 2516.87,
  })
  low: number;

  @ApiProperty({
    type: Number,
    description: 'Closing price',
    example: 2517.79,
  })
  close: number;

  @ApiProperty({
    type: Number,
    description: 'Number of trades done',
    example: 189,
    nullable: true,
  })
  trades_done: number | null;

  @ApiProperty({
    type: Number,
    description: 'Volume of trades',
    example: 64.34111897999999,
    nullable: true,
  })
  volume: number | null;

  @ApiProperty({
    type: Number,
    description: 'Notional volume of trades',
    example: 161997.4259566542,
    nullable: true,
  })
  volume_notional: number | null;

  constructor(marketData: MarketData) {
    this.ticker = marketData.ticker;
    this.exchange_code = marketData.exchangeCode;
    this.date = marketData.date;
    this.open = marketData.open;
    this.high = marketData.high;
    this.low = marketData.low;
    this.close = marketData.close;
    this.trades_done = marketData.tradesDone;
    this.volume = marketData.volume;
    this.volume_notional = marketData.volumeNotional;
  }
}
