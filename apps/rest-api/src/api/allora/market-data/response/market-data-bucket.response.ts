import {
  MarketDataBucket,
  MarketDataBucketFileFormat,
  MarketDataBucketPublicState,
} from '@app/core/domain/allora/market-data/market-data-bucket';
import { ApiProperty } from '@nestjs/swagger';

export class MarketDataBucketResponse {
  @ApiProperty({
    type: String,
    required: true,
    description: 'Ticker symbol of the market data bucket',
    example: 'ethusd',
  })
  ticker: string;

  @ApiProperty({
    type: String,
    required: true,
    description: 'State of the market data bucket',
    example: 'ready',
  })
  state: MarketDataBucketPublicState;

  @ApiProperty({
    type: String,
    required: true,
    description:
      'First day of the month; when this market data bucket starts (ISO-8601)',
    example: '2024-10-01',
  })
  start: string;

  @ApiProperty({
    type: String,
    required: true,
    description:
      'Last day of the month; when this market data bucket ends (ISO-8601)',
    example: '2024-10-31',
  })
  end: string;

  @ApiProperty({
    type: Number,
    required: true,
    description:
      'Availability of the market data bucket, where 0 means unavailable and 1 means 100% available',
    example: '0.989',
  })
  availability: number;

  @ApiProperty({
    type: String,
    required: true,
    description:
      'File format that is used by this market data bucket to store OHLC candles (only CSV is supported at this time)',
    example: 'csv',
  })
  file_format: MarketDataBucketFileFormat;

  @ApiProperty({
    type: String,
    required: false,
    description:
      'Download URL for the file that contains the market data bucket OHLC candles. ' +
      'This URL is confidential and will expire after a configured number of hours.',
    example: 'https://s3.example.com/file/download.csv',
  })
  download_url: string | null;

  constructor(bucket: MarketDataBucket, downloadUrl: string | null) {
    this.ticker = bucket.ticker;
    this.state = bucket.getPublicState();
    this.start = bucket.start;
    this.end = bucket.end;
    this.availability = bucket.availability;
    this.file_format = bucket.getFileFormat();
    this.download_url = downloadUrl;
  }
}
