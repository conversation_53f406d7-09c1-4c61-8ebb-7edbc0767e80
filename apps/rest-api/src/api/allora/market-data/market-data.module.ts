import { MARKET_DATA_QUERY_REPOSITORY } from '@app/core/domain/allora/market-data/market-data.query.repository';
import { MARKET_DATA_BUCKET_QUERY_REPOSITORY } from '@app/core/domain/allora/market-data/market-data-bucket.query.repository';
import { MARKET_DATA_UPLOAD_COMMAND_REPOSITORY } from '@app/core/domain/allora/market-data/market-data-upload.command.repository';
import { ContinuationTokenRepositoryModule } from '@app/core/domain/continuation-token/continuation-token.repository.module';
import { S3MarketDataUploadCommandRepository } from '@app/core/infra/repository/allora/market-data/command/s3.market-data-upload.command.repository';
import { SequelizeMarketDataQueryRepository } from '@app/core/infra/repository/allora/market-data/query/sequelize.market-data.query.repository';
import { SequelizeMarketDataBucketQueryRepository } from '@app/core/infra/repository/allora/market-data/query/sequelize.market-data-bucket.query.repository';
import { Module } from '@nestjs/common';
import { CqrsModule } from '@nestjs/cqrs';

import { MarketDataController } from './market-data.controller';

@Module({
  imports: [CqrsModule, ContinuationTokenRepositoryModule],
  providers: [
    {
      useClass: SequelizeMarketDataQueryRepository,
      provide: MARKET_DATA_QUERY_REPOSITORY,
    },
    {
      useClass: SequelizeMarketDataBucketQueryRepository,
      provide: MARKET_DATA_BUCKET_QUERY_REPOSITORY,
    },
    {
      useClass: S3MarketDataUploadCommandRepository,
      provide: MARKET_DATA_UPLOAD_COMMAND_REPOSITORY,
    },
  ],
  controllers: [MarketDataController],
})
export class MarketDataModule {}
