import { applyDecorators } from '@nestjs/common';
import { ApiOperation, ApiQuery } from '@nestjs/swagger';

import { ApiResponses } from '../../../common/decorator/api-responses.decorator';
import { MarketDataResponse } from './response/market-data.response';

export const getMarketDataDecorators = () => {
  return applyDecorators(
    ApiOperation({
      operationId: 'getMarketData',
      description:
        'Fetch the latest market data for specified tickers from a given date, with pagination support.',
      summary: 'Get latest market data',
    }),
    ApiQuery({
      name: 'tickers',
      required: true,
      description: 'Comma-separated list of ticker symbols to fetch data for',
      schema: { type: 'string' },
    }),
    ApiQuery({
      name: 'from_date',
      required: true,
      description: 'The start date to fetch market data from (ISO 8601 format)',
      schema: { type: 'string', format: 'date-time' },
    }),
    ApiQuery({
      name: 'continuation_token',
      required: false,
      description: 'Token for fetching the next page of results',
      schema: { type: 'string' },
    }),
    ...ApiResponses([MarketDataResponse]),
  );
};

export const getMonthlyMarketDataBucketsDecorators = () => {
  return applyDecorators(
    ApiOperation({
      operationId: 'getMonthlyMarketDataBuckets',
      description:
        'Fetch monthly market data buckets data for specified tickers, starting from a given date, up to last calendar month.',
      summary: 'Get monthly market data buckets',
    }),
    ApiQuery({
      name: 'tickers',
      required: true,
      description: 'Comma-separated list of ticker symbols to fetch data for',
      schema: { type: 'string' },
    }),
    ApiQuery({
      name: 'from_month',
      required: true,
      description: 'The start month to fetch market data from (ISO-8601 date)',
      schema: { type: 'string', format: 'date' },
    }),
  );
};
