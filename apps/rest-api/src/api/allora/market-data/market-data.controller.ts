import {
  getMonthStart,
  parseISODate,
  toISODate,
} from '@app/core/domain/allora/market-data/date';
import { MARKET_DATA_QUERY_REPOSITORY } from '@app/core/domain/allora/market-data/market-data.query.repository';
import { MarketDataBucket } from '@app/core/domain/allora/market-data/market-data-bucket';
import { MARKET_DATA_BUCKET_QUERY_REPOSITORY } from '@app/core/domain/allora/market-data/market-data-bucket.query.repository';
import {
  MARKET_DATA_UPLOAD_COMMAND_REPOSITORY,
  MarketDataUploadCommandRepository,
} from '@app/core/domain/allora/market-data/market-data-upload.command.repository';
import { ContinuationToken } from '@app/core/domain/continuation-token/continuation-token';
import { SequelizeMarketDataQueryRepository } from '@app/core/infra/repository/allora/market-data/query/sequelize.market-data.query.repository';
import { SequelizeMarketDataBucketQueryRepository } from '@app/core/infra/repository/allora/market-data/query/sequelize.market-data-bucket.query.repository';
import {
  BadRequestException,
  Controller,
  Get,
  Inject,
  Logger,
  ParseArrayPipe,
  Query,
} from '@nestjs/common';
import { QueryBus } from '@nestjs/cqrs';
import { ApiTags } from '@nestjs/swagger';

import * as Docs from './market-data.docs';
import { MarketDataResponse } from './response/market-data.response';
import { MarketDataBucketResponse } from './response/market-data-bucket.response';

@Controller('/allora/market-data')
@ApiTags('Allora Market Data')
export class MarketDataController {
  private readonly logger = new Logger(MarketDataController.name);

  constructor(
    private readonly queryBus: QueryBus,

    @Inject(MARKET_DATA_QUERY_REPOSITORY)
    private readonly marketDataQueryRepository: SequelizeMarketDataQueryRepository,

    @Inject(MARKET_DATA_BUCKET_QUERY_REPOSITORY)
    private readonly marketDataBucketQueryRepository: SequelizeMarketDataBucketQueryRepository,

    @Inject(MARKET_DATA_UPLOAD_COMMAND_REPOSITORY)
    private readonly marketDataUploadCommandRepository: MarketDataUploadCommandRepository,
  ) {}

  @Get('/ohlc')
  @Docs.getMarketDataDecorators()
  async getMarketData(
    @Query(
      'tickers',
      new ParseArrayPipe({ items: String, separator: ',', optional: false }),
    )
    tickers: string[],
    @Query('from_date') from_date: string,
    @Query('continuation_token') continuation_token?: string,
  ) {
    const pagination = continuation_token
      ? { continuationToken: new ContinuationToken(continuation_token) }
      : {};
    // validate params
    if (tickers.length === 0) {
      throw new BadRequestException('Tickers are required');
    } else if (!from_date) {
      throw new BadRequestException('From date is required');
    }

    // if from_date is not provided, use the latest minute
    const fromDateObject = from_date
      ? new Date(from_date)
      : new Date(new Date().setMinutes(new Date().getMinutes() - 1));

    const res = await this.marketDataQueryRepository.getLatestMarketData(
      tickers,
      fromDateObject,
      pagination,
    );

    return {
      data: res.data.map((d) => new MarketDataResponse(d)),
      continuation_token: res.continuationToken?.value,
    };
  }

  @Get('/ohlc/buckets/by-month')
  @Docs.getMonthlyMarketDataBucketsDecorators()
  async getMarketDataBuckets(
    @Query(
      'tickers',
      new ParseArrayPipe({ items: String, separator: ',', optional: false }),
    )
    tickers: string[],
    @Query('from_month') from_month: string,
  ) {
    if (tickers.length === 0) {
      throw new BadRequestException(
        'Query parameter "tickers" is required (at least one ticker must be provided)',
      );
    }

    if (!from_month) {
      throw new BadRequestException(
        'Query parameter "from_month" is required and should be a ISO-8601 date or date-time',
      );
    }

    let parsed: Date;
    try {
      parsed = parseISODate(from_month);
    } catch (error) {
      throw new BadRequestException(
        'Invalid "from_month" (should be a valid ISO-8601 date or date-time)',
      );
    }

    const start = toISODate(getMonthStart(parsed));

    const tickerPrefix = tickers.map((i) => `[${i}]`).join('');
    const logPrefix = `${tickerPrefix}[${start}]`;
    this.logger.log(`${logPrefix} GET market-data buckets by month`);

    const buckets =
      await this.marketDataBucketQueryRepository.getMonthlyBucketsStartingAtOrAfter(
        tickers,
        start,
      );

    const downloadUrls = await this.generateDownloadUrlsForBuckets(
      buckets,
      logPrefix,
    );

    return {
      data: buckets.map((bucket) => {
        return new MarketDataBucketResponse(
          bucket,
          downloadUrls.get(bucket.id) ?? null,
        );
      }),
    };
  }

  private async generateDownloadUrlsForBuckets(
    buckets: MarketDataBucket[],
    logPrefix: string,
  ) {
    const downloadUrls = new Map<bigint, string>();

    for (const bucket of buckets) {
      const objectKey = bucket.s3ObjectKey;
      if (bucket.state == 'ready' && !!objectKey) {
        try {
          const url =
            await this.marketDataUploadCommandRepository.generateDownloadUrl(
              objectKey,
            );
          downloadUrls.set(bucket.id, url);
        } catch (e) {
          this.logger.error(
            `${logPrefix}[${bucket.id}] unable to generate pre-signed ` +
              `download URL for S3 object key ${objectKey} → ${e}`,
          );
          throw e;
        }
      }
    }

    return downloadUrls;
  }
}
