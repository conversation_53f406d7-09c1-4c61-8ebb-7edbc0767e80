import { newS3Client } from '@app/core/infra/db/s3.client';
import { uploadS3ObjectString } from '@app/core/infra/repository/allora/market-data/command/s3.market-data-upload.command.repository.integration.spec.shared';
import { SequelizeModule } from '@nestjs/sequelize';
import { Test, TestingModule } from '@nestjs/testing';
import { S3 } from 'aws-sdk';
import { Sequelize } from 'sequelize-typescript';

import { reloadFixtures } from '../../../../../../test-integration/fixtures';
import { sequelizeOptions } from '../../../config';
import { MarketDataController } from './market-data.controller';
import { MarketDataModule } from './market-data.module';
import { MarketDataBucketResponse } from './response/market-data-bucket.response';

describe('MarketDataController', () => {
  let controller: MarketDataController;
  let sequelize: Sequelize;
  let s3Client: S3;

  beforeEach(async () => {
    sequelize = new Sequelize({
      ...sequelizeOptions,
      dialectOptions: {
        multipleStatements: true,
      },
    });

    s3Client = newS3Client();

    const module: TestingModule = await Test.createTestingModule({
      imports: [SequelizeModule.forRoot(sequelizeOptions), MarketDataModule],
    }).compile();

    await module.init();

    controller = module.get(MarketDataController);

    await reloadFixtures(sequelize, ['allora-ohlc-buckets']);
  });

  describe('getMarketDataBuckets', () => {
    test('for one ticker, one month, processing [solusd][2025-02]', async () => {
      const ticker = 'solusd';

      const response = await controller.getMarketDataBuckets(
        [ticker],
        '2025-02-01',
      );

      expect(response.data).toHaveLength(1);
      const bucket: MarketDataBucketResponse = response.data[0];

      expect(bucket.ticker).toBe(ticker);
      expect(bucket.start).toBe('2025-02-01');
      expect(bucket.end).toBe('2025-02-28');
      expect(bucket.state).toBe('processing');
      expect(bucket.download_url).toBe(null);
    });

    test('for one ticker, one month, ready to download [ethusd][2024-12]', async () => {
      const ticker = 'ethusd';

      const response = await controller.getMarketDataBuckets(
        [ticker],
        '2024-12-01',
      );

      expect(response.data).toHaveLength(1);
      const bucket: MarketDataBucketResponse = response.data[0];

      await uploadS3ObjectString(
        s3Client,
        'ethusd_month_2024-12.csv',
        'foo|bar|5',
      );

      expect(bucket.ticker).toBe(ticker);
      expect(bucket.start).toBe('2024-12-01');
      expect(bucket.end).toBe('2024-12-31');
      expect(bucket.state).toBe('ready');
      expect(bucket.download_url).toBeTruthy();

      expect(bucket.download_url).toContain('ethusd_month_2024-12.csv');

      // this is defined in docker-compose.yml (we use localstack to emulate s3 as a container)
      expect(bucket.download_url).toContain(
        'http://localhost:14566/allora-dev-ohlc-timeseries/ethusd_month_2024-12.csv?AWSAccessKeyId=test',
      );
    });

    test('for multiple buckets [btcusd][ethusd][2024-12]', async () => {
      const tickers = ['btcusd', 'ethusd'];

      const response = await controller.getMarketDataBuckets(
        tickers,
        '2024-12-01',
      );

      expect(response.data).toHaveLength(5);
      const buckets: MarketDataBucketResponse[] = response.data;

      await uploadS3ObjectString(
        s3Client,
        'btcusd_month_2025-02.csv',
        'foo|bar|333',
      );

      await uploadS3ObjectString(
        s3Client,
        'btcusd_month_2025-01.csv',
        'foo|bar|333',
      );

      await uploadS3ObjectString(
        s3Client,
        'ethusd_month_2024-12.csv',
        'foo|bar|333',
      );

      expect(buckets[0].ticker).toBe('btcusd');
      expect(buckets[0].start).toBe('2025-03-01');
      expect(buckets[0].state).toBe('processing');
      expect(buckets[0].download_url).toBeNull();

      expect(buckets[1].ticker).toBe('btcusd');
      expect(buckets[1].start).toBe('2025-02-01');
      expect(buckets[1].state).toBe('ready');
      expect(buckets[1].download_url).toContain('btcusd_month_2025-02.csv');

      expect(buckets[2].ticker).toBe('btcusd');
      expect(buckets[2].start).toBe('2025-01-01');
      expect(buckets[2].state).toBe('ready');
      expect(buckets[2].download_url).toContain('btcusd_month_2025-01.csv');

      expect(buckets[3].ticker).toBe('btcusd');
      expect(buckets[3].start).toBe('2024-12-01');
      expect(buckets[3].state).toBe('processing');
      expect(buckets[3].download_url).toBeNull();

      expect(buckets[4].ticker).toBe('ethusd');
      expect(buckets[4].start).toBe('2024-12-01');
      expect(buckets[4].state).toBe('ready');
      expect(buckets[4].download_url).toContain('ethusd_month_2024-12.csv');
    });

    test('if no tickers are given', async () => {
      await expect(
        controller.getMarketDataBuckets([], '2024-12'),
      ).rejects.toThrowError(
        'Query parameter "tickers" is required (at least one ticker must be provided)',
      );
    });

    test('if from_date is not given', async () => {
      await expect(
        controller.getMarketDataBuckets(['btcusd'], ''),
      ).rejects.toThrowError(
        'Query parameter "from_month" is required and should be a ISO-8601 date',
      );
    });

    test('if from_date is not a valid ISO-8601 date or timestamp', async () => {
      await expect(
        controller.getMarketDataBuckets(['btcusd'], '2025-13-01T99:99:33.000Z'),
      ).rejects.toThrowError(
        'Invalid "from_month" (should be a valid ISO-8601 date or date-time)',
      );
    });

    test('if from_date is only month [2025-02]', async () => {
      const ticker = 'solusd';

      const response = await controller.getMarketDataBuckets(
        [ticker],
        '2025-02',
      );

      expect(response.data).toHaveLength(1);
      const bucket: MarketDataBucketResponse = response.data[0];

      expect(bucket.ticker).toBe(ticker);
      expect(bucket.start).toBe('2025-02-01');
      expect(bucket.end).toBe('2025-02-28');
      expect(bucket.state).toBe('processing');
      expect(bucket.download_url).toBe(null);
    });

    test('if from_date is full ISO-8601 timestamp [2025-02-03T15:55:33.000+03:00]', async () => {
      const ticker = 'solusd';

      const response = await controller.getMarketDataBuckets(
        [ticker],
        '2025-02-03T15:55:33.000+03:00',
      );

      expect(response.data).toHaveLength(1);
      const bucket: MarketDataBucketResponse = response.data[0];

      expect(bucket.ticker).toBe(ticker);
      expect(bucket.start).toBe('2025-02-01');
      expect(bucket.end).toBe('2025-02-28');
      expect(bucket.state).toBe('processing');
      expect(bucket.download_url).toBe(null);
    });
  });
});
