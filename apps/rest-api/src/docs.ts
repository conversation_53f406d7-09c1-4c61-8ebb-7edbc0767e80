import { ENodeEnv, NODE_ENV } from '@app/core/config/env';
import { Logger } from '@nestjs/common';
import { NestExpressApplication } from '@nestjs/platform-express';
import { DocumentBuilder, OpenAPIObject, SwaggerModule } from '@nestjs/swagger';
import { SecuritySchemeObject } from '@nestjs/swagger/dist/interfaces/open-api-spec.interface';

export const docs = (app: NestExpressApplication): OpenAPIObject => {
  const logger = new Logger('Rest-API-Docs');
  logger.log('Creating REST API Docs...');

  let docServer = '';
  let docServerStaging = '';
  if (NODE_ENV === ENodeEnv.MOCK || NODE_ENV === ENodeEnv.PROD) {
    docServer = 'https://api.upshot.xyz';
  } else if (NODE_ENV === ENodeEnv.STAGE_MOCK || NODE_ENV === ENodeEnv.STAGE) {
    docServer = 'https://stage.api.upshot.xyz';
    docServerStaging = 'http://localhost:3000';
  }

  const PROPS_TO_ADD = {
    security: [
      {
        'x-api-key': [],
      },
    ],
    'x-readme': {
      'explorer-enabled': true,
      'proxy-enabled': true,
      'samples-enabled': true,
    },
  };

  // Extend SecuritySchemeObject with a custom type
  type CustomSecuritySchemeObject = SecuritySchemeObject & {
    type: string;
  };

  const SECURITY_SCHEMES: Record<string, CustomSecuritySchemeObject> = {
    'x-api-key': {
      name: 'x-api-key',
      in: 'header',
      type: 'apiKey',
      description: 'API Key',
    },
  };

  const config = new DocumentBuilder()
    .setTitle('Upshot API')
    .setDescription(
      'The Upshot API v2 for getting NFT metadata, events, appraisals, and wallet/ownership data.',
    )
    .setVersion('2.0')
    .addServer(docServer)
    .addServer(docServerStaging)
    .addSecurity('x-api-key', SECURITY_SCHEMES['x-api-key'])
    .build();

  const document = SwaggerModule.createDocument(app, config, {
    ignoreGlobalPrefix: false,
  });
  Object.assign(document, PROPS_TO_ADD);
  (document.components ||= {}).securitySchemes = SECURITY_SCHEMES;

  return document;
};
