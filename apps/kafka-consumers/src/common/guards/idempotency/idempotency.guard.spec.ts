import { REDIS_IDEMPOTENCY_URL } from '@app/core/infra/redis/redis.config';
import { REDIS_IDEMPOTENCY_SERVICE } from '@app/core/infra/redis/redis.module';
import { RedisService } from '@app/core/infra/redis/redis.service';
import { FAKE_DATA_DOG_CLIENT } from '@app/datadog-client/test-utils';
import { Test, TestingModule } from '@nestjs/testing';
import * as hash from 'object-hash';

import { IdempotencyGuard } from './idempotency.guard';

describe('IdempotencyGuard', () => {
  let guard: IdempotencyGuard;
  let redisService: RedisService;

  beforeEach(async () => {
    redisService = new RedisService(REDIS_IDEMPOTENCY_URL);
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        IdempotencyGuard,
        {
          provide: REDIS_IDEMPOTENCY_SERVICE,
          useValue: redisService,
        },
        FAKE_DATA_DOG_CLIENT,
      ],
    }).compile();

    guard = module.get<IdempotencyGuard>(IdempotencyGuard);
    await module.init();
  });

  it('should be defined', () => {
    expect(guard).toBeDefined();
  });

  describe('canActivate', () => {
    it('should return true if context is not rpc', () => {
      const context = {
        getType: jest.fn().mockReturnValue('http'),
      };
      expect(guard.canActivate(context as any)).toEqual(true);
    });

    it('should return true if topic is not found', () => {
      const context = {
        getType: jest.fn().mockReturnValue('rpc'),
        switchToRpc: jest.fn().mockReturnValue({
          getData: jest.fn().mockReturnValue({}),
        }),
      };
      expect(guard.canActivate(context as any)).toEqual(true);
    });

    describe('using defaultGuard', () => {
      const topic = 'test-topic';
      const mockMessage = {
        topic,
        value: { something: 'test-value' },
        key: 'test-key',
        timestamp: 123456789,
      };

      it('should return true if the hashed key is not found in the database', async () => {
        const context = {
          getType: jest.fn().mockReturnValue('rpc'),
          switchToRpc: jest.fn().mockReturnValue({
            getData: jest.fn().mockReturnValue(mockMessage),
          }),
        };

        redisService.get = jest.fn().mockResolvedValue(null);

        expect(await guard.canActivate(context as any)).toEqual(true);
      });

      it('should return false if the hashed key is found in the database', async () => {
        const context = {
          getType: jest.fn().mockReturnValue('rpc'),
          switchToRpc: jest.fn().mockReturnValue({
            getData: jest.fn().mockReturnValue(mockMessage),
          }),
        };

        redisService.get = jest.fn().mockResolvedValue({ data: 'yo' });

        expect(await guard.canActivate(context as any)).toEqual(false);
      });
    });

    describe('passThroughGuard', () => {
      const topic = 'alerts.metrics.user_triggers_daily';
      const mockMessage = {
        topic,
        value: { something: 'test-value' },
        key: 'test-key',
        timestamp: 123456789,
      };

      it('always passes through', async () => {
        const context = {
          getType: jest.fn().mockReturnValue('rpc'),
          switchToRpc: jest.fn().mockReturnValue({
            getData: jest.fn().mockReturnValue(mockMessage),
          }),
        };

        redisService.get = jest.fn().mockResolvedValue({});

        expect(await guard.canActivate(context as any)).toEqual(true);
      });
    });
  });

  describe('object-hash usage', () => {
    it('object hash returns the same value regardless of property order', () => {
      const message0 = [1, 2, { top: 'toppo', sub: { a: 1, b: 2, c: 3 } }];
      const message1 = [1, 2, { top: 'toppo', sub: { a: 1, c: 3, b: 2 } }];
      const message2 = [1, 2, { top: 'toppo', sub: { b: 2, a: 1, c: 3 } }];
      const message3 = [1, 2, { top: 'toppo', sub: { b: 2, c: 3, a: 1 } }];

      const hash0 = hash(message0);
      const hash1 = hash(message1);
      const hash2 = hash(message2);
      const hash3 = hash(message3);

      expect(hash0).toEqual('984c309d4ccff6525a7387362c819265af825d06');
      expect(hash0).toEqual(hash1);
      expect(hash0).toEqual(hash2);
      expect(hash0).toEqual(hash3);
    });
  });
});
