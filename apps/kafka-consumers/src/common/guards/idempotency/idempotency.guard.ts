import { REDIS_IDEMPOTENCY_SERVICE } from '@app/core/infra/redis/redis.module';
import { RedisService } from '@app/core/infra/redis/redis.service';
import {
  CanActivate,
  ExecutionContext,
  Inject,
  Injectable,
  Logger,
} from '@nestjs/common';
import { StatsD } from 'hot-shots';
import * as hash from 'object-hash';
import { Observable } from 'rxjs';

import { TTL } from './config';

type TTopicGuardFn = (args: {
  topic: string;
  value: any | null;
  key: string;
  timestamp: string;
}) => boolean;

type TTopicHashFn = (args: {
  value: any | null;
  key: string;
  timestamp: string;
}) => string;

@Injectable()
export class IdempotencyGuard implements CanActivate {
  private readonly logger = new Logger(IdempotencyGuard.name);

  private readonly topicGuardMap: Record<string, TTopicGuardFn> = {
    'alerts.metrics.user_triggers_daily': this.passThroughGuard.bind(this),
  };

  private readonly topicHashMap: Record<string, TTopicHashFn> = {
    'metrics.appraisals': this.timelessHashFn.bind(this),
    'alerts.delivery.email_alerts': this.timelessHashFn.bind(this),
    'alerts.delivery.web_alerts': this.timelessHashFn.bind(this),
    'alerts.delivery.assigned_alerts': this.timelessHashFn.bind(this),
  };

  constructor(
    @Inject(REDIS_IDEMPOTENCY_SERVICE)
    private readonly redisService: RedisService,
    private readonly dataDogClient: StatsD,
  ) {}

  /**
   * Will return true unless the message has a positive idempotency match
   */
  canActivate(
    context: ExecutionContext,
  ): boolean | Promise<boolean> | Observable<boolean> {
    if (context.getType() != 'rpc') {
      this.logger.error('can only be used with RPC');
      return true;
    }

    const rpcData = context.switchToRpc().getData();
    const { topic, value, key, timestamp } = rpcData;

    if (!topic) {
      this.logger.error('unable to find topic');
      return true;
    }

    return this.performIdempotencyCheck({ topic, value, key, timestamp });
  }

  public performIdempotencyCheck = async ({
    topic,
    value,
    key,
    timestamp,
    consumerGroup,
  }: {
    topic: string;
    value: any | null;
    key: string;
    timestamp: string;
    consumerGroup?: string;
  }): Promise<boolean> => {
    try {
      this.logger.debug(`topic: ${topic}`);
      this.logger.debug(`value: ${value}`);
      this.logger.debug(`key: ${key}`);

      if (value?.id && consumerGroup) {
        return this.domainEventGuard({
          topic,
          value,
          key,
          timestamp,
          consumerGroup,
        });
      }

      const guardFn = this.topicGuardMap[topic] ?? this.defaultGuard;
      return guardFn({ key, timestamp, value, topic });
    } catch (e) {
      this.logger.error(e);
      return true;
    }
  };

  /**
   *  Log idempotency hits
   */
  private logHit(topic: any) {
    this.logger.debug(`idempotency hit: ${topic}`);
    this.dataDogClient.increment('xyz.upshot.backend.idempotency.hit', [
      `topic:${topic}`,
    ]);
  }

  /**
   *  Log idempotency misses
   */
  private logMiss(topic: any) {
    this.logger.debug(`idempotency miss: ${topic}`);
    this.dataDogClient.increment('xyz.upshot.backend.idempotency.miss', [
      `topic:${topic}`,
    ]);
  }

  /**
   * Returns true
   */
  private passThroughGuard() {
    return true;
  }

  /**
   * Hashes the key, timestamp, and value to create a unique idempotency key
   * checks for that key in the database
   */
  private defaultGuard = async ({
    topic,
    key,
    timestamp,
    value,
  }: {
    topic: string;
    key: string;
    timestamp: string;
    value: any | null;
  }): Promise<boolean> => {
    this.logger.debug('using defaultGuard');
    // let tombstone messages through
    if (value === null) {
      return true;
    }

    // create hash key
    const hashFn = this.topicHashMap[topic] ?? this.defaultHashFn.bind(this);
    const idempotencyKey = hashFn({ key, timestamp, value });
    this.logger.debug(`EVENT_ID: ${value.EVENT_ID}`);
    this.logger.debug(`idempotencyKey: ${idempotencyKey}`);

    // check if key exists in redis
    const item = await this.redisService.get(idempotencyKey);
    this.logger.debug(`item: ${JSON.stringify(item)}`);

    if (!item || !item.data) {
      this.logMiss(topic);
      // if not, add it
      await this.redisService.set(idempotencyKey, true, TTL);
      return true;
    }

    // if item exists, return false
    this.logHit(topic);
    return false;
  };

  private domainEventGuard = async ({
    topic,
    key,
    timestamp,
    value,
    consumerGroup,
  }: {
    topic: string;
    key: string;
    timestamp: string;
    value: any | null;
    consumerGroup: string;
  }): Promise<boolean> => {
    this.logger.debug('using domainEventGuard');
    // let tombstone messages through
    if (value === null || !value.id) {
      return true;
    }

    const idempotencyKey = `${value.id}-${consumerGroup}`;

    // create hash key
    this.logger.debug(`EVENT_ID: ${value.id}`);
    this.logger.debug(`idempotencyKey: ${value.id}`);

    // check if key exists in redis
    const item = await this.redisService.get(idempotencyKey);

    this.logger.debug(`item: ${JSON.stringify(item)}`);

    if (!item || !item.data) {
      this.logMiss(topic);
      // if not, add it
      await this.redisService.set(idempotencyKey, true, TTL);
      return true;
    }

    // if item exists, return false
    this.logHit(topic);
    return false;
  };

  /**
   * Default hash function hashes the key, timestamp, and value to create a unique idempotency key
   */
  private defaultHashFn({
    key,
    timestamp,
    value,
  }: {
    key: string;
    timestamp: number;
    value: any | null;
  }): string {
    this.logger.debug('using defaultHashFn');
    return hash(
      { key, timestamp, value },
      {
        respectType: false,
        respectFunctionProperties: false,
      },
    );
  }

  /**
   * Timeless hash function hashes the key and value to create a unique idempotency key
   */
  private timelessHashFn({
    key,
    value,
  }: {
    key: string;
    value: any | null;
  }): string {
    this.logger.debug('using timelessHashFn');
    return hash(
      { key, value },
      {
        respectType: false,
        respectFunctionProperties: false,
      },
    );
  }
}
