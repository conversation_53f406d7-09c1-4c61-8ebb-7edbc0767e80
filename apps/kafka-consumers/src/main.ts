import { ENodeEnv, NODE_ENV } from '@app/core/config/env';
import { INestApplication } from '@nestjs/common';
import { NestFactory } from '@nestjs/core';
import { Logger } from 'nestjs-pino';

import { KAFKA_CONSUMERS_HEALTH_PORT } from './env.config';
import { KafkaConsumersModule } from './kafka-consumers.module';

let logger;

// from https://nodejs.org/api/process.html#event-unhandledrejection
process.on('unhandledRejection', (reason: any, promise) => {
  logger.error(`Unhandled Rejection at: ${promise}`);
  logger.error(reason);
  logger.error(reason.stack);
});

export async function bootstrap(): Promise<INestApplication> {
  const app = await NestFactory.create(KafkaConsumersModule, {
    bufferLogs: true,
  });
  logger = app.get(Logger);
  app.useLogger(logger);
  console.log = app.get(Logger).debug.bind(app.get(Logger));
  app.connectMicroservice({});

  logger.log(`Upshot Kafka Consumers started in environment ${NODE_ENV}`);

  try {
    // quit on ctrl-c when running docker in terminal
    NODE_ENV === ENodeEnv.PROD
      ? process.on('SIGINT', () => {
          logger.log(
            'Got SIGINT (aka ctrl-c in docker). Graceful shutdown ',
            new Date().toISOString(),
          );
          app.close();
        })
      : '';

    // quit properly on docker stop
    NODE_ENV === ENodeEnv.PROD
      ? process.on('SIGTERM', () => {
          logger.log(
            'Got SIGTERM (docker container stop). Graceful shutdown ',
            new Date().toISOString(),
          );
          app.close();
        })
      : '';
  } catch (e) {
    logger.error(e);
  }

  const port = KAFKA_CONSUMERS_HEALTH_PORT || 5001;
  await app.startAllMicroservices();
  await app.listen(port);
  logger.log(
    `Upshot kafka-consumer started on ${process.env.NODE_ENV} environment with port ${port}`,
  );
  return app;
}
bootstrap();
