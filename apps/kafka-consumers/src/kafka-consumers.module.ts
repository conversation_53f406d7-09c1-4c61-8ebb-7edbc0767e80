import { AppLoggerModule } from '@app/app-logger/logger.module';
import { UnitOfWorkModule } from '@app/core/app/unit-of-work/unit-of-work.module';
import { MetricsLoggerModule } from '@app/core/domain/metrics-logger/metrics-logger.module';
import { PrometheusMetricsModule } from '@app/core/infra/prometheus/prometheus-metrics.module';
import { Module } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';
import { ClsModule } from 'nestjs-cls';
import { v4 } from 'uuid';

import { sequelizeOptions } from '../../rest-api/src/config';
import { DomainEventConsumersModule } from './domain-event-consumers/domain-event-consumers.module';
import { HealthModule } from './health/health.module';

@Module({
  imports: [
    SequelizeModule.forRoot(sequelizeOptions),
    PrometheusMetricsModule.forRoot({ appName: 'kafka-consumers' }),
    DomainEventConsumersModule,
    HealthModule,
    UnitOfWorkModule,
    MetricsLoggerModule,
    ClsModule.forRoot({
      global: true,
      middleware: {
        mount: true,
        generateId: true,
        idGenerator: (req: Request) => req.headers['x-request-id'] ?? v4(),
      },
    }),
    AppLoggerModule,
  ],
  controllers: [],
})
export class KafkaConsumersModule {}
