import { ClientHealthRegistry } from '@app/core/infra/transport/kafka-utils';
import { Controller, Get, HttpException, Logger } from '@nestjs/common';

import { Public } from '../../../rest-api/src/common/decorator';

@Controller('health')
export class HealthController {
  private readonly logger = new Logger(HealthController.name);

  constructor(private readonly clientHealthRegistry: ClientHealthRegistry) {}

  @Public()
  @Get('/')
  public async health() {
    const healthMap = this.clientHealthRegistry.getHealthMap();
    this.logger.log(JSON.stringify(healthMap));
    const areAllHealthy = this.clientHealthRegistry.areAllHealthy();

    if (!areAllHealthy) {
      throw new HttpException(
        `Not all clients are healthy ${JSON.stringify(healthMap)}`,
        500,
      );
    }

    return healthMap;
  }
}
