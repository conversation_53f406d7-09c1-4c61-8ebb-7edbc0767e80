import { ClientHealthRegistry } from '@app/core/infra/transport/kafka-utils';
import { Test, TestingModule } from '@nestjs/testing';

import { HealthController } from './health.controller';

describe('HealthController', () => {
  let controller: HealthController;

  const mockClientHealthRegistry = {
    areAllHealthy: jest.fn().mockReturnValue(true),
    getHealthMap: jest.fn().mockReturnValue({
      foo: 'bar',
    }),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [HealthController],
      providers: [
        {
          provide: ClientHealthRegistry,
          useValue: mockClientHealthRegistry,
        },
      ],
    }).compile();

    controller = module.get<HealthController>(HealthController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  it('throws if not all clients are healthy', async () => {
    mockClientHealthRegistry.areAllHealthy.mockReturnValue(false);

    try {
      await controller.health();
      throw new Error('should have thrown');
    } catch (e) {
      expect(e.message).toEqual('Not all clients are healthy {"foo":"bar"}');
    }
  });

  it('returns health map if all clients are healthy', async () => {
    mockClientHealthRegistry.areAllHealthy.mockReturnValue(true);
    expect(await controller.health()).toEqual({ foo: 'bar' });
  });
});
