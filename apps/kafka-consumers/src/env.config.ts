import { config } from 'dotenv';
import * as env from 'env-var';

config();

export const KAFKA_CONSUMERS_HEALTH_PORT = env
  .get('KAFKA_CONSUMERS_HEALTH_PORT')
  .asInt();

export const ALERTS_DELIVER_TO_ALL_USERS = env
  .get('ALERTS_DELIVER_TO_ALL_USERS')
  .default('false')
  .asBool();

export const DITTO_UPDATE_LIQUIDITY_STATS_TOPIC =
  'domain-event.output.protocol-ditto-ethereum.DittoUpdateLiquidityStats';
