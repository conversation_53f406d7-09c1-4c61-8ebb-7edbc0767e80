import { ReportVaultCommand } from '@app/core/app/use-case/robonet/vault/command/report-vault/report-vault.command';
import {
  DOMAIN_EVENT_CONSUMER,
  DomainEventConsumer,
} from '@app/core/domain/domain-event/domain-event.consumer';
import { RoboNetReportedEvent } from '@app/core/domain/robonet/vault/events/robonet-reported.event';
import { Controller, Inject, OnApplicationBootstrap } from '@nestjs/common';
import { CommandBus } from '@nestjs/cqrs';

@Controller()
export class RoboNetVaultReportedController implements OnApplicationBootstrap {
  constructor(
    @Inject(DOMAIN_EVENT_CONSUMER)
    private readonly domainEventConsumer: DomainEventConsumer<RoboNetReportedEvent>,
    private readonly commandBus: CommandBus,
  ) {}

  async onApplicationBootstrap() {
    await this.domainEventConsumer.consumeBatch(
      {
        topics: [
          'domain-event.output.protocol-robonetv2-ethereum.RoboNetReported',
        ],
        consumerName: RoboNetReportedEvent.getName(),
        maxBatchSize: 1,
      },
      async (events) => {
        for (const event of events) {
          await this.commandBus.execute(new ReportVaultCommand(event));
        }
      },
    );
  }
}
