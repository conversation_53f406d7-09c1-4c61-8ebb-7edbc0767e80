import { ProcessNetworkInferenceCommand } from '@app/core/app/use-case/allora/network-inference/process-network-inference.command';
import { AlloraNetworkInferenceEvent } from '@app/core/domain/allora/network-inference/allora-network-inference.event';
import {
  DOMAIN_EVENT_CONSUMER,
  DomainEventConsumer,
} from '@app/core/domain/domain-event/domain-event.consumer';
import { Controller, Inject, OnApplicationBootstrap } from '@nestjs/common';
import { CommandBus } from '@nestjs/cqrs';

@Controller()
export class AlloraNetworkInferenceTestNetController
  implements OnApplicationBootstrap
{
  constructor(
    @Inject(DOMAIN_EVENT_CONSUMER)
    private readonly domainEventConsumer: DomainEventConsumer<AlloraNetworkInferenceEvent>,
    private readonly commandBus: CommandBus,
  ) {}

  async onApplicationBootstrap() {
    await this.domainEventConsumer.consumeBatch(
      {
        topics: ['domain-event.input.allora.network-inferences'],
        consumerName: AlloraNetworkInferenceEvent.getName(),
        maxBatchSize: 1000,
      },
      async (events) => {
        for (const event of events) {
          await this.commandBus.execute(
            new ProcessNetworkInferenceCommand(event),
          );
        }
      },
    );
  }
}
