import { ProcessWalletBalanceCommand } from '@app/core/app/use-case/allora/wallet-balance/process-wallet-balance.command';
import { AlloraValidatorRewardsEvent } from '@app/core/domain/allora/allora-wallet-balance/allora-comission.event';
import { AlloraTransferEvent } from '@app/core/domain/allora/allora-wallet-balance/allora-transfer.event';
import {
  DOMAIN_EVENT_CONSUMER,
  DomainEventConsumer,
} from '@app/core/domain/domain-event/domain-event.consumer';
import { Controller, Inject, OnApplicationBootstrap } from '@nestjs/common';
import { CommandBus } from '@nestjs/cqrs';

@Controller()
export class AlloraWalletBalanceChangedTestNetController
  implements OnApplicationBootstrap
{
  constructor(
    @Inject(DOMAIN_EVENT_CONSUMER)
    private readonly domainEventConsumer: DomainEventConsumer<
      AlloraTransferEvent | AlloraValidatorRewardsEvent
    >,
    private readonly commandBus: CommandBus,
  ) {}

  async onApplicationBootstrap() {
    await this.domainEventConsumer.consumeBatch(
      {
        topics: ['domain-event.input.allora.transfer'],
        consumerName: 'allora-wallet-balance-changed-part-2',
        maxBatchSize: 1,
      },
      async (events) => {
        for (const event of events) {
          await this.commandBus.execute(new ProcessWalletBalanceCommand(event));
        }
      },
    );
  }
}
