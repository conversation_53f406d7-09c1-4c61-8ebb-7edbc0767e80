import { ActivityVaultCommand } from '@app/core/app/use-case/robonet/vault/command/activity-vault/activity-vault.command';
import {
  DOMAIN_EVENT_CONSUMER,
  DomainEventConsumer,
} from '@app/core/domain/domain-event/domain-event.consumer';
import { RoboNetDepositEvent } from '@app/core/domain/robonet/vault/events/robonet-deposit.event';
import { RoboNetWithdrawEvent } from '@app/core/domain/robonet/vault/events/robonet-withdraw.event';
import { Controller, Inject, OnApplicationBootstrap } from '@nestjs/common';
import { CommandBus } from '@nestjs/cqrs';

@Controller()
export class RoboNetVaultActivityController implements OnApplicationBootstrap {
  constructor(
    @Inject(DOMAIN_EVENT_CONSUMER)
    private readonly domainEventConsumer: DomainEventConsumer<
      RoboNetDepositEvent | RoboNetWithdrawEvent
    >,
    private readonly commandBus: CommandBus,
  ) {}

  async onApplicationBootstrap() {
    await this.domainEventConsumer.consumeBatch(
      {
        topics: [
          'domain-event.output.protocol-robonetv2-ethereum.RoboNetDeposit',
          'domain-event.output.protocol-robonetv2-ethereum.RoboNetWithdraw',
        ],
        consumerName: 'robonet-vault-activity-consumer',
        maxBatchSize: 1,
      },
      async (events) => {
        for (const event of events) {
          await this.commandBus.execute(new ActivityVaultCommand(event));
        }
      },
    );
  }
}
