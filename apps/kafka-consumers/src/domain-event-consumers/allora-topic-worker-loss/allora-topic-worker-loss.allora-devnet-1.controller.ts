import { ProcessTopicWorkerLossCommand } from '@app/core/app/use-case/allora/topic/topic-worker-loss/process-topic-worker-loss.command';
import { AlloraTopicWorkerLossEvent } from '@app/core/domain/allora/topic/events/allora-topic-worker-loss.event';
import {
  DOMAIN_EVENT_CONSUMER,
  DomainEventConsumer,
} from '@app/core/domain/domain-event/domain-event.consumer';
import { Controller, Inject, OnApplicationBootstrap } from '@nestjs/common';
import { CommandBus } from '@nestjs/cqrs';

@Controller()
export class AlloraTopicWorkerLossDevNetController
  implements OnApplicationBootstrap
{
  constructor(
    @Inject(DOMAIN_EVENT_CONSUMER)
    private readonly domainEventConsumer: DomainEventConsumer<AlloraTopicWorkerLossEvent>,
    private readonly commandBus: CommandBus,
  ) {}

  async onApplicationBootstrap() {
    await this.domainEventConsumer.consumeBatch(
      {
        topics: ['domain-event.input.allora.allora-devnet-1.worker-loss'],
        consumerName: AlloraTopicWorkerLossEvent.getName() + '-allora-devnet-1',
        maxBatchSize: 1000,
      },
      async (events) => {
        for (const event of events) {
          await this.commandBus.execute(
            new ProcessTopicWorkerLossCommand(event),
          );
        }
      },
    );
  }
}
