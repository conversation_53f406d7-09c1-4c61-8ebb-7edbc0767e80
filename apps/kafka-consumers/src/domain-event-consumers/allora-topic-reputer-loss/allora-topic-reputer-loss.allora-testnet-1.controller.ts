import { ProcessTopicReputerLossCommand } from '@app/core/app/use-case/allora/topic/topic-reputer-loss/process-topic-reputer-loss.command';
import { AlloraTopicReputerLossEvent } from '@app/core/domain/allora/topic/events/allora-topic-reputer-loss.event';
import {
  DOMAIN_EVENT_CONSUMER,
  DomainEventConsumer,
} from '@app/core/domain/domain-event/domain-event.consumer';
import { Controller, Inject, OnApplicationBootstrap } from '@nestjs/common';
import { CommandBus } from '@nestjs/cqrs';

@Controller()
export class AlloraTopicReputerLossTestNetController
  implements OnApplicationBootstrap
{
  constructor(
    @Inject(DOMAIN_EVENT_CONSUMER)
    private readonly domainEventConsumer: DomainEventConsumer<AlloraTopicReputerLossEvent>,
    private readonly commandBus: CommandBus,
  ) {}

  async onApplicationBootstrap() {
    await this.domainEventConsumer.consumeBatch(
      {
        topics: ['domain-event.input.allora.reputer-loss'],
        consumerName: AlloraTopicReputerLossEvent.getName(),
        maxBatchSize: 1000,
      },
      async (events) => {
        for (const event of events) {
          await this.commandBus.execute(
            new ProcessTopicReputerLossCommand(event),
          );
        }
      },
    );
  }
}
