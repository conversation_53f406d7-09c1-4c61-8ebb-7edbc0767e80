import { ProcessTopicReputerStakeChangedCommand } from '@app/core/app/use-case/allora/topic/topic-reputer-stake-changed/process-topic-reputer-stake-changed.command';
import { AlloraAddStakeRequestEvent } from '@app/core/domain/allora/topic/events/allora-topic-add-stake-request.event';
import { AlloraCancelRemoveDelegateStakeRequestEvent } from '@app/core/domain/allora/topic/events/allora-topic-cancel-remove-delegate-stake-request.event';
import { AlloraCancelRemoveStakeRequestEvent } from '@app/core/domain/allora/topic/events/allora-topic-cancel-remove-stake-request.event';
import { AlloraDelegateStakeRequestEvent } from '@app/core/domain/allora/topic/events/allora-topic-delegate-stake-request.event';
import { AlloraRemoveDelegateStakeRequestEvent } from '@app/core/domain/allora/topic/events/allora-topic-remove-delegate-stake-request.event';
import { AlloraRemoveStakeRequestEvent } from '@app/core/domain/allora/topic/events/allora-topic-remove-stake-request.event';
import {
  DOMAIN_EVENT_CONSUMER,
  DomainEventConsumer,
} from '@app/core/domain/domain-event/domain-event.consumer';
import { Controller, Inject, OnApplicationBootstrap } from '@nestjs/common';
import { CommandBus } from '@nestjs/cqrs';

@Controller()
export class AlloraTopicReputerStakeChangedTestNetController
  implements OnApplicationBootstrap
{
  constructor(
    @Inject(DOMAIN_EVENT_CONSUMER)
    private readonly domainEventConsumer: DomainEventConsumer<
      | AlloraAddStakeRequestEvent
      | AlloraRemoveStakeRequestEvent
      | AlloraDelegateStakeRequestEvent
      | AlloraRemoveDelegateStakeRequestEvent
      | AlloraCancelRemoveStakeRequestEvent
      | AlloraCancelRemoveDelegateStakeRequestEvent
    >,
    private readonly commandBus: CommandBus,
  ) {}

  async onApplicationBootstrap() {
    await this.domainEventConsumer.consumeBatch(
      {
        topics: [
          'domain-event.input.allora.add-stake',
          'domain-event.input.allora.remove-stake',
          'domain-event.input.allora.cancel-remove-stake',
          'domain-event.input.allora.delegate-stake',
          'domain-event.input.allora.remove-delegate-stake',
          'domain-event.input.allora.cancel-remove-delegate-stake',
        ],
        consumerName:
          'xyz.upshot.backend.domain-event.output.allora-event-reputer-stake-changed-allora-testnet-1',
        maxBatchSize: 1,
      },
      async (events) => {
        for (const event of events) {
          await this.commandBus.execute(
            new ProcessTopicReputerStakeChangedCommand(event),
          );
        }
      },
    );
  }
}
