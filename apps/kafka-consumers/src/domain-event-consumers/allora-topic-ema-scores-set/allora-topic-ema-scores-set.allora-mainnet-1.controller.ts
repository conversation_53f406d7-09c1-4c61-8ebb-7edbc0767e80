import { ProcessTopicEmaScoresCommand } from '@app/core/app/use-case/allora/topic/topic-ema-score-set/process-topic-ema-score-set.command';
import { AlloraTopicEmaScores } from '@app/core/domain/allora/topic/events/allora-topic-ema-scores.event';
import {
  DOMAIN_EVENT_CONSUMER,
  DomainEventConsumer,
} from '@app/core/domain/domain-event/domain-event.consumer';
import { Controller, Inject, OnApplicationBootstrap } from '@nestjs/common';
import { CommandBus } from '@nestjs/cqrs';

@Controller()
export class AlloraTopicEmaScoresSetMainNetController
  implements OnApplicationBootstrap
{
  constructor(
    @Inject(DOMAIN_EVENT_CONSUMER)
    private readonly domainEventConsumer: DomainEventConsumer<AlloraTopicEmaScores>,
    private readonly commandBus: CommandBus,
  ) {}

  async onApplicationBootstrap() {
    await this.domainEventConsumer.consumeBatch(
      {
        topics: ['domain-event.input.allora.allora-mainnet-1.emascores'],
        consumerName: AlloraTopicEmaScores.getName() + '-allora-mainnet-1',
        maxBatchSize: 1000,
      },
      async (events) => {
        for (const event of events) {
          await this.commandBus.execute(
            new ProcessTopicEmaScoresCommand(event),
          );
        }
      },
    );
  }
}
