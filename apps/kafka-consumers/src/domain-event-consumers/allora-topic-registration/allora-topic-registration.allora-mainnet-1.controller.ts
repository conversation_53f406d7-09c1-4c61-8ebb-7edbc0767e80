import { ProcessTopicRegistrationCommand } from '@app/core/app/use-case/allora/topic/topic-registration/process-topic-registration.command';
import { AlloraTopicMsgRegisterEvent } from '@app/core/domain/allora/topic/events/allora-topic-msg-register';
import {
  DOMAIN_EVENT_CONSUMER,
  DomainEventConsumer,
} from '@app/core/domain/domain-event/domain-event.consumer';
import { Controller, Inject, OnApplicationBootstrap } from '@nestjs/common';
import { CommandBus } from '@nestjs/cqrs';

@Controller()
export class AlloraTopicRegistrationMainNetController
  implements OnApplicationBootstrap
{
  constructor(
    @Inject(DOMAIN_EVENT_CONSUMER)
    private readonly domainEventConsumer: DomainEventConsumer<AlloraTopicMsgRegisterEvent>,
    private readonly commandBus: CommandBus,
  ) {}

  async onApplicationBootstrap() {
    await this.domainEventConsumer.consumeBatch(
      {
        topics: ['domain-event.input.allora.allora-mainnet-1.registration'],
        consumerName:
          AlloraTopicMsgRegisterEvent.getName() + '-allora-mainnet-1',
        maxBatchSize: 1,
      },
      async (events) => {
        for (const event of events) {
          await this.commandBus.execute(
            new ProcessTopicRegistrationCommand(event),
          );
        }
      },
    );
  }
}
