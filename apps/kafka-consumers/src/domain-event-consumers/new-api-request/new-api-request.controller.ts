import { BatchNewApiRequestCommand } from '@app/core/app/use-case/api/command/batch-new-api-request/batch-new-api-request.command';
import { NewApiRequestEvent } from '@app/core/domain/api/domain-event/new-api-request.event';
import {
  DOMAIN_EVENT_CONSUMER,
  DomainEventConsumer,
} from '@app/core/domain/domain-event/domain-event.consumer';
import { Controller, Inject, OnApplicationBootstrap } from '@nestjs/common';
import { CommandBus } from '@nestjs/cqrs';

@Controller()
export class NewApiRequestController implements OnApplicationBootstrap {
  constructor(
    @Inject(DOMAIN_EVENT_CONSUMER)
    private readonly domainEventConsumer: DomainEventConsumer<NewApiRequestEvent>,
    private readonly commandBus: CommandBus,
  ) {}

  async onApplicationBootstrap() {
    await this.domainEventConsumer.consumeBatch(
      {
        topics: [NewApiRequestEvent.getName()],
        consumerName: NewApiRequestEvent.getName(),
      },
      async (events) => {
        await this.commandBus.execute(new BatchNewApiRequestCommand(events));
      },
    );
  }
}
