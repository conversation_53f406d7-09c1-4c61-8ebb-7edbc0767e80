import { CommandUseCaseModule } from '@app/core/app/use-case/command.use-case.module';
import { DomainEventConsumerModule } from '@app/core/domain/domain-event/domain-event-consumer.module';
import { Module } from '@nestjs/common';
import { CqrsModule } from '@nestjs/cqrs';

import { AlloraNetworkInferenceDevNetController } from './allora-network-inference/allora-network-inference.allora-devnet-1.controller';
import { AlloraNetworkInferenceMainNetController } from './allora-network-inference/allora-network-inference.allora-mainnet-1.controller';
import { AlloraNetworkInferenceTestNetController } from './allora-network-inference/allora-network-inference.allora-testnet-1.controller';
import { AlloraTopicEmaScoresSetDevNetController } from './allora-topic-ema-scores-set/allora-topic-ema-scores-set.allora-devnet-1.controller';
import { AlloraTopicEmaScoresSetMainNetController } from './allora-topic-ema-scores-set/allora-topic-ema-scores-set.allora-mainnet-1.controller';
import { AlloraTopicEmaScoresSetTestNetController } from './allora-topic-ema-scores-set/allora-topic-ema-scores-set.allora-testnet-1.controller';
import { AlloraTopicReputerLossDevNetController } from './allora-topic-reputer-loss/allora-topic-reputer-loss.allora-devnet-1.controller';
import { AlloraTopicReputerLossMainNetController } from './allora-topic-reputer-loss/allora-topic-reputer-loss.allora-mainnet-1.controller';
import { AlloraTopicReputerLossTestNetController } from './allora-topic-reputer-loss/allora-topic-reputer-loss.allora-testnet-1.controller';
import { AlloraTopicReputerStakeChangedDevNetController } from './allora-topic-reputer-stake-changed/allora-topic-reputer-stake-changed.allora-devnet-1.controller';
import { AlloraTopicReputerStakeChangedMainNetController } from './allora-topic-reputer-stake-changed/allora-topic-reputer-stake-changed.allora-mainnet-1.controller';
import { AlloraTopicReputerStakeChangedTestNetController } from './allora-topic-reputer-stake-changed/allora-topic-reputer-stake-changed.allora-testnet-1.controller';
import { AlloraTopicRewardsSettledDevNetController } from './allora-topic-rewards-settled/allora-topic-rewards-settled.allora-devnet-1.controller';
import { AlloraTopicRewardsSettledMainNetController } from './allora-topic-rewards-settled/allora-topic-rewards-settled.allora-mainnet-1.controller';
import { AlloraTopicRewardsSettledTestNetController } from './allora-topic-rewards-settled/allora-topic-rewards-settled.allora-testnet-1.controller';
import { AlloraTopicWorkerLossDevNetController } from './allora-topic-worker-loss/allora-topic-worker-loss.allora-devnet-1.controller';
import { AlloraTopicWorkerLossMainNetController } from './allora-topic-worker-loss/allora-topic-worker-loss.allora-mainnet-1.controller';
import { AlloraTopicWorkerLossTestNetController } from './allora-topic-worker-loss/allora-topic-worker-loss.allora-testnet-1.controller';
import { AlloraWalletBalanceChangedDevNetController } from './allora-wallet-balance-changed/allora-wallet-balance-changed.allora-devnet-1.controller';
import { AlloraWalletBalanceChangedMainNetController } from './allora-wallet-balance-changed/allora-wallet-balance-changed.allora-mainnet-1.controller';
import { AlloraWalletBalanceChangedTestNetController } from './allora-wallet-balance-changed/allora-wallet-balance-changed.allora-testnet-1.controller';
import { NewApiRequestController } from './new-api-request/new-api-request.controller';
import { RetryModule } from './retry/retry.module';
import { RoboNetVaultActivityController } from './vault-activity/robonet-vault-activity.controller';
import { RoboNetVaultCreatedController } from './vault-created/robonet-vault-created.controller';
import { RoboNetVaultReportedController } from './vault-reported/robonet-vault-reported.controller';

@Module({
  imports: [
    DomainEventConsumerModule,
    CqrsModule,
    CommandUseCaseModule,
    RetryModule,
  ],
  controllers: [
    NewApiRequestController,
    RoboNetVaultCreatedController,
    RoboNetVaultReportedController,
    RoboNetVaultActivityController,
    AlloraTopicWorkerLossTestNetController,
    AlloraTopicWorkerLossDevNetController,
    AlloraTopicWorkerLossMainNetController,
    AlloraTopicReputerLossTestNetController,
    AlloraTopicReputerLossDevNetController,
    AlloraTopicReputerLossMainNetController,
    AlloraTopicRewardsSettledTestNetController,
    AlloraTopicRewardsSettledDevNetController,
    AlloraTopicRewardsSettledMainNetController,
    AlloraTopicEmaScoresSetTestNetController,
    AlloraTopicEmaScoresSetDevNetController,
    AlloraTopicEmaScoresSetMainNetController,
    AlloraWalletBalanceChangedTestNetController,
    AlloraWalletBalanceChangedDevNetController,
    AlloraWalletBalanceChangedMainNetController,
    AlloraTopicReputerStakeChangedTestNetController,
    AlloraTopicReputerStakeChangedDevNetController,
    AlloraTopicReputerStakeChangedMainNetController,
    AlloraNetworkInferenceTestNetController,
    AlloraNetworkInferenceDevNetController,
    AlloraNetworkInferenceMainNetController,
  ],
})
export class DomainEventConsumersModule {}
