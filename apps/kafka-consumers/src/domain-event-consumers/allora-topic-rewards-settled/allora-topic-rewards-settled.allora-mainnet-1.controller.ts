import { ProcessTopicRewardsSettledsCommand } from '@app/core/app/use-case/allora/topic/topic-rewards-settled/process-topic-rewards-settled.command';
import { AlloraTopicRewardsSettled } from '@app/core/domain/allora/topic/events/allora-topic-event-rewards-settled';
import {
  DOMAIN_EVENT_CONSUMER,
  DomainEventConsumer,
} from '@app/core/domain/domain-event/domain-event.consumer';
import { Controller, Inject, OnApplicationBootstrap } from '@nestjs/common';
import { CommandBus } from '@nestjs/cqrs';

@Controller()
export class AlloraTopicRewardsSettledMainNetController
  implements OnApplicationBootstrap
{
  constructor(
    @Inject(DOMAIN_EVENT_CONSUMER)
    private readonly domainEventConsumer: DomainEventConsumer<AlloraTopicRewardsSettled>,
    private readonly commandBus: CommandBus,
  ) {}

  async onApplicationBootstrap() {
    await this.domainEventConsumer.consumeBatch(
      {
        topics: ['domain-event.input.allora.allora-mainnet-1.rewards'],
        consumerName: AlloraTopicRewardsSettled.getName() + '-allora-mainnet-1',
        maxBatchSize: 1,
      },
      async (events) => {
        for (const event of events) {
          await this.commandBus.execute(
            new ProcessTopicRewardsSettledsCommand(event),
          );
        }
      },
    );
  }
}
