import { ProcessTopicTotalStakedCommand } from '@app/core/app/use-case/allora/topic/topic-total-staked/process-topic-total-staked.command';
import { EAlloraChainId } from '@app/core/domain/allora/chain/allora-chain-config';
import {
  AlloraTopicTotalStaked,
  EventAlloraTopicTotalStaked,
} from '@app/core/domain/allora/topic/events/allora-topic-total-staked.event';
import {
  DOMAIN_EVENT_CONSUMER,
  DomainEventConsumer,
} from '@app/core/domain/domain-event/domain-event.consumer';
import { Controller, Inject, OnApplicationBootstrap } from '@nestjs/common';
import { CommandBus } from '@nestjs/cqrs';

@Controller()
export class AlloraTopicTotalStakedTestNetController
  implements OnApplicationBootstrap
{
  constructor(
    @Inject(DOMAIN_EVENT_CONSUMER)
    private readonly domainEventConsumer: DomainEventConsumer<AlloraTopicTotalStaked>,
    private readonly commandBus: CommandBus,
  ) {}

  async onApplicationBootstrap() {
    await this.domainEventConsumer.consumeBatch(
      {
        topics: ['allora_topic_total_staked_aggregation'],
        consumerName: EventAlloraTopicTotalStaked.getName(),
        maxBatchSize: 1,
      },
      async (events) => {
        for (const event of events) {
          // manually stitch the chain_id to the event
          event.chain_id = EAlloraChainId.ALLORA_TESTNET_1;
          await this.commandBus.execute(
            new ProcessTopicTotalStakedCommand(
              new EventAlloraTopicTotalStaked(event),
            ),
          );
        }
      },
    );
  }
}
