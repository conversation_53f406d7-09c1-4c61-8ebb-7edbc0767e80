import {
  DOMAIN_EVENT_CONSUMER,
  DomainEventConsumer,
} from '@app/core/domain/domain-event/domain-event.consumer';
import {
  DOMAIN_EVENT_PUBLISHER,
  DomainEventPublisher,
} from '@app/core/domain/domain-event/domain-event-publisher';
import { RetryDomainEvent } from '@app/core/domain/domain-event/retry-domain-event';
import { RetryHandler } from '@app/core/infra/transport/domain-event/retry.handler';
import { Controller, Inject, OnApplicationBootstrap } from '@nestjs/common';

@Controller('retry')
export class RetryController implements OnApplicationBootstrap {
  constructor(
    @Inject(DOMAIN_EVENT_CONSUMER)
    private readonly domainEventConsumer: DomainEventConsumer<
      RetryDomainEvent<any>
    >,
    @Inject(DOMAIN_EVENT_PUBLISHER)
    private readonly domainEventPublisher: DomainEventPublisher<any>,
    private readonly retryHandler: RetryHandler,
  ) {}

  async onApplicationBootstrap() {
    this.domainEventConsumer.consumeRetryBatch(
      {
        topics: this.retryHandler.retryTopics.map((t) => t.name),
        consumerName: 'domain-event.retry',
      },
      async (events) => {
        for (const event of events) {
          await this.domainEventPublisher.publishBatch([
            {
              id: event.id,
              name: event.rerouteKey,
              payload: event.payload,
              timestamp: event.timestamp,
              retryCount: event.retryCount,
            },
          ]);
        }
      },
    );
  }
}
