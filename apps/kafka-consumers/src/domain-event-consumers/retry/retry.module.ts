import { DomainEventPublisherModule } from '@app/core/domain/domain-event/domain-event.publisher.module';
import { DomainEventConsumerModule } from '@app/core/domain/domain-event/domain-event-consumer.module';
import { RetryHandlerModule } from '@app/core/domain/domain-event/retry-handler.module';
import { Module } from '@nestjs/common';

import { RetryController } from './retry.controller';

@Module({
  imports: [
    DomainEventConsumerModule,
    DomainEventPublisherModule,
    RetryHandlerModule,
  ],
  controllers: [RetryController],
})
export class RetryModule {}
