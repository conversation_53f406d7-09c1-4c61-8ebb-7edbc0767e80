import { CreateVaultCommand } from '@app/core/app/use-case/robonet/vault/command/create-vault/create-vault.command';
import {
  DOMAIN_EVENT_CONSUMER,
  DomainEventConsumer,
} from '@app/core/domain/domain-event/domain-event.consumer';
import { RoboNetNewVaultEvent } from '@app/core/domain/robonet/vault/events/robonet-new-vault.event';
import { Controller, Inject, OnApplicationBootstrap } from '@nestjs/common';
import { CommandBus } from '@nestjs/cqrs';

@Controller()
export class RoboNetVaultCreatedController implements OnApplicationBootstrap {
  constructor(
    @Inject(DOMAIN_EVENT_CONSUMER)
    private readonly domainEventConsumer: DomainEventConsumer<RoboNetNewVaultEvent>,
    private readonly commandBus: CommandBus,
  ) {}

  async onApplicationBootstrap() {
    await this.domainEventConsumer.consumeBatch(
      {
        topics: [
          'domain-event.output.protocol-robonetv2-ethereum.RoboNetNewTokenizedStrategy',
        ],
        consumerName: RoboNetNewVaultEvent.getName(),
        maxBatchSize: 1,
      },
      async (events) => {
        for (const event of events) {
          await this.commandBus.execute(new CreateVaultCommand(event));
        }
      },
    );
  }
}
