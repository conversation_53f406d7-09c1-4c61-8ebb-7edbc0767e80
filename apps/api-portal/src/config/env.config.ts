import { config } from 'dotenv';
import * as env from 'env-var';

config();

export const API_PORTAL_PORT = env
  .get('API_PORTAL_PORT')
  .default('13001')
  .asInt();

export const JWT_API_PORTAL_SECRET = env
  .get('JWT_API_PORTAL_SECRET')
  .required()
  .asString();

export const JWT_ACCESS_TOKEN_EXPIRES_IN = env
  .get('JWT_ACCESS_TOKEN_EXPIRES_IN')
  .default('15m')
  .asString();

export const JWT_REFRESH_TOKEN_EXPIRES_IN = env
  .get('JWT_REFRESH_TOKEN_EXPIRES_IN')
  .default('7d')
  .asString();
export const JWT_REFRESH_TOKEN_SECRET = env
  .get('JWT_REFRESH_TOKEN_SECRET')
  .required()
  .asString();

export const RECAPTCHA_SECRET = env
  .get('RECAPTCHA_SECRET')
  .required()
  .asString();

export const STRIPE_SECRET_KEY = env
  .get('STRIPE_SECRET_KEY')
  .required()
  .asString();

export const STRIPE_WEBHOOK_SECRET = env
  .get('STRIPE_WEBHOOK_SECRET')
  .required()
  .asString();

export const JWT_ONE_TIME_LOGIN_TOKEN_SECRET = env
  .get('JWT_ONE_TIME_LOGIN_TOKEN_SECRET')
  .required()
  .asString();
