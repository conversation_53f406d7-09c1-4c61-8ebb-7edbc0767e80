import { ENodeEnv, NODE_ENV } from '@app/core/config/env';
import { ValidationPipe } from '@nestjs/common';
import { NestFactory } from '@nestjs/core';
import { NestExpressApplication } from '@nestjs/platform-express';
import { SwaggerModule } from '@nestjs/swagger';
import * as compression from 'compression';
import * as cookieParser from 'cookie-parser';
import * as basicAuth from 'express-basic-auth';
import * as helmet from 'helmet';
import * as morgan from 'morgan';
import { Logger } from 'nestjs-pino';

import { AppModule } from './app.module';
import { docs } from './docs';

export const app = async (): Promise<NestExpressApplication> => {
  const app = await NestFactory.create<NestExpressApplication>(AppModule, {
    bufferLogs: ![ENodeEnv.TEST, ENodeEnv.DEV].includes(NODE_ENV),
    rawBody: true,
  });
  app.useLogger(app.get(Logger));
  console.log = app.get(Logger).debug.bind(app.get(Logger));
  const documents = docs(app);
  if (documents) {
    app.use(
      ['/docs', '/docs-json'],
      basicAuth({
        challenge: true,
        users: {
          upshot: 'gm',
        },
      }),
    );
    SwaggerModule.setup('docs', app, documents);
  }

  app.use(morgan('short'));
  app.useGlobalPipes(
    new ValidationPipe({
      whitelist: true,
    }),
  );
  app.use(compression());
  app.use(helmet());
  app.use(cookieParser());
  app.set('trust proxy', 1);
  app.disable('x-powered-by');
  app.enableCors();
  return app;
};
