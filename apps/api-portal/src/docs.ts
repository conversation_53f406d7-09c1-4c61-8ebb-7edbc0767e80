import { ENodeEnv, NODE_ENV } from '@app/core/config/env';
import { Logger } from '@nestjs/common';
import { NestExpressApplication } from '@nestjs/platform-express';
import { DocumentBuilder, OpenAPIObject, SwaggerModule } from '@nestjs/swagger';

export const docs = (app: NestExpressApplication): OpenAPIObject | void => {
  const logger = new Logger('API-Portal-Docs');
  logger.log('Creating API Portal Docs...');

  if (NODE_ENV === ENodeEnv.PROD) return;

  const config = new DocumentBuilder()
    .setTitle('Upshot API Portal')
    .setDescription('The Upshot API Portal documentation.')
    .setVersion('1.0')
    .addSecurity('ApiKeyAuth', {
      type: 'apiKey',
      in: 'header',
      name: 'Authorization',
    })
    .addSecurityRequirements('ApiKeyAuth')
    .build();

  const document = SwaggerModule.createDocument(app, config, {
    ignoreGlobalPrefix: false,
  });

  return document;
};
