import { ENodeEnv, NODE_ENV } from '@app/core/config/env';
import { Logger } from '@nestjs/common';

import { app } from './app';
import { API_PORTAL_PORT } from './config/env.config';

const logger = new Logger('api-portal');

// from https://nodejs.org/api/process.html#event-unhandledrejection
process.on('unhandledRejection', (reason: any, promise) => {
  logger.error(`Unhandled Rejection at: ${promise}`);
  logger.error(reason);
  logger.error(reason.stack);
});

async function bootstrap() {
  const restApi = await app();

  try {
    // quit on ctrl-c when running docker in terminal
    NODE_ENV === ENodeEnv.PROD
      ? process.on('SIGINT', () => {
          logger.log(
            'Got SIGINT (aka ctrl-c in docker). Graceful shutdown ',
            new Date().toISOString(),
          );
          restApi.close();
        })
      : '';

    // quit properly on docker stop
    NODE_ENV === ENodeEnv.PROD
      ? process.on('SIGTERM', () => {
          logger.log(
            'Got SIGTERM (docker container stop). Graceful shutdown ',
            new Date().toISOString(),
          );
          restApi.close();
        })
      : '';
  } catch (e) {
    logger.error(e);
  }
  await restApi.listen(API_PORTAL_PORT || 13001);
  logger.log(
    `Upshot API Portal started on ${process.env.NODE_ENV} environment with port ${API_PORTAL_PORT}`,
  );
}
bootstrap();
