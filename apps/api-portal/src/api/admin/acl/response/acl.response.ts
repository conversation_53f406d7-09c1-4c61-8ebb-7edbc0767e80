import { AccessControlRule } from '@app/core/domain/api/accress-control/access-control-rule';

export class AclResponse {
  id: string;
  endpoint: string;
  tier_id: number;
  monthly_limit: number | null;

  constructor(accessControlRule: AccessControlRule) {
    this.id = accessControlRule.id.toString();
    this.endpoint = accessControlRule.endpoint;
    this.tier_id = accessControlRule.tierId.value;
    this.monthly_limit = accessControlRule.monthlyLimit;
  }
}
