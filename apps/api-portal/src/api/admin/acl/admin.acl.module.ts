import { AccessControlRuleRepositoryModule } from '@app/core/domain/api/accress-control/access-control-rule.repository.module';
import { TierRepositoryModule } from '@app/core/domain/api/api-tier/tier.repository.module';
import { Module } from '@nestjs/common';

import { AdminAclController } from './admin.acl.controller';

@Module({
  imports: [AccessControlRuleRepositoryModule, TierRepositoryModule],
  controllers: [AdminAclController],
})
export class AdminAclModule {}
