import { AccessControlRule } from '@app/core/domain/api/accress-control/access-control-rule';
import {
  ACCESS_CONTROL_RULE_REPOSITORY,
  AccessControlRuleRepository,
} from '@app/core/domain/api/accress-control/access-control-rule.repository';
import { TierId } from '@app/core/domain/api/api-tier/tier.id';
import {
  TIER_REPOSITORY,
  TierRepository,
} from '@app/core/domain/api/api-tier/tier.repository';
import {
  Body,
  Controller,
  Delete,
  Get,
  Inject,
  NotFoundException,
  Param,
  Post,
} from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';

import { Admin } from '../../../decorator/admin.decorator';
import { CreateAclDto } from './dto/create.acl.dto';
import { AclResponse } from './response/acl.response';

@Controller('admin/acl')
@ApiTags('Admin ACL')
export class AdminAclController {
  constructor(
    @Inject(ACCESS_CONTROL_RULE_REPOSITORY)
    private readonly accessControlRuleRepository: AccessControlRuleRepository,
    @Inject(TIER_REPOSITORY)
    private readonly tierRepository: TierRepository,
  ) {}

  @Post()
  @Admin()
  async createAclRule(@Body() body: CreateAclDto) {
    const tier = await this.tierRepository.getById(new TierId(body.tier_id));
    if (!tier) {
      throw new NotFoundException(`Tier ${body.tier_id} not found`);
    }
    const rule = new AccessControlRule({
      id: await this.accessControlRuleRepository.nextId(),
      endpoint: body.endpoint,
      tierId: tier.id,
      monthlyLimit: body.monthly_limit,
      createdAt: new Date(),
    });
    await this.accessControlRuleRepository.persist(rule);
    return new AclResponse(rule);
  }

  @Post('bulk')
  @Admin()
  async bulkCreateAclRule(@Body() body: CreateAclDto[]) {
    const response: AclResponse[] = [];
    for (const rule of body) {
      const aclRule = await this.createAclRule(rule);
      response.push(aclRule);
    }
    return response;
  }

  @Delete(':ruleId')
  @Admin()
  async deleteAclRule(@Param('ruleId') ruleId: string) {
    const rule = await this.accessControlRuleRepository.getById(ruleId);
    if (!rule) {
      throw new NotFoundException(`Rule ${ruleId} not found`);
    }
    await this.accessControlRuleRepository.remove(rule);
  }

  @Get('tiers/:tierId')
  @Admin()
  async getAclRulesByTierId(@Param('tierId') tierId: number) {
    const tier = await this.tierRepository.getById(new TierId(tierId));
    if (!tier) {
      throw new NotFoundException(`Tier ${tierId} not found`);
    }
    const acls = await this.accessControlRuleRepository.getByTierId(
      new TierId(tierId),
    );
    return acls.map((acl) => new AclResponse(acl));
  }

  @Get('endpoints')
  @Admin()
  async getAclEndpoints() {
    return this.accessControlRuleRepository.getEndpoints();
  }
}
