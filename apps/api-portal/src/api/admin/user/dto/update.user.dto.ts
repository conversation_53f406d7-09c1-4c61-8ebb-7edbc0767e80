import { ApiProperty } from '@nestjs/swagger';
import { IsBoolean, IsNumber, IsOptional, IsString } from 'class-validator';

export class UpdateUserDto {
  @IsString()
  @IsOptional()
  @ApiProperty()
  name: string;

  @IsString()
  @IsOptional()
  @ApiProperty()
  email: string;

  @IsBoolean()
  @IsOptional()
  @ApiProperty()
  active: boolean;

  @IsNumber()
  @IsOptional()
  @ApiProperty()
  overage_request_cap: number;

  @IsBoolean()
  @IsOptional()
  @ApiProperty()
  overage_warning_alerts: boolean;
}
