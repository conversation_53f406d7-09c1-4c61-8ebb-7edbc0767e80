import { TierId } from '@app/core/domain/api/api-tier/tier.id';
import {
  TIER_REPOSITORY,
  TierRepository,
} from '@app/core/domain/api/api-tier/tier.repository';
import { ApiUser } from '@app/core/domain/api/user/api-user';
import { ApiUserId } from '@app/core/domain/api/user/api-user.id';
import {
  API_USER_REPOSITORY,
  ApiUserRepository,
} from '@app/core/domain/api/user/api-user.repository';
import {
  Body,
  Controller,
  Get,
  Inject,
  NotFoundException,
  Param,
  Post,
  Put,
} from '@nestjs/common';
import { ApiBody, ApiTags } from '@nestjs/swagger';

import { Admin } from '../../../decorator/admin.decorator';
import { AuthService } from '../../auth/auth.service';
import { CreateUserDto } from './dto/create.user.dto';
import { OneTimeLoginDto } from './dto/one-time-login.dto';
import { UpdateUserDto } from './dto/update.user.dto';
import { UserResponse } from './response/user.response';

@Controller('admin/users')
@ApiTags('Admin Users')
export class AdminUserController {
  constructor(
    @Inject(API_USER_REPOSITORY)
    private readonly apiUserRepository: ApiUserRepository,
    @Inject(TIER_REPOSITORY)
    private readonly tierRepository: TierRepository,
    private readonly authService: AuthService,
  ) {}

  @Get()
  @Admin()
  async getUsers() {
    const users = await this.apiUserRepository.getAll();
    return users.map((user) => new UserResponse(user));
  }

  @Get(':userId')
  @Admin()
  async getUserById(@Param('userId') userId: string) {
    const user = await this.apiUserRepository.getById(new ApiUserId(userId));
    if (!user) {
      throw new NotFoundException(`User ${userId} not found`);
    }
    return new UserResponse(user);
  }

  @Put(':userId')
  @ApiBody({ type: UpdateUserDto })
  @Admin()
  async updateUser(
    @Body() body: UpdateUserDto,
    @Param('userId') userId: string,
  ) {
    const apiUser = await this.apiUserRepository.getById(new ApiUserId(userId));
    if (!apiUser) {
      throw new NotFoundException(`User ${userId} not found`);
    }
    if (body.email !== undefined) {
      apiUser.email = body.email;
    }
    if (body.name !== undefined) {
      apiUser.name = body.name;
    }
    if (body.active !== undefined) {
      apiUser.active = body.active;
    }
    if (body.overage_warning_alerts !== undefined) {
      apiUser.overageWarningAlerts = body.overage_warning_alerts;
    }
    if (body.overage_request_cap !== undefined) {
      apiUser.overageRequestCap = body.overage_request_cap;
    }
    await this.apiUserRepository.persist(apiUser);
  }

  @Post()
  @ApiBody({ type: CreateUserDto })
  @Admin()
  async createApiUser(@Body() body: CreateUserDto) {
    const apiUser = await this.apiUserRepository.getByEmail(body.email);
    if (apiUser) {
      throw new Error(`User ${body.email} already exists`);
    }
    const tier = await this.tierRepository.getById(new TierId(0));
    const newApiUser = new ApiUser({
      id: await this.apiUserRepository.getNextId(),
      email: body.email,
      name: body.name,
      active: body.active,
      overageWarningAlerts: body.overage_warning_alerts,
      _overageRequestCap: body.overage_request_cap,
      createdAt: new Date(),
      apiKeys: [],
      tier: tier,
      billingDay: new Date().getDate(),
      password: null,
      refreshToken: null,
      googleId: null,
      paymentAccount: null,
      userId: null,
      oneTimeLoginToken: null,
      isEmailConfirmed: false,
      lastOverageEmailAtPercent: 0,
    });
    await this.apiUserRepository.persist(newApiUser);
  }

  @Post(':userId/send-one-time-login-email')
  @ApiBody({ type: OneTimeLoginDto })
  @Admin()
  async sendOneTimeLoginEmail(
    @Param('userId') userId: string,
    @Body() body: OneTimeLoginDto,
  ) {
    const apiUser = await this.apiUserRepository.getById(new ApiUserId(userId));
    if (!apiUser) {
      throw new NotFoundException(`User ${userId} not found`);
    }
    if (!apiUser.email) {
      throw new Error(`User ${userId} has no email`);
    }
    await this.authService.generateOneTimeLoginForgotPassword(
      {
        email: apiUser.email,
        redirect_url: body.redirect_url,
        captcha_token: '',
      },
      false,
    );
    return { message: 'ok' };
  }
}
