import { TierRepositoryModule } from '@app/core/domain/api/api-tier/tier.repository.module';
import { ApiUserRepositoryModule } from '@app/core/domain/api/user/api-user.repository.module';
import { Module } from '@nestjs/common';

import { AuthModule } from '../../auth/auth.module';
import { AdminUserController } from './admin.user.controller';

@Module({
  imports: [ApiUserRepositoryModule, TierRepositoryModule, AuthModule],
  controllers: [AdminUserController],
})
export class AdminUserModule {}
