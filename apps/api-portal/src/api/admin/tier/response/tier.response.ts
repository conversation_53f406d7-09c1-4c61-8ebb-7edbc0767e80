import { Tier } from '@app/core/domain/api/api-tier/tier';

export class TierResponse {
  id: number;
  name: string;
  monthly_limit: number;
  requests_per_second: number;
  overage_cost_per_request: number;
  created_at: Date;

  constructor(tier: Tier) {
    this.id = tier.id.value;
    this.name = tier.name;
    this.monthly_limit = tier.monthlyLimit;
    this.requests_per_second = tier.requestsPerSecond;
    this.overage_cost_per_request = tier.overageCostPerRequest;
    this.created_at = tier.createdAt;
  }
}
