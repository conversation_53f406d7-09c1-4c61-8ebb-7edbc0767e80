import { AccessControlRuleRepositoryModule } from '@app/core/domain/api/accress-control/access-control-rule.repository.module';
import { TierRepositoryModule } from '@app/core/domain/api/api-tier/tier.repository.module';
import { Module } from '@nestjs/common';

import { AdminTierController } from './admin.tier.controller';

@Module({
  imports: [TierRepositoryModule, AccessControlRuleRepositoryModule],
  controllers: [AdminTierController],
})
export class AdminTierModule {}
