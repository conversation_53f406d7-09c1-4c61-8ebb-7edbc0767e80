import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsNumber, IsString } from 'class-validator';

export class CreateTierDto {
  @IsString()
  @IsNotEmpty()
  @ApiProperty()
  name: string;

  @IsNumber()
  @IsNotEmpty()
  @ApiProperty()
  monthly_limit: number;

  @IsNumber()
  @IsNotEmpty()
  @ApiProperty()
  requests_per_second: number;

  @IsNumber()
  @IsNotEmpty()
  @ApiProperty()
  overage_cost_per_request: number;
}
