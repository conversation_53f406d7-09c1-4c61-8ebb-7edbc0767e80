import { AccessControlRule } from '@app/core/domain/api/accress-control/access-control-rule';
import {
  ACCESS_CONTROL_RULE_REPOSITORY,
  AccessControlRuleRepository,
} from '@app/core/domain/api/accress-control/access-control-rule.repository';
import { Tier } from '@app/core/domain/api/api-tier/tier';
import { TierId } from '@app/core/domain/api/api-tier/tier.id';
import {
  TIER_REPOSITORY,
  TierRepository,
} from '@app/core/domain/api/api-tier/tier.repository';
import {
  Body,
  Controller,
  Get,
  Inject,
  NotFoundException,
  Param,
  Post,
} from '@nestjs/common';
import { ApiBody, ApiTags } from '@nestjs/swagger';

import { Admin } from '../../../decorator/admin.decorator';
import { CreateTierDto } from './dto/create.tier.dto';
import { CreateTierFromDto } from './dto/create.tier.from.dto';
import { TierResponse } from './response/tier.response';

@Controller('admin/tiers')
@ApiTags('Admin Tiers')
export class AdminTierController {
  constructor(
    @Inject(TIER_REPOSITORY)
    private readonly tierRepository: TierRepository,
    @Inject(ACCESS_CONTROL_RULE_REPOSITORY)
    private readonly accessControlRuleRepository: AccessControlRuleRepository,
  ) {}

  @Post('copy')
  @ApiBody({ type: CreateTierFromDto })
  @Admin()
  async createTierFrom(@Body() body: CreateTierFromDto) {
    const fromTier = await this.tierRepository.getById(
      new TierId(body.from_tier_id),
    );
    if (!fromTier) {
      throw new NotFoundException(`Tier ${body.from_tier_id} not found`);
    }
    const toTier = new Tier({
      id: await this.tierRepository.nextId(),
      name: 'Copy of ' + fromTier.name,
      monthlyLimit: fromTier.monthlyLimit,
      requestsPerSecond: fromTier.requestsPerSecond,
      overageCostPerRequest: fromTier.overageCostPerRequest,
      paymentProductId: null,
      createdAt: new Date(),
    });
    await this.tierRepository.persist(toTier);

    const accessControlRules =
      await this.accessControlRuleRepository.getByTierId(fromTier.id);

    for (const rule of accessControlRules) {
      await this.accessControlRuleRepository.persist(
        new AccessControlRule({
          id: await this.accessControlRuleRepository.nextId(),
          endpoint: rule.endpoint,
          tierId: toTier.id,
          monthlyLimit: rule.monthlyLimit,
          createdAt: new Date(),
        }),
      );
    }
  }

  @Post()
  @ApiBody({ type: CreateTierDto })
  @Admin()
  async createTier(@Body() body: CreateTierDto) {
    const tier = new Tier({
      id: await this.tierRepository.nextId(),
      name: body.name,
      monthlyLimit: body.monthly_limit,
      requestsPerSecond: body.requests_per_second,
      overageCostPerRequest: body.overage_cost_per_request,
      paymentProductId: null,
      createdAt: new Date(),
    });
    await this.tierRepository.persist(tier);
    return new TierResponse(tier);
  }

  @Get()
  @Admin()
  async getTiers() {
    return this.tierRepository.get();
  }

  @Get(':tierId')
  @Admin()
  async getTierById(@Param('tierId') tierId: number) {
    const tier = await this.tierRepository.getById(new TierId(tierId));
    return new TierResponse(tier);
  }
}
