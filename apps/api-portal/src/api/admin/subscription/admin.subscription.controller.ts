import { TierId } from '@app/core/domain/api/api-tier/tier.id';
import {
  TIER_REPOSITORY,
  TierRepository,
} from '@app/core/domain/api/api-tier/tier.repository';
import { PaymentSubscription } from '@app/core/domain/api/payment/subscription/payment-subscription';
import {
  PAYMENT_SUBSCRIPTION_REPOSITORY,
  PaymentSubscriptionRepository,
} from '@app/core/domain/api/payment/subscription/payment-subscription.repository';
import { ApiUserId } from '@app/core/domain/api/user/api-user.id';
import {
  API_USER_REPOSITORY,
  ApiUserRepository,
} from '@app/core/domain/api/user/api-user.repository';
import {
  Body,
  Controller,
  Get,
  Inject,
  NotFoundException,
  Param,
  Post,
} from '@nestjs/common';
import { ApiBody, ApiTags } from '@nestjs/swagger';

import { Admin } from '../../../decorator/admin.decorator';
import { CreateSubscriptionDto } from './dto/create.subscription.dto';
import { SubscriptionResponse } from './response/subscription.response';

@Controller('admin/subscriptions')
@ApiTags('Admin Subscriptions')
export class AdminSubscriptionController {
  constructor(
    @Inject(API_USER_REPOSITORY)
    private readonly apiUserRepository: ApiUserRepository,
    @Inject(TIER_REPOSITORY)
    private readonly tierRepository: TierRepository,
    @Inject(PAYMENT_SUBSCRIPTION_REPOSITORY)
    private readonly paymentSubscriptionRepository: PaymentSubscriptionRepository,
  ) {}

  @Post()
  @ApiBody({ type: CreateSubscriptionDto })
  @Admin()
  async createSubscription(@Body() body: CreateSubscriptionDto) {
    const user = await this.apiUserRepository.getById(
      new ApiUserId(body.user_id),
    );
    if (!user) {
      throw new NotFoundException(`User ${body.user_id} not found`);
    }

    const tier = await this.tierRepository.getById(new TierId(body.tier_id));
    if (!tier) {
      throw new NotFoundException(`Tier ${body.tier_id} not found`);
    }

    const subscription = new PaymentSubscription({
      id: await this.paymentSubscriptionRepository.getNextId(),
      providerSubscriptionId: 'manual',
      apiUserId: user.id,
      tierId: tier.id,
      startsAt: new Date(),
      endsAt: body.ends_at,
      isCancelled: false,
      status: 'active',
      createdAt: new Date(),
      updatedAt: new Date(),
    });
    await this.paymentSubscriptionRepository.persist(subscription);
    return new SubscriptionResponse(subscription);
  }

  @Get('user/:userId/active')
  @Admin()
  async getSubscriptionsByUserId(@Param('userId') userId: string) {
    const user = await this.apiUserRepository.getById(new ApiUserId(userId));
    if (!user) {
      throw new NotFoundException(`User ${userId} not found`);
    }

    const subscription =
      await this.paymentSubscriptionRepository.getActiveByApiUserId(user.id);
    if (!subscription) {
      throw new NotFoundException(`User ${userId} has no active subscription`);
    }
    return new SubscriptionResponse(subscription);
  }
}
