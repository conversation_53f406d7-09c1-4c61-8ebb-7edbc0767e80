import { PaymentSubscription } from '@app/core/domain/api/payment/subscription/payment-subscription';

export class SubscriptionResponse {
  id: string;
  provider_subscription_id: string;
  user_id: string;
  tier_id: number;
  starts_at: Date;
  ends_at: Date;
  is_cancelled: boolean;
  status: string;
  created_at: Date;
  updated_at: Date;

  constructor(subscription: PaymentSubscription) {
    this.id = subscription.id.value;
    this.provider_subscription_id = subscription.providerSubscriptionId;
    this.user_id = subscription.apiUserId.value;
    this.tier_id = subscription.tierId.value;
    this.starts_at = subscription.startsAt;
    this.ends_at = subscription.endsAt;
    this.is_cancelled = subscription.isCancelled;
    this.status = subscription.status;
    this.created_at = subscription.createdAt;
    this.updated_at = subscription.updatedAt;
  }
}
