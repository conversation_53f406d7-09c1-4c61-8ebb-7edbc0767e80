import { TierRepositoryModule } from '@app/core/domain/api/api-tier/tier.repository.module';
import { PaymentSubscriptionRepositoryModule } from '@app/core/domain/api/payment/subscription/payment-subscription.repository.module';
import { ApiUserRepositoryModule } from '@app/core/domain/api/user/api-user.repository.module';
import { Module } from '@nestjs/common';

import { AdminSubscriptionController } from './admin.subscription.controller';

@Module({
  imports: [
    ApiUserRepositoryModule,
    TierRepositoryModule,
    PaymentSubscriptionRepositoryModule,
  ],
  controllers: [AdminSubscriptionController],
})
export class AdminSubscriptionModule {}
