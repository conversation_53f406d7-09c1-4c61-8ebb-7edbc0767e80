import {
  TIER_REPOSITORY,
  TierRepository,
} from '@app/core/domain/api/api-tier/tier.repository';
import { StripeClient } from '@app/core/infra/providers/stripe/stripe-client';
import { Trace } from '@app/core/infra/utils/tracing.decorator';
import { Controller, Get, Inject } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';

import { Public } from '../../decorator/public.decorator';
import { TierResponse } from './response/tier.response';

@Controller('tiers')
@ApiTags('Tiers Endpoints')
export class TiersController {
  constructor(
    @Inject(TIER_REPOSITORY)
    private readonly tierRepository: TierRepository,
    private readonly stripeClient: StripeClient,
  ) {}

  @Get()
  @Trace
  @Public()
  async get() {
    const tiers = await this.tierRepository.get();
    const stripePrices = await this.stripeClient.retrievePrices();
    const stripeProducts = await this.stripeClient.retrieveProducts();
    const tiersWithStripe = tiers.map((tier) => {
      const stripePrice = stripePrices.data.filter(
        (price) => price.product === tier.paymentProductId,
      );
      const stripeProduct = stripeProducts.data.find(
        (product) => product.id === tier.paymentProductId,
      );
      return {
        ...new TierResponse(tier),
        stripe_prices: stripePrice,
        stripe_product: stripeProduct,
      };
    });
    return tiersWithStripe.filter((tier) => tier.stripe_prices.length > 0);
  }
}
