import { Tier } from '@app/core/domain/api/api-tier/tier';

export class TierResponse {
  readonly id: number;
  readonly name: string;
  readonly monthly_limit: number;
  readonly requests_per_second: number;
  readonly overage_cost_per_request: number;
  readonly payment_product_id: string | null;
  readonly created_at: Date;

  constructor(tier: Tier) {
    this.id = tier.id.value;
    this.name = tier.name;
    this.monthly_limit = tier.monthlyLimit;
    this.requests_per_second = tier.requestsPerSecond;
    this.overage_cost_per_request = tier.overageCostPerRequest;
    this.payment_product_id = tier.paymentProductId;
    this.created_at = tier.createdAt;
  }
}
