import { TierRepositoryModule } from '@app/core/domain/api/api-tier/tier.repository.module';
import { StripeClientModule } from '@app/core/infra/providers/stripe/stripe-client.module';
import { Module } from '@nestjs/common';

import { TiersController } from './tiers.controller';

@Module({
  imports: [TierRepositoryModule, StripeClientModule],
  controllers: [TiersController],
})
export class TiersModule {}
