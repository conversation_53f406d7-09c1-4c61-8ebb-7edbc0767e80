import { NewPaymentCheckoutRequest } from '@app/core/app/use-case/api/payment/new.payment-checkout.request';
import { NewPaymentCheckoutUseCase } from '@app/core/app/use-case/api/payment/new.payment-checkout.use-case';
import { NewSubscriptionRequest } from '@app/core/app/use-case/api/payment/new.subscription.request';
import { NewSubscriptionUseCase } from '@app/core/app/use-case/api/payment/new.subscription.use-case';
import { UpdateSubscriptionRequest } from '@app/core/app/use-case/api/payment/update.subscription.request';
import { UpdateSubscriptionUseCase } from '@app/core/app/use-case/api/payment/update.subscription.use-case';
import { ApiUser } from '@app/core/domain/api/user/api-user';
import { StripeClient } from '@app/core/infra/providers/stripe/stripe-client';
import { Trace } from '@app/core/infra/utils/tracing.decorator';
import {
  Body,
  Controller,
  ForbiddenException,
  Logger,
  Post,
  RawBodyRequest,
  Req,
} from '@nestjs/common';
import { ApiBody, ApiTags } from '@nestjs/swagger';

import { STRIPE_WEBHOOK_SECRET } from '../../config/env.config';
import { Public } from '../../decorator/public.decorator';
import { User } from '../../decorator/user.decorator';
import { CheckoutDto } from './dto/checkout.dto';
import { PortalDto } from './dto/portal.dto';

@Controller('/payments')
@ApiTags('Payments Endpoints')
export class PaymentController {
  private readonly logger = new Logger(PaymentController.name);

  constructor(
    private readonly newPaymentCheckoutUseCase: NewPaymentCheckoutUseCase,
    private readonly stripeClient: StripeClient,
    private readonly newSubscriptionUseCase: NewSubscriptionUseCase,
    private readonly updateSubscriptionUseCase: UpdateSubscriptionUseCase,
  ) {}

  @Post('webhook')
  @Trace
  @Public()
  async webhook(@Req() request: RawBodyRequest<Request>): Promise<void> {
    this.logger.log('Processing Stripe Webhook...');
    if (!request.rawBody) throw new ForbiddenException();

    const signature = request.headers['stripe-signature'];
    const event = await this.stripeClient.verifyWebhookSignature(
      request.rawBody,
      signature,
      STRIPE_WEBHOOK_SECRET,
    );
    let subscription, checkoutSession, invoice;

    this.logger.log(`Received Stripe event of type: ${event.type}`);

    switch (event.type) {
      /**
       * When the checkout session is completed means the user has paid going through the checkout process.
       */
      case 'checkout.session.completed':
        checkoutSession = event.data.object;
        subscription = await this.stripeClient.retrieveSubscription(
          checkoutSession.subscription,
        );
        invoice = await this.stripeClient.retrieveInvoice(
          checkoutSession.invoice,
        );
        const newSubscriptionRequest = new NewSubscriptionRequest(
          subscription.customer,
          subscription.items.data[0].price.product,
          subscription.id,
          new Date(invoice.created),
          invoice.amount_paid / 100,
          new Date(subscription.current_period_start * 1000),
          new Date(subscription.current_period_end * 1000),
          subscription.cancel_at_period_end,
          subscription.status,
        );
        await this.newSubscriptionUseCase.execute(newSubscriptionRequest);

        this.logger.log(
          `Successfully processed event type ${event.type} for subscription ID: ${subscription.id}`,
        );

        break;
      /**
       * Invoice paid is triggered also on re-bills, which are triggered automatically by stripe.
       */
      case 'invoice.paid':
        invoice = event.data.object;
        subscription = await this.stripeClient.retrieveSubscription(
          invoice.subscription,
        );
        const updateSubscriptionRequest = new UpdateSubscriptionRequest(
          subscription.customer,
          subscription.items.data[0].price.product,
          subscription.id,
          new Date(invoice.created),
          invoice.amount_paid / 100,
          new Date(subscription.current_period_start * 1000),
          new Date(subscription.current_period_end * 1000),
          subscription.cancel_at_period_end,
          subscription.status,
        );
        await this.updateSubscriptionUseCase.execute(updateSubscriptionRequest);

        this.logger.log(
          `Successfully processed event type ${event.type} for subscription ID: ${subscription.id}`,
        );

        break;
      /**
       * There's a few cases we want to listen for this event, such as cancelled subscriptions, or subscriptions that have status changes due to failed payments.
       */
      case 'customer.subscription.updated':
        subscription = event.data.object;
        const status = subscription.status;
        if (!['active', 'cancelled', 'incomplete_expired'].includes(status))
          break;
        const updateSubscriptionRequest2 = new UpdateSubscriptionRequest(
          subscription.customer,
          subscription.items.data[0].price.product,
          subscription.id,
          null,
          null,
          new Date(subscription.current_period_start * 1000),
          new Date(subscription.current_period_end * 1000),
          subscription.cancel_at_period_end,
          subscription.status,
        );
        await this.updateSubscriptionUseCase.execute(
          updateSubscriptionRequest2,
        );

        this.logger.log(
          `Successfully processed event type ${event.type} for subscription ID: ${subscription.id}`,
        );

        break;
      default:
        this.logger.warn(`Unknown Stripe event type received: ${event.type}`);
        return;
    }
  }

  @Post('checkout')
  @Trace
  @ApiBody({ type: CheckoutDto })
  async checkout(@User() user: ApiUser, @Body() body: CheckoutDto) {
    const stripePrices = await this.stripeClient.retrievePrices();

    const stripePrice = stripePrices.data.filter(
      (price) => price.id === body.price_id,
    );
    if (stripePrice.length === 0)
      throw new ForbiddenException('Price not found.');

    const request = new NewPaymentCheckoutRequest(
      user,
      body.price_id,
      stripePrice[0].product.toString(),
      body.success_url,
      body.cancel_url,
    );
    return this.newPaymentCheckoutUseCase.execute(request);
  }

  @Post('portal')
  @Trace
  @ApiBody({ type: PortalDto })
  async portal(@User() user: ApiUser, @Body() body: PortalDto) {
    if (!user.paymentAccount)
      throw new ForbiddenException('Not a paid user yet.');
    const session = await this.stripeClient.createPortalSession(
      user.paymentAccount.id,
      body.return_url,
    );
    return {
      url: session.url,
    };
  }
}
