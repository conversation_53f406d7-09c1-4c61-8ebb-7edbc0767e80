import { NewPaymentCheckoutUseCaseModule } from '@app/core/app/use-case/api/payment/new.payment-checkout.use-case.module';
import { NewSubscriptionUseCaseModule } from '@app/core/app/use-case/api/payment/new.subscription.use-case.module';
import { UpdateSubscriptionUseCaseModule } from '@app/core/app/use-case/api/payment/update.subscription.use-case.module';
import { StripeClientModule } from '@app/core/infra/providers/stripe/stripe-client.module';
import { Module } from '@nestjs/common';

import { PaymentController } from './payment.controller';

@Module({
  imports: [
    NewPaymentCheckoutUseCaseModule,
    StripeClientModule,
    NewSubscriptionUseCaseModule,
    UpdateSubscriptionUseCaseModule,
  ],
  controllers: [PaymentController],
})
export class PaymentModule {}
