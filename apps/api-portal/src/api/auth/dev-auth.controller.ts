import { Body, Controller, Post, Res } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';

import { Public } from '../../decorator/public.decorator';
import { AuthService } from './auth.service';
import { FakeLoginDto } from './dto/login.dto';

@Controller('/auth')
@ApiTags('Auth Endpoints')
export class DevDevAuthController {
  constructor(private readonly authService: AuthService) {}

  @Post('login/fake')
  @Public()
  async loginFake(
    @Body() body: FakeLoginDto,
    @Res({ passthrough: true }) response,
  ) {
    const { access_token, refresh_token } = await this.authService.loginFake(
      body.api_key,
    );
    this.authService.setRefreshTokenCookie(response, refresh_token);
    return { access_token };
  }
}
