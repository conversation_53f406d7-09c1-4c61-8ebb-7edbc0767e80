import { NewApiUserRequest } from '@app/core/app/use-case/api/new.api-user.request';
import { NewApiUserUseCase } from '@app/core/app/use-case/api/new.api-user.use-case';
import { ApiUser } from '@app/core/domain/api/user/api-user';
import {
  API_USER_REPOSITORY,
  ApiUserRepository,
} from '@app/core/domain/api/user/api-user.repository';
import { MAILER, Mailer } from '@app/core/domain/mail/mailer';
import { GoogleClient } from '@app/core/infra/providers/google/google-client';
import { TraceClass } from '@app/core/infra/utils/tracing.decorator';
import {
  ForbiddenException,
  Inject,
  UnauthorizedException,
} from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';

import {
  JWT_ACCESS_TOKEN_EXPIRES_IN,
  JWT_ONE_TIME_LOGIN_TOKEN_SECRET,
  JWT_REFRESH_TOKEN_EXPIRES_IN,
  JWT_REFRESH_TOKEN_SECRET,
} from '../../config/env.config';
import { GenerateOneTimeLoginDto } from './dto/generate-one-time-login.dto';
import { GoogleLoginDto } from './dto/google-login.dto';
import { LoginDto } from './dto/login.dto';
import { OneTimeLoginDto } from './dto/one-time-login.dto';

export type TokensType = {
  access_token: string;
  refresh_token: string;
};

@TraceClass
export class AuthService {
  constructor(
    @Inject(API_USER_REPOSITORY)
    private readonly apiUserRepository: ApiUserRepository,
    private readonly jwtService: JwtService,
    private readonly googleClient: GoogleClient,
    private readonly newApiUserUseCase: NewApiUserUseCase,
    @Inject(MAILER)
    private readonly mailer: Mailer,
  ) {}

  async login(loginDto: LoginDto): Promise<TokensType> {
    const user = await this.validateUser(loginDto);
    // Skip captcha if the user signed up less than 1 minute ago
    if (user.createdAt.getTime() + 60000 < Date.now()) {
      const isCaptchaSolved = await this.googleClient.verifyCaptcha(
        loginDto.captcha_token,
      );
      if (!isCaptchaSolved) throw new ForbiddenException();
    }
    if (!user.isEmailConfirmed) {
      throw new ForbiddenException('Email not confirmed');
    }
    return this.generateToken(user);
  }

  async loginFake(apiKey: string): Promise<TokensType> {
    const user = await this.apiUserRepository.getByActiveApiKey(apiKey);

    if (!user) {
      throw new UnauthorizedException();
    }

    return this.generateToken(user);
  }

  async loginWithGoogle(googleLoginDto: GoogleLoginDto): Promise<TokensType> {
    const googleProfile = await this.googleClient.getGoogleProfile(
      googleLoginDto.access_token,
    );
    if (!googleProfile) {
      throw new UnauthorizedException();
    }
    let user = await this.apiUserRepository.getByGoogleId(googleProfile.sub);
    if (!user) {
      const newApiUserRequest = new NewApiUserRequest(
        googleProfile.name,
        googleProfile.email,
        null,
        googleProfile.sub,
        0, // default to Free Tier
        true,
      );
      user = await this.newApiUserUseCase.execute(newApiUserRequest);
    }
    if (!user.signedUpWithGoogle()) {
      throw new UnauthorizedException();
    }
    return this.generateToken(user);
  }

  async refresh(email: string, refreshToken: string): Promise<TokensType> {
    const user = await this.validateRefreshToken(email, refreshToken);
    return this.generateToken(user);
  }

  async validateUser(loginDto: LoginDto) {
    const user = await this.apiUserRepository.getByEmail(loginDto.email);
    if (
      user &&
      !user.signedUpWithGoogle() &&
      (await user.password?.matches(loginDto.password))
    ) {
      return user;
    }
    throw new UnauthorizedException();
  }

  protected async validateRefreshToken(email: string, refreshToken: string) {
    const user = await this.apiUserRepository.getByEmail(email);
    if (user && user.refreshToken && user.refreshToken === refreshToken) {
      return user;
    }
    throw new UnauthorizedException('Invalid refresh token');
  }

  async validateToken(token: string): Promise<ApiUser | null> {
    try {
      const decoded = this.jwtService.verify(token);
      return this.apiUserRepository.getByEmail(decoded.email);
    } catch (e) {
      return null;
    }
  }

  async generateToken(user: ApiUser): Promise<TokensType> {
    const accessToken = this.jwtService.sign(
      {
        email: user.email,
        sub: user.id.value,
      },
      {
        expiresIn: JWT_ACCESS_TOKEN_EXPIRES_IN,
      },
    );
    const refreshToken = this.jwtService.sign(
      {
        username: user.email,
        sub: user.id.value,
      },
      {
        secret: JWT_REFRESH_TOKEN_SECRET,
        expiresIn: JWT_REFRESH_TOKEN_EXPIRES_IN,
      },
    );
    user.refreshToken = refreshToken;
    await this.apiUserRepository.persist(user);
    return {
      access_token: accessToken,
      refresh_token: refreshToken,
    };
  }

  setRefreshTokenCookie(response, refreshToken) {
    response.cookie('refreshToken', refreshToken, {
      httpOnly: true,
      secure: true,
      maxAge: 1000 * 60 * 60 * 24 * 30,
    });
  }

  generateOneTimeLoginToken(user: ApiUser): string {
    return this.jwtService.sign(
      {
        email: user.email,
        sub: user.id.value,
      },
      {
        secret: JWT_ONE_TIME_LOGIN_TOKEN_SECRET,
        expiresIn: '2h',
      },
    );
  }

  async generateOneTimeLoginVerifyEmail(user: ApiUser, redirectUrl: string) {
    if (!user.email) {
      throw new Error('User has no email');
    }
    if (user.isEmailConfirmed) {
      throw new Error('Email already confirmed');
    }
    const token = this.generateOneTimeLoginToken(user);
    user.oneTimeLoginToken = token;
    await this.apiUserRepository.persist(user);
    await this.mailer.sendVerifyEmail({
      toEmail: user.email,
      name: user.name ?? 'Customer',
      link: redirectUrl + '?token=' + token,
    });
  }

  async generateOneTimeLoginForgotPassword(
    dto: GenerateOneTimeLoginDto,
    captcha = true,
  ): Promise<boolean> {
    if (captcha) {
      const isCaptchaSolved = await this.googleClient.verifyCaptcha(
        dto.captcha_token,
      );
      if (!isCaptchaSolved) {
        throw new ForbiddenException('Captcha not solved');
      }
    }
    const user = await this.apiUserRepository.getByEmail(dto.email);
    if (!user || !user.email) {
      return false;
    }
    const token = this.generateOneTimeLoginToken(user);
    user.oneTimeLoginToken = token;
    await this.apiUserRepository.persist(user);
    await this.mailer.sendForgotPasswordEmail({
      toEmail: user.email,
      name: user.name ?? 'Customer',
      link: `${dto.redirect_url}?token=${token}`,
    });
    return true;
  }

  async oneTimeLogin(dto: OneTimeLoginDto): Promise<TokensType> {
    const user = await this.validateOneTimeLoginToken(dto.token);
    if (!user) {
      throw new UnauthorizedException();
    }
    if (user.oneTimeLoginToken !== dto.token) {
      throw new UnauthorizedException();
    }
    user.oneTimeLoginToken = null;
    user.isEmailConfirmed = true;
    await this.apiUserRepository.persist(user);
    return this.generateToken(user);
  }

  async validateOneTimeLoginToken(token: string): Promise<ApiUser | null> {
    try {
      const decoded = this.jwtService.verify(token, {
        secret: JWT_ONE_TIME_LOGIN_TOKEN_SECRET,
      });
      return this.apiUserRepository.getByEmail(decoded.email);
    } catch (e) {
      return null;
    }
  }
}
