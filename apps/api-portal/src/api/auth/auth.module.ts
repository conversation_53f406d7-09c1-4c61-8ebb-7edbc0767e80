import { NewApiUserUseCaseModule } from '@app/core/app/use-case/api/new.api-user.use-case.module';
import { ENodeEnv, NODE_ENV } from '@app/core/config/env';
import { ApiUserRepositoryModule } from '@app/core/domain/api/user/api-user.repository.module';
import { MailerModule } from '@app/core/infra/mail/mailer.module';
import { GoogleClientModule } from '@app/core/infra/providers/google/google-client.module';
import { Module } from '@nestjs/common';
import { APP_GUARD } from '@nestjs/core';
import { JwtModule } from '@nestjs/jwt';

import { JWT_API_PORTAL_SECRET } from '../../config/env.config';
import { SignupController } from '../signup/signup.controller';
import { AuthController } from './auth.controller';
import { AuthGuard } from './auth.guard';
import { AuthService } from './auth.service';
import { DevDevAuthController } from './dev-auth.controller';

// include dev auth controller if in dev or test env
const devAuthControllers = [ENodeEnv.TEST, ENodeEnv.DEV].includes(NODE_ENV)
  ? [DevDevAuthController]
  : [];

@Module({
  imports: [
    ApiUserRepositoryModule,
    JwtModule.register({
      secret: JWT_API_PORTAL_SECRET,
    }),
    GoogleClientModule,
    NewApiUserUseCaseModule,
    MailerModule,
  ],
  providers: [
    AuthService,
    {
      provide: APP_GUARD,
      useClass: AuthGuard,
    },
  ],
  exports: [AuthService],
  controllers: [AuthController, SignupController, ...devAuthControllers],
})
export class AuthModule {}
