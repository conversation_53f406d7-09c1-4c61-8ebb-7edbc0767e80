import {
  CanActivate,
  ExecutionContext,
  Injectable,
  Logger,
} from '@nestjs/common';
import { Reflector } from '@nestjs/core';

import { IS_ADMIN_KEY } from '../../decorator/admin.decorator';
import { IS_PUBLIC_KEY } from '../../decorator/public.decorator';
import { AuthService } from './auth.service';

@Injectable()
export class AuthGuard implements CanActivate {
  private readonly logger = new Logger(AuthGuard.name);

  constructor(
    private readonly authService: AuthService,
    private readonly reflector: Reflector,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    if (this.reflector.get<boolean>(IS_PUBLIC_KEY, context.getHandler()))
      return true;
    const request = context.switchToHttp().getRequest();
    const authHeader = request.headers.authorization;
    if (!authHeader) {
      this.logger.verbose('No auth header found');
      return false;
    }
    const [bearer, token] = authHeader.split(' ');
    if (bearer !== 'Bearer') {
      this.logger.verbose('No bearer token found');
      return false;
    }
    const user = await this.authService.validateToken(token);
    if (!user) {
      this.logger.verbose('No user found');
      return false;
    }
    if (this.reflector.get<boolean>(IS_ADMIN_KEY, context.getHandler())) {
      if (!user.tier.isAdmin()) return false;
    }
    request.user = user;
    return true;
  }
}
