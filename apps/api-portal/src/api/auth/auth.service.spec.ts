import { NewApiUserUseCase } from '@app/core/app/use-case/api/new.api-user.use-case';
import { Tier } from '@app/core/domain/api/api-tier/tier';
import { TierId } from '@app/core/domain/api/api-tier/tier.id';
import { ApiUser } from '@app/core/domain/api/user/api-user';
import { ApiUserId } from '@app/core/domain/api/user/api-user.id';
import { API_USER_REPOSITORY } from '@app/core/domain/api/user/api-user.repository';
import { HashedPassword } from '@app/core/domain/value-object/hashed-password';
import { GoogleClient } from '@app/core/infra/providers/google/google-client';
import { UnauthorizedException } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { Test, TestingModule } from '@nestjs/testing';
import { v4 } from 'uuid';

import { AuthService } from './auth.service';

describe('AuthService', () => {
  let service: AuthService;

  const apiUserRepository = {
    persist: jest.fn(),
    getByApiKey: jest.fn(),
    getByEmail: jest.fn(),
    getByGoogleId: jest.fn(),
    getAll: jest.fn(),
    getNextId: jest.fn(),
  };

  const newApiUserUseCase = {
    execute: jest.fn(),
  };

  const googleClient = {
    getGoogleProfile: jest.fn(),
    verifyCaptcha: jest.fn(),
  };

  const jwt = {
    sign: jest.fn(),
  };

  let user: ApiUser;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [AuthService],
    })
      .useMocker((token) => {
        if (token === API_USER_REPOSITORY) return apiUserRepository;
        if (token === GoogleClient) return googleClient;
        if (token === JwtService) return jwt;
        if (token === NewApiUserUseCase) return newApiUserUseCase;
        return {};
      })
      .compile();
    service = module.get<AuthService>(AuthService);
    user = new ApiUser({
      id: new ApiUserId(v4()),
      email: '<EMAIL>',
      name: 'Test',
      active: true,
      overageWarningAlerts: true,
      _overageRequestCap: 1000,
      apiKeys: [],
      tier: new Tier({
        id: new TierId(1),
        name: 'Free',
        monthlyLimit: 1000,
        requestsPerSecond: 1000,
        overageCostPerRequest: 1000,
        paymentProductId: null,
        createdAt: new Date(),
      }),
      billingDay: 20,
      createdAt: new Date(),
      password: await HashedPassword.create('Password123'),
      refreshToken: null,
      googleId: null,
      paymentAccount: null,
      userId: null,
      oneTimeLoginToken: null,
      rateLimit: undefined,
      isEmailConfirmed: true,
      lastOverageEmailAtPercent: 0,
    });
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('login', () => {
    const loginDto = {
      email: '<EMAIL>',
      password: 'Password123',
      captcha_token: '1234',
    };
    it('it should login if valid credentials', async () => {
      apiUserRepository.getByEmail.mockResolvedValueOnce(user);
      googleClient.verifyCaptcha.mockResolvedValueOnce(true);
      const result = await service.login(loginDto);
      expect(result).toHaveProperty('access_token');
      expect(result).toHaveProperty('refresh_token');
    });

    it('it should not login if invalid password', async () => {
      loginDto.password = 'invalid';
      apiUserRepository.getByEmail.mockResolvedValueOnce(user);
      googleClient.verifyCaptcha.mockResolvedValueOnce(true);
      await expect(service.login(loginDto)).rejects.toThrow(
        UnauthorizedException,
      );
    });

    it('it should not login if is a google user', async () => {
      user.googleId = '123';
      apiUserRepository.getByEmail.mockResolvedValueOnce(user);
      googleClient.verifyCaptcha.mockResolvedValueOnce(true);
      await expect(service.login(loginDto)).rejects.toThrow(
        UnauthorizedException,
      );
    });

    it('it should not login if user does not exist', async () => {
      apiUserRepository.getByEmail.mockResolvedValueOnce(null);
      googleClient.verifyCaptcha.mockResolvedValueOnce(true);
      await expect(service.login(loginDto)).rejects.toThrow(
        UnauthorizedException,
      );
    });
  });

  describe('loginWithGoogle', () => {
    const googleLoginDto = {
      access_token: 'testAccess_token',
    };
    const googleId = 'testGoogleId';
    const googleProfile = {
      sub: googleId,
    };

    beforeEach(async () => {
      user.password = null;
      user.googleId = googleId;
    });

    it('it should login if valid credentials', async () => {
      googleClient.getGoogleProfile.mockResolvedValueOnce(googleProfile);
      apiUserRepository.getByGoogleId.mockResolvedValueOnce(user);
      const result = await service.loginWithGoogle(googleLoginDto);
      expect(result).toHaveProperty('access_token');
      expect(result).toHaveProperty('refresh_token');
    });

    it('it should not login if invalid credentials', async () => {
      googleClient.getGoogleProfile.mockResolvedValueOnce(null);
      await expect(service.loginWithGoogle(googleLoginDto)).rejects.toThrow(
        UnauthorizedException,
      );
    });

    it('it should signup if user does not exist', async () => {
      googleClient.getGoogleProfile.mockResolvedValueOnce(googleProfile);
      apiUserRepository.getByGoogleId.mockResolvedValueOnce(null);
      newApiUserUseCase.execute.mockResolvedValueOnce(user);
      const result = await service.loginWithGoogle(googleLoginDto);
      expect(result).toHaveProperty('access_token');
      expect(result).toHaveProperty('refresh_token');
    });

    it('it should not login if user is not a google user', async () => {
      googleClient.getGoogleProfile.mockResolvedValueOnce(googleProfile);
      user.googleId = null;
      apiUserRepository.getByGoogleId.mockResolvedValueOnce(user);
      await expect(service.loginWithGoogle(googleLoginDto)).rejects.toThrow(
        UnauthorizedException,
      );
    });
  });

  describe('refresh_token', () => {
    it('it should refresh token', async () => {
      user.refreshToken = 'testRefreshToken';
      apiUserRepository.getByEmail.mockResolvedValueOnce(user);
      const result = await service.refresh(user.email!, user.refreshToken);
      expect(result).toHaveProperty('access_token');
      expect(result).toHaveProperty('refresh_token');
    });

    it('it should not refresh token if invalid refresh token', async () => {
      user.refreshToken = 'testRefreshToken';
      apiUserRepository.getByEmail.mockResolvedValueOnce(user);
      await expect(
        service.refresh(user.email!, 'invalidRefreshToken'),
      ).rejects.toThrow(UnauthorizedException);
    });
  });
});
