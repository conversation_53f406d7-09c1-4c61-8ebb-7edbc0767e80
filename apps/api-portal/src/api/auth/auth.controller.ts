import { CustomApiOperation } from '@app/core/infra/utils/custom-api-operation.decorator';
import { Trace } from '@app/core/infra/utils/tracing.decorator';
import { Body, Controller, Post, Req, Res } from '@nestjs/common';
import { ApiBody, ApiTags } from '@nestjs/swagger';

import { Public } from '../../decorator/public.decorator';
import { AuthService } from './auth.service';
import { GenerateOneTimeLoginDto } from './dto/generate-one-time-login.dto';
import { GoogleLoginDto } from './dto/google-login.dto';
import { LoginDto } from './dto/login.dto';
import { OneTimeLoginDto } from './dto/one-time-login.dto';
import { RefreshDto } from './dto/refresh.dto';

@Controller('/auth')
@ApiTags('Auth Endpoints')
export class AuthController {
  constructor(private readonly authService: AuthService) {}

  @Post('login')
  @Public()
  @CustomApiOperation({
    description: `Logs in a user via email/password and returns an access token. Also sets a refresh token httpOnly cookie.`,
    summary: `Login`,
  })
  @ApiBody({ type: LoginDto })
  @Trace
  async login(
    @Body() loginDto: LoginDto,
    @Res({ passthrough: true }) response,
  ) {
    const { access_token, refresh_token } = await this.authService.login(
      loginDto,
    );
    this.authService.setRefreshTokenCookie(response, refresh_token);
    return { access_token };
  }

  @Post('login/google')
  @Public()
  @CustomApiOperation({
    description: `Logs in a user via Google access token and returns an access token. Also sets a refresh token httpOnly cookie.`,
    summary: `Login with Google`,
  })
  @ApiBody({ type: GoogleLoginDto })
  @Trace
  async loginGoogle(
    @Body() body: GoogleLoginDto,
    @Res({ passthrough: true }) response,
  ) {
    const { access_token, refresh_token } =
      await this.authService.loginWithGoogle(body);
    this.authService.setRefreshTokenCookie(response, refresh_token);
    return { access_token };
  }

  @Post('refresh')
  @Public()
  @CustomApiOperation({
    description: `Refreshes an access token and sets a new refresh token httpOnly cookie.`,
    summary: `Refresh`,
  })
  @ApiBody({ type: RefreshDto })
  @Trace
  async refresh(
    @Req() request,
    @Body() refreshDto: RefreshDto,
    @Res({ passthrough: true }) response,
  ) {
    const { access_token, refresh_token } = await this.authService.refresh(
      refreshDto.email,
      request.cookies.refreshToken,
    );
    this.authService.setRefreshTokenCookie(response, refresh_token);
    return { access_token };
  }

  @Post('logout')
  @Public()
  @CustomApiOperation({
    description: `Logs out a user by clearing the refresh token httpOnly cookie.`,
    summary: `Logout`,
  })
  @Trace
  async logout(@Res({ passthrough: true }) response) {
    response.clearCookie('refreshToken');
    return { message: 'ok' };
  }

  @Post('generate-one-time-login')
  @Public()
  @ApiBody({ type: GenerateOneTimeLoginDto })
  @Trace
  async generateOneTimeLogin(@Body() body: GenerateOneTimeLoginDto) {
    await this.authService.generateOneTimeLoginForgotPassword(body);
    return { message: 'ok' };
  }

  @Post('login/one-time')
  @Public()
  @Trace
  async oneTimeLogin(
    @Body() body: OneTimeLoginDto,
    @Res({ passthrough: true }) response,
    @Req() req,
  ) {
    const { access_token, refresh_token } = await this.authService.oneTimeLogin(
      body,
    );
    this.authService.setRefreshTokenCookie(response, refresh_token);
    return { access_token };
  }
}
