import {
  PAYMENT_SUBSCRIPTION_REPOSITORY,
  PaymentSubscriptionRepository,
} from '@app/core/domain/api/payment/subscription/payment-subscription.repository';
import { ApiKeyId } from '@app/core/domain/api/user/api-key.id';
import { ApiUser } from '@app/core/domain/api/user/api-user';
import {
  API_USER_REPOSITORY,
  ApiUserRepository,
} from '@app/core/domain/api/user/api-user.repository';
import { HashedPassword } from '@app/core/domain/value-object/hashed-password';
import { Trace } from '@app/core/infra/utils/tracing.decorator';
import {
  Body,
  Controller,
  Get,
  Inject,
  NotFoundException,
  Param,
  Post,
  Put,
} from '@nestjs/common';
import { ApiBody, ApiTags } from '@nestjs/swagger';

import { User } from '../../decorator/user.decorator';
import { UpdateUserDto } from '../auth/dto/update-user.dto';
import { CreateApiKeyDto } from './dto/create.api-key.dto';
import { UpdateApiKeyDto } from './dto/update.api-key.dto';
import { ApiKeyResponse } from './response/api-key.response';
import { SubscriptionResponse } from './response/subscription.response';
import { UserResponse } from './response/user.response';

@Controller('users')
@ApiTags('User Endpoints')
export class UserController {
  constructor(
    @Inject(API_USER_REPOSITORY)
    private readonly apiUserRepository: ApiUserRepository,
    @Inject(PAYMENT_SUBSCRIPTION_REPOSITORY)
    private readonly paymentSubscriptionRepository: PaymentSubscriptionRepository,
  ) {}

  @Get()
  @Trace
  async get(@User() apiUser: ApiUser) {
    return new UserResponse(apiUser);
  }

  @Get('subscriptions/active')
  @Trace
  async getSubscriptions(@User() apiUser: ApiUser) {
    const paymentSubscription =
      await this.paymentSubscriptionRepository.getActiveByApiUserId(apiUser.id);
    if (!paymentSubscription) return null;
    return new SubscriptionResponse(paymentSubscription);
  }

  @Post('api-keys')
  @Trace
  @ApiBody({ type: CreateApiKeyDto })
  async createApiKey(
    @Body() request: CreateApiKeyDto,
    @User() apiUser: ApiUser,
  ) {
    const apiKey = apiUser.newApiKey(request.name);
    await this.apiUserRepository.persist(apiUser);
    return new ApiKeyResponse(apiKey);
  }

  @Put('api-keys/:id')
  @Trace
  @ApiBody({ type: UpdateApiKeyDto })
  async updateApiKey(
    @Body() request: UpdateApiKeyDto,
    @User() apiUser: ApiUser,
    @Param('id') id: string,
  ) {
    const apiKeyId = new ApiKeyId(id);
    const apiKey = apiUser.updateApiKey(apiKeyId, request.name, request.active);
    if (!apiKey) throw new NotFoundException('API Key not found');
    await this.apiUserRepository.persist(apiUser);
    return new ApiKeyResponse(apiKey);
  }

  @Put('/')
  @Trace
  @ApiBody({ type: UpdateUserDto })
  async update(@User() user: ApiUser, @Body() request: UpdateUserDto) {
    if (request.name) user.name = request.name;
    if (request.email && !user.signedUpWithGoogle()) user.email = request.email;
    if (request.password)
      user.password = await HashedPassword.create(request.password);
    await this.apiUserRepository.persist(user);
    return new UserResponse(user);
  }
}
