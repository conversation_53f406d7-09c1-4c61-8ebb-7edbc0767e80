import { PaymentSubscriptionRepositoryModule } from '@app/core/domain/api/payment/subscription/payment-subscription.repository.module';
import { ApiUserRepositoryModule } from '@app/core/domain/api/user/api-user.repository.module';
import { Module } from '@nestjs/common';

import { UserController } from './user.controller';

@Module({
  imports: [ApiUserRepositoryModule, PaymentSubscriptionRepositoryModule],
  controllers: [UserController],
})
export class UserModule {}
