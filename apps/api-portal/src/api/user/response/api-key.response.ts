import { ApiKey } from '@app/core/domain/api/user/api-key';

export class ApiKeyResponse {
  id: string;
  key: string;
  name: string;
  active: boolean;
  monthly_usage: number;
  created_at: Date;

  constructor(apiKey: ApiKey) {
    this.id = apiKey.id.value;
    this.key = apiKey.key;
    this.name = apiKey.name;
    this.active = apiKey.active;
    this.monthly_usage = apiKey.monthlyUsage;
    this.created_at = apiKey.createdAt;
  }
}
