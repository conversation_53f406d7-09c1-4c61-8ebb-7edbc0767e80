import { PaymentSubscription } from '@app/core/domain/api/payment/subscription/payment-subscription';

export class SubscriptionResponse {
  tier_id: number;
  starts_at: Date;
  ends_at: Date;
  is_cancelled: boolean;
  status: string;

  constructor(paymentSubscription: PaymentSubscription) {
    this.tier_id = paymentSubscription.tierId.value;
    this.starts_at = paymentSubscription.startsAt;
    this.ends_at = paymentSubscription.endsAt;
    this.is_cancelled = paymentSubscription.isCancelled;
    this.status = paymentSubscription.status;
  }
}
