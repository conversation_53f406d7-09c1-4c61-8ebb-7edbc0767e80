import { NewApiUserUseCaseModule } from '@app/core/app/use-case/api/new.api-user.use-case.module';
import { MailerModule } from '@app/core/infra/mail/mailer.module';
import { GoogleClientModule } from '@app/core/infra/providers/google/google-client.module';
import { Module } from '@nestjs/common';

import { AuthModule } from '../auth/auth.module';
import { SignupController } from './signup.controller';

@Module({
  imports: [
    NewApiUserUseCaseModule,
    GoogleClientModule,
    AuthModule,
    MailerModule,
  ],
  providers: [MailerModule],
  controllers: [SignupController],
})
export class SignupModule {}
