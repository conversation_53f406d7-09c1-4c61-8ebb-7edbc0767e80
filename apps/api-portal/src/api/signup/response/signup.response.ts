import { ApiUser } from '@app/core/domain/api/user/api-user';

type ApiKeyType = {
  id: string;
  name: string;
  key: string;
  monthly_usage: number;
  active: boolean;
  created_at: Date;
};

type TierType = {
  id: number;
  name: string;
  monthly_limit: number;
  requests_per_second: number;
  overage_cost_per_request: number;
};

export class SignupResponse {
  email: string;
  name: string;
  api_keys: ApiKeyType[];
  overage_warning_alerts: boolean;
  tier: TierType;
  billing_date: Date;
  total_monthly_usage: number;
  overage_request_cap: number;
  created_at: Date;
  payment_account_id: string | null;
  is_email_confirmed: boolean;

  constructor(apiUser: ApiUser) {
    this.email = apiUser.email ?? '';
    this.name = apiUser.name ?? '';
    this.api_keys = apiUser.getApiKeys().map((apiKey) => ({
      id: apiKey.id.value,
      key: apiKey.key,
      name: apiKey.name,
      monthly_usage: apiKey.monthlyUsage,
      active: apiKey.active,
      created_at: apiKey.createdAt,
    }));
    this.overage_warning_alerts = apiUser.overageWarningAlerts;
    this.tier = {
      id: apiUser.tier.id.value,
      name: apiUser.tier.name,
      monthly_limit: apiUser.tier.monthlyLimit,
      requests_per_second: apiUser.tier.requestsPerSecond,
      overage_cost_per_request: apiUser.tier.overageCostPerRequest,
    };
    this.billing_date = apiUser.nextBillingDate();
    this.total_monthly_usage = apiUser.monthlyUsage();
    this.overage_request_cap = apiUser.overageRequestCap;
    this.created_at = apiUser.createdAt;
    this.payment_account_id = apiUser.paymentAccount?.id ?? null;
    this.is_email_confirmed = apiUser.isEmailConfirmed;
  }
}
