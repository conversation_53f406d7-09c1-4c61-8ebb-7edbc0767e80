import { ApiProperty } from '@nestjs/swagger';
import { IsEmail, IsNotEmpty, IsString, IsUrl } from 'class-validator';

export class SignupVerifyEmailDto {
  @IsNotEmpty()
  @IsString()
  @ApiProperty()
  readonly name: string;

  @IsNotEmpty()
  @IsEmail()
  @ApiProperty()
  readonly email: string;

  @IsNotEmpty()
  @IsString()
  @ApiProperty()
  readonly password: string;

  @ApiProperty()
  @IsNotEmpty()
  readonly captcha_token: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsUrl()
  readonly redirect_url: string;
}
