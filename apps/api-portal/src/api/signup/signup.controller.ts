import { NewApiUserRequest } from '@app/core/app/use-case/api/new.api-user.request';
import { NewApiUserUseCase } from '@app/core/app/use-case/api/new.api-user.use-case';
import { GoogleClient } from '@app/core/infra/providers/google/google-client';
import { Trace } from '@app/core/infra/utils/tracing.decorator';
import { Body, Controller, ForbiddenException, Post } from '@nestjs/common';
import { ApiBody, ApiOperation, ApiTags } from '@nestjs/swagger';

import { Public } from '../../decorator/public.decorator';
import { AuthService } from '../auth/auth.service';
import { SignupEmailDto } from './dto/signup.email.dto';
import { SignupGoogleDto } from './dto/signup.google.dto';
import { SignupVerifyEmailDto } from './dto/signup.verify-email.dto';
import { SignupResponse } from './response/signup.response';

@Controller('signup')
@ApiTags('Signup Endpoints')
export class SignupController {
  constructor(
    private readonly newApiUserUseCase: NewApiUserUseCase,
    private readonly googleClient: GoogleClient,
    private readonly authService: AuthService,
  ) {}

  @Post()
  @Public()
  @ApiOperation({
    deprecated: true,
    summary: 'Deprecated: Use /signup/verify-email instead',
  })
  @Trace
  @ApiBody({ type: SignupEmailDto })
  async create(@Body() request: SignupEmailDto): Promise<SignupResponse> {
    const isCaptchaSolved = await this.googleClient.verifyCaptcha(
      request.captcha_token,
    );
    if (!isCaptchaSolved) throw new ForbiddenException();
    const newApiUserRequest = new NewApiUserRequest(
      request.name,
      request.email,
      request.password,
      null,
      0, // default to Free Tier
      true,
    );
    const user = await this.newApiUserUseCase.execute(newApiUserRequest);

    return new SignupResponse(user);
  }

  @Post('/verify-email')
  @Public()
  @Trace
  @ApiBody({ type: SignupVerifyEmailDto })
  async createVerifyEmail(
    @Body() request: SignupVerifyEmailDto,
  ): Promise<SignupResponse> {
    const isCaptchaSolved = await this.googleClient.verifyCaptcha(
      request.captcha_token,
    );
    if (!isCaptchaSolved) throw new ForbiddenException();
    const newApiUserRequest = new NewApiUserRequest(
      request.name,
      request.email,
      request.password,
      null,
      0, // default to Free Tier
      false,
    );
    const user = await this.newApiUserUseCase.execute(newApiUserRequest);
    await this.authService.generateOneTimeLoginVerifyEmail(
      user,
      request.redirect_url,
    );
    return new SignupResponse(user);
  }

  @Post('google')
  @Public()
  @Trace
  @ApiBody({ type: SignupGoogleDto })
  async createGoogle(
    @Body() request: SignupGoogleDto,
  ): Promise<SignupResponse> {
    const googleProfile = await this.googleClient.getGoogleProfile(
      request.access_token,
    );
    const newApiUserRequest = new NewApiUserRequest(
      googleProfile.name,
      googleProfile.email,
      null,
      googleProfile.sub,
      0, // default to Free Tier
      true,
    );
    const user = await this.newApiUserUseCase.execute(newApiUserRequest);
    return new SignupResponse(user);
  }
}
