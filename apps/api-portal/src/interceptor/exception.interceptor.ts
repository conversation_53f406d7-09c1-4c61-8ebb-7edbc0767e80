import { DomainException } from '@app/core/domain/domain.exception';
import {
  ArgumentsHost,
  Catch,
  ExceptionFilter,
  HttpException,
  HttpStatus,
  Logger,
} from '@nestjs/common';

@Catch()
export class GlobalExceptionsFilter implements ExceptionFilter {
  private readonly logger = new Logger(GlobalExceptionsFilter.name);

  catch(exception: unknown, host: ArgumentsHost): void {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse();

    const status = false;
    let statusCode, resObj, apiResponseMessage, logMessage;
    if (exception instanceof HttpException) {
      statusCode = exception.getStatus();
      resObj = exception.getResponse();
      apiResponseMessage =
        statusCode === 429 ? 'Too many request' : resObj.message;
      logMessage = apiResponseMessage;
    } else if (exception instanceof DomainException) {
      statusCode = 400;
      apiResponseMessage = exception.message;
      logMessage = apiResponseMessage;
    } else {
      statusCode = HttpStatus.INTERNAL_SERVER_ERROR;
      apiResponseMessage = 'Sorry, we are unable to process your request.';
      logMessage =
        exception instanceof Error ? exception.message : apiResponseMessage;
    }
    this.logger.error(logMessage, (exception as any).stack);
    return response
      .status(statusCode)
      .json({ status, message: apiResponseMessage });
  }
}
