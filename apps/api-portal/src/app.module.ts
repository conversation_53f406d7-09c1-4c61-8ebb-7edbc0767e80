import { AppLoggerModule } from '@app/app-logger/logger.module';
import { UnitOfWorkModule } from '@app/core/app/unit-of-work/unit-of-work.module';
import { MetricsLoggerModule } from '@app/core/domain/metrics-logger/metrics-logger.module';
import { DatadogClientModule } from '@app/datadog-client';
import { Module } from '@nestjs/common';
import { APP_FILTER } from '@nestjs/core';
import { SequelizeModule } from '@nestjs/sequelize';

import { sequelizeOptions } from '../../rest-api/src/config';
import { AdminAclModule } from './api/admin/acl/admin.acl.module';
import { AdminSubscriptionModule } from './api/admin/subscription/admin.subscription.module';
import { AdminTierModule } from './api/admin/tier/admin.tier.module';
import { AdminUserModule } from './api/admin/user/admin.user.module';
import { AuthModule } from './api/auth/auth.module';
import { HealthController } from './api/health/health.controller';
import { PaymentModule } from './api/payment/payment.module';
import { SignupModule } from './api/signup/signup.module';
import { TiersModule } from './api/tiers/tiers.module';
import { UserModule } from './api/user/user.module';
import { AppService } from './app.service';
import { GlobalExceptionsFilter } from './interceptor/exception.interceptor';

@Module({
  imports: [
    SequelizeModule.forRoot(sequelizeOptions),
    AuthModule,
    SignupModule,
    PaymentModule,
    UserModule,
    AdminUserModule,
    AdminTierModule,
    AdminAclModule,
    AdminSubscriptionModule,
    TiersModule,
    DatadogClientModule,
    AppLoggerModule,
    MetricsLoggerModule,
    UnitOfWorkModule,
  ],
  providers: [
    AppService,
    {
      provide: APP_FILTER,
      useClass: GlobalExceptionsFilter,
    },
  ],
  controllers: [HealthController],
})
export class AppModule {}
