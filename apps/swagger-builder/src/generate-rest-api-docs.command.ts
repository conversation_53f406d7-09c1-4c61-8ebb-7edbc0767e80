import { AppLogger } from '@app/app-logger';
import { writeFileSync } from 'fs';
import { Command, CommandRunner, Option } from 'nest-commander';

import { app } from '../../rest-api/src/app';
import { docs } from '../../rest-api/src/docs';

interface GenerateRestApiDocsCommandOptions {
  outputFile: string;
}

@Command({
  name: 'generate:rest-api-docs',
  description:
    'Generates the swagger docs for the REST API, & outputs them to a file',
})
export class GenerateRestApiDocsCommand extends CommandRunner {
  private readonly logger = new AppLogger(GenerateRestApiDocsCommand.name);

  async run(
    passedParams: string[],
    options: GenerateRestApiDocsCommandOptions,
  ): Promise<void> {
    this.logger.log('Building Swagger Docs');

    const outputFile = options.outputFile;
    const restApi = await app();
    const document = docs(restApi);
    this.logger.log(`Writing docs to ${outputFile}`);
    writeFileSync(outputFile, JSON.stringify(document, null, 2));
    restApi.close();
    return Promise.resolve();
  }

  @Option({
    flags: '-o, --output-file [string]',
    name: 'output-file',
    description: 'The file to output the swagger docs to',
    required: true,
  })
  parseOutputFile(outputFile: string): string {
    return outputFile;
  }
}
