import {
  Allora<PERSON>hainConfig,
  EAlloraChainId,
} from '@app/core/domain/allora/chain/allora-chain-config';
import { SlackBot } from '@app/core/infra/slack/slackbot';
import { CRON_ALERT_SLACK_BOT } from '@app/core/infra/slack/slackbot.module';
import { Controller, Inject, Logger } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { StatsD } from 'hot-shots';

import { AlloraDumpTimeseriesUseCase } from './use-case/allora-dump-timeseries.use-case';
import { AlloraGroundTruthUpdaterUseCase } from './use-case/allora-ground-truth-updater.use-case';
import { AlloraMarketDataBucketBackfillerUseCase } from './use-case/allora-market-data-bucket-backfiller.use-case';
import { AlloraMarketDataBucketCreatorUseCase } from './use-case/allora-market-data-bucket-creator.use-case';
import { AlloraMarketDataBucketUploaderUseCase } from './use-case/allora-market-data-bucket-uploader.use-case';
import { AlloraMarketDataUpdaterUseCase } from './use-case/allora-market-data-updater.use-case';
import { AlloraModelDataUpdaterUseCase } from './use-case/allora-model-data-updater.use-case';
import { AlloraStudioDataUpdaterUseCase } from './use-case/allora-studio-data-updater.use-case';
import { AlloraTopicMetadataUpdaterUseCase } from './use-case/allora-topic-metadata-updater.use-case';
import { ApiBillingUseCase } from './use-case/api-billing.use-case';
// DISABLED: Legacy Robonet cron job imports - can be re-enabled if needed
// import { RoboNetDailyPointsUseCase } from './use-case/robonet-daily-points.use-case';
// import { RoboNetVaultReporterUseCase } from './use-case/robonet-vault-reporter.use-case';
// import { RoboNetVaultStatsUseCase } from './use-case/robonet-vault-stats.use-case';
import { UpdateExchangeRatesUseCase } from './use-case/update-exchange-rates.use-case';

@Controller()
export class CronController {
  private readonly logger = new Logger(CronController.name);
  private readonly runningCrons = new Set<string>();

  constructor(
    private readonly updateExchangeRatesUseCase: UpdateExchangeRatesUseCase,
    private readonly apiBillingUseCase: ApiBillingUseCase,
    // DISABLED: Legacy Robonet cron job dependencies - can be re-enabled if needed
    // private readonly robonetDailyPointsUseCase: RoboNetDailyPointsUseCase,
    // private readonly roboNetVaultStatsUseCase: RoboNetVaultStatsUseCase,
    // private readonly roboNetVaultReporterUseCase: RoboNetVaultReporterUseCase,
    private readonly dataDogClient: StatsD,
    private readonly alloraTopicDumpTimeseriesUseCase: AlloraDumpTimeseriesUseCase,
    private readonly alloraTopicMetadataUpdaterUseCase: AlloraTopicMetadataUpdaterUseCase,
    private readonly alloraMarketDataUpdaterUseCase: AlloraMarketDataUpdaterUseCase,
    private readonly alloraMarketDataBucketUploaderUseCase: AlloraMarketDataBucketUploaderUseCase,
    private readonly alloraMarketDataBucketCreatorUseCase: AlloraMarketDataBucketCreatorUseCase,
    private readonly alloraMarketDataBucketBackfillerUseCase: AlloraMarketDataBucketBackfillerUseCase,
    private readonly alloraStudioDataUpdaterUseCase: AlloraStudioDataUpdaterUseCase,
    private readonly alloraModelDataUpdaterUseCase: AlloraModelDataUpdaterUseCase,
    private readonly alloraGroundTruthUpdaterUseCase: AlloraGroundTruthUpdaterUseCase,

    @Inject(CRON_ALERT_SLACK_BOT)
    private readonly slackBot: SlackBot,
  ) {}

  // runs every minute
  @Cron(CronExpression.EVERY_5_MINUTES)
  async updateAlloraGroundTruth() {
    const executeFn = async (chainId: EAlloraChainId) => {
      try {
        await this.alloraGroundTruthUpdaterUseCase.execute(
          new AlloraChainConfig(chainId),
        );
      } catch (error) {
        this.logger.error(
          `Error updating ground truth for chain ${chainId}: ${error}`,
        );
      }
    };

    // trigger crons for each chain
    // run crons in parallel
    await Promise.all(
      Object.values(EAlloraChainId).map(async (chainId) => {
        return this.runCron(
          `${this.updateAlloraGroundTruth.name}_${chainId}`,
          async () => await executeFn(chainId),
        );
      }),
    );
  }

  // runs every 2 minutes
  @Cron('1 */2 * * * *')
  async updateAlloraModelInferences() {
    await this.runCron(
      this.updateAlloraModelInferences.name,
      async () => await this.alloraModelDataUpdaterUseCase.execute(),
    );
  }

  // runs daily at midnight
  @Cron('0 0 * * *')
  async updateAlloraStudioData() {
    await this.runCron(
      this.updateAlloraStudioData.name,
      async () => await this.alloraStudioDataUpdaterUseCase.execute(),
    );
  }

  @Cron(CronExpression.EVERY_30_SECONDS)
  async updateAlloraMarketData() {
    const now = new Date();
    // the beginning of the current minute
    const startTime = new Date(
      now.getFullYear(),
      now.getMonth(),
      now.getDate(),
      now.getHours(),
      now.getMinutes(),
      0,
    );
    // the start of the next minute
    const endTime = new Date(startTime);
    endTime.setMinutes(endTime.getMinutes() + 1);
    await this.runCron(
      this.updateAlloraMarketData.name,
      async () =>
        await this.alloraMarketDataUpdaterUseCase.execute(startTime, endTime),
    );
  }

  @Cron(CronExpression.EVERY_30_MINUTES)
  async uploadAlloraMarketDataBuckets() {
    await this.runCron(
      this.uploadAlloraMarketDataBuckets.name,
      async () => await this.alloraMarketDataBucketUploaderUseCase.execute(),
    );
  }

  @Cron(CronExpression.EVERY_DAY_AT_1AM)
  async createAlloraMarketDataBuckets() {
    await this.runCron(
      this.createAlloraMarketDataBuckets.name,
      async () => await this.alloraMarketDataBucketCreatorUseCase.execute(),
    );
  }

  @Cron(CronExpression.EVERY_30_MINUTES)
  async backfillAlloraMarketDataBuckets() {
    await this.runCron(
      this.backfillAlloraMarketDataBuckets.name,
      async () => await this.alloraMarketDataBucketBackfillerUseCase.execute(),
    );
  }

  // runs hourly
  @Cron('0 * * * *')
  async updateAlloraTopicMetadata() {
    await this.runCron(
      this.updateAlloraTopicMetadata.name,
      async () => await this.alloraTopicMetadataUpdaterUseCase.execute(),
    );
  }

  // runs daily
  @Cron('0 0 * * *')
  async dumpAlloraTopicTimeseries() {
    await this.runCron(
      this.dumpAlloraTopicTimeseries.name,
      async () => await this.alloraTopicDumpTimeseriesUseCase.execute(),
    );
  }

  // runs hourly
  @Cron('0 20 * * * *')
  async updateExchangeRates() {
    await this.runCron(
      this.updateExchangeRates.name,
      async () => await this.updateExchangeRatesUseCase.run(),
    );
  }

  // runs daily
  @Cron('10 0 * * *')
  async apiBilling() {
    await this.runCron(
      this.apiBilling.name,
      async () => await this.apiBillingUseCase.execute(),
    );
  }

  // DISABLED: Legacy Robonet cron jobs - can be re-enabled if needed
  // runs once daily at midnight
  // @Cron('0 0 * * *')
  // async robonetDailyPoints() {
  //   await this.runCron(this.robonetDailyPoints.name, async () => {
  //     await this.robonetDailyPointsUseCase.execute();
  //   });
  // }

  // runs hourly
  // @Cron('0 0-23/1 * * *')
  // async updateVaultStats() {
  //   await this.runCron(
  //     this.updateVaultStats.name,
  //     async () => await this.roboNetVaultStatsUseCase.execute(),
  //   );
  // }

  // runs hourly
  // @Cron('0 40 * * * *')
  // async vaultReporter() {
  //   await this.runCron(
  //     this.vaultReporter.name,
  //     async () => await this.roboNetVaultReporterUseCase.execute(),
  //   );
  // }

  MAX_TIMEOUT = 15 * 60 * 1000; // 15 minutes, in milliseconds

  protected async runCron(name: string, useCase: () => Promise<void>) {
    // Prevent overlapping executions
    if (this.runningCrons.has(name)) {
      this.logger.warn(`Cron ${name} is already running, skipping execution`);
      return;
    } else {
      this.runningCrons.add(name);
      this.logger.log(name, 'start cron');
      const start = new Date().getTime();
      let isSuccess = true;
      let timeout = false;

      // Create a promise that rejects after some minutes, sends Slack and DataDog metric if timeout
      const timeoutPromise = new Promise((resolve, _) => {
        setTimeout(async () => {
          timeout = true;
          resolve('Timeout!');
        }, this.MAX_TIMEOUT);
      });

      // Race between the useCase and the timeout
      const promises = [useCase(), timeoutPromise];
      try {
        await Promise.race(promises);
      } catch (error) {
        this.logger.error(error);
        isSuccess = false;
      }

      if (timeout) {
        const msg = `Cron job ${name} timed out after ${
          this.MAX_TIMEOUT / 1000
        } seconds`;
        this.logger.error(msg);
        await this.triggerSlackNotification(msg);
        this.incrementTimeoutMetric(name);
      }

      this.triggerHistogramMetric(name, start, isSuccess);
      this.logger.log(name, 'end cron');

      // Always remove from running set, even if there was an error or timeout
      this.runningCrons.delete(name);
    }
  }

  protected async triggerSlackNotification(msg: string): Promise<void> {
    return this.slackBot.sendMessage(msg);
  }

  protected incrementTimeoutMetric(name: string) {
    this.dataDogClient.increment(`xyz.upshot.backend.cron.timetout`, [
      `cron:${name}`,
    ]);
  }

  protected triggerHistogramMetric(
    name: string,
    start: number,
    success: boolean,
  ) {
    const end = new Date().getTime();
    const duration = end - start;
    this.logger.log(`[${name}] Ended in ${duration} ms`);
    const status = success ? 'ok' : 'ko';
    this.dataDogClient.histogram(`xyz.upshot.backend.cron`, duration, [
      `cron:${name}`,
      `status:${status}`,
    ]);
  }
}
