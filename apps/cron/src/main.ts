import { ENodeEnv, NODE_ENV } from '@app/core/config/env';
import { NestFactory } from '@nestjs/core';
import { config } from 'dotenv';
import { Logger } from 'nestjs-pino';

// import { CronController } from './cron.controller';
import { CronModule } from './cron.module';

config();

async function bootstrap() {
  const app = await NestFactory.createApplicationContext(CronModule, {
    bufferLogs: ![ENodeEnv.TEST, ENodeEnv.DEV].includes(NODE_ENV),
  });
  app.useLogger(app.get(Logger));
  await app.init();

  // // For live testing of jobs in development:
  // const cronController = app.get(CronController);
  // await cronController.updateAlloraModelInferences();
}

bootstrap();
