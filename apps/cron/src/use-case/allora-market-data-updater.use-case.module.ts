import { MARKET_DATA_COMMAND_REPOSITORY } from '@app/core/domain/allora/market-data/market-data.command.repository';
import { MARKET_DATA_QUERY_REPOSITORY } from '@app/core/domain/allora/market-data/market-data.query.repository';
import { MARKET_DATA_HISTORY_COMMAND_REPOSITORY } from '@app/core/domain/allora/market-data/market-data-history.command.repository';
import { ContinuationTokenRepositoryModule } from '@app/core/domain/continuation-token/continuation-token.repository.module';
import { SequelizeMarketDataCommandRepository } from '@app/core/infra/repository/allora/market-data/command/sequelize.market-data.command.repository';
import { SequelizeMarketDataHistoryCommandRepository } from '@app/core/infra/repository/allora/market-data/command/sequelize.market-data-history.command.repository';
import { SequelizeMarketDataQueryRepository } from '@app/core/infra/repository/allora/market-data/query/sequelize.market-data.query.repository';
import { Module } from '@nestjs/common';

import { AlloraMarketDataUpdaterUseCase } from './allora-market-data-updater.use-case';

@Module({
  providers: [
    AlloraMarketDataUpdaterUseCase,
    {
      useClass: SequelizeMarketDataCommandRepository,
      provide: MARKET_DATA_COMMAND_REPOSITORY,
    },
    {
      useClass: SequelizeMarketDataQueryRepository,
      provide: MARKET_DATA_QUERY_REPOSITORY,
    },
    {
      useClass: SequelizeMarketDataHistoryCommandRepository,
      provide: MARKET_DATA_HISTORY_COMMAND_REPOSITORY,
    },
  ],
  imports: [ContinuationTokenRepositoryModule],
  exports: [AlloraMarketDataUpdaterUseCase],
})
export class AlloraMarketDataUpdaterUseCaseModule {}
