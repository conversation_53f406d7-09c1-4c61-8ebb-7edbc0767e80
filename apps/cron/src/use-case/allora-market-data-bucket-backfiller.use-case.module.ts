import { MARKET_DATA_PROVIDER_MAIN_REPOSITORY } from '@app/core/domain/allora/market-data/marker-data.provider.repository';
import { MARKET_DATA_BUCKET_COMMAND_REPOSITORY } from '@app/core/domain/allora/market-data/market-data-bucket.command.repository';
import { MARKET_DATA_BUCKET_QUERY_REPOSITORY } from '@app/core/domain/allora/market-data/market-data-bucket.query.repository';
import { MARKET_DATA_HISTORY_COMMAND_REPOSITORY } from '@app/core/domain/allora/market-data/market-data-history.command.repository';
import { MARKET_DATA_HISTORY_QUERY_REPOSITORY } from '@app/core/domain/allora/market-data/market-data-history.query.repository';
import { SequelizeMarketDataBucketCommandRepository } from '@app/core/infra/repository/allora/market-data/command/sequelize.market-data-bucket.command.repository';
import { SequelizeMarketDataHistoryCommandRepository } from '@app/core/infra/repository/allora/market-data/command/sequelize.market-data-history.command.repository';
import { TiingoMarketDataProviderRepository } from '@app/core/infra/repository/allora/market-data/providers/tiingo.market-data.provider.repository';
import { SequelizeMarketDataBucketQueryRepository } from '@app/core/infra/repository/allora/market-data/query/sequelize.market-data-bucket.query.repository';
import { SequelizeMarketDataHistoryQueryRepository } from '@app/core/infra/repository/allora/market-data/query/sequelize.market-data-history.query.repository';
import { Module } from '@nestjs/common';

import { AlloraMarketDataBucketBackfillerUseCase } from './allora-market-data-bucket-backfiller.use-case';

@Module({
  providers: [
    AlloraMarketDataBucketBackfillerUseCase,
    {
      useClass: SequelizeMarketDataBucketCommandRepository,
      provide: MARKET_DATA_BUCKET_COMMAND_REPOSITORY,
    },
    {
      useClass: SequelizeMarketDataBucketQueryRepository,
      provide: MARKET_DATA_BUCKET_QUERY_REPOSITORY,
    },
    {
      useClass: SequelizeMarketDataHistoryCommandRepository,
      provide: MARKET_DATA_HISTORY_COMMAND_REPOSITORY,
    },
    {
      useClass: SequelizeMarketDataHistoryQueryRepository,
      provide: MARKET_DATA_HISTORY_QUERY_REPOSITORY,
    },
    {
      useClass: TiingoMarketDataProviderRepository,
      provide: MARKET_DATA_PROVIDER_MAIN_REPOSITORY,
    },
  ],
  exports: [AlloraMarketDataBucketBackfillerUseCase],
})
export class AlloraMarketDataBucketBackfillerUseCaseModule {}
