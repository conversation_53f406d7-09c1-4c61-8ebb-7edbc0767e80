import { ApiInvoiceRepositoryModule } from '@app/core/domain/api/invoice/api-invoice.repository.module';
import { RequestByEndpointRepositoryModule } from '@app/core/domain/api/request-by-endpoint/request-by-endpoint.repository.module';
import { ApiUserRepositoryModule } from '@app/core/domain/api/user/api-user.repository.module';
import { Module } from '@nestjs/common';

import { ApiBillingUseCase } from './api-billing.use-case';

@Module({
  imports: [
    ApiUserRepositoryModule,
    ApiInvoiceRepositoryModule,
    RequestByEndpointRepositoryModule,
  ],
  providers: [ApiBillingUseCase],
  exports: [ApiBillingUseCase],
})
export class ApiBillingUseCaseModule {}
