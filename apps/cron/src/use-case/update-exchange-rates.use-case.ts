import {
  EXCHANGE_RATE_REPOSITORY,
  ExchangeRateRepository,
} from '@app/core/domain/exchange-rate/exchange-rate.repository';
import { Inject } from '@nestjs/common';
import { ClsService } from 'nestjs-cls';

export class UpdateExchangeRatesUseCase {
  constructor(
    @Inject(EXCHANGE_RATE_REPOSITORY)
    private readonly exchangeRateRepository: ExchangeRateRepository,
    private readonly cls: ClsService,
  ) {}

  async run() {
    this.exchangeRateRepository.updateAll();
  }
}
