import { SequelizeVaultCommandRepository } from '@app/core/infra/repository/robonet/vault/command/sequelize.vault.command.repository';
import { SequelizeVaultReportQueryRepository } from '@app/core/infra/repository/robonet/vault/query/sequilize.vault-report.query.repository';
import { Test, TestingModule } from '@nestjs/testing';

import { RoboNetVaultStatsUseCase } from './robonet-vault-stats.use-case';

describe('RoboNetVaultStatsUseCase', () => {
  let service: RoboNetVaultStatsUseCase;

  const vaultReportQueryRepository = {
    getVaultReportSummary: jest.fn(),
  };

  const sequelizeVaultCommandRepository = {
    createVaultAprStats: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [RoboNetVaultStatsUseCase],
    })
      .useMocker((token) => {
        if (token === SequelizeVaultReportQueryRepository)
          return vaultReportQueryRepository;
        if (token === SequelizeVaultCommandRepository)
          return sequelizeVaultCommandRepository;
        return vaultReportQueryRepository;
        return {};
      })
      .compile();
    service = module.get<RoboNetVaultStatsUseCase>(RoboNetVaultStatsUseCase);
    sequelizeVaultCommandRepository.createVaultAprStats.mockResolvedValue({});
  });

  afterEach(async () => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('Create vault statistics', () => {
    it('should correctly annualize APR from 365 days', () => {
      const latestPrice = 1.1;
      const startingPrice = 1;
      const days = 365;
      const result = service.annualizeAPR(latestPrice, startingPrice, days);
      expect(result).toEqual(10);
    });

    it('should correctly annualize APR from 1 day', () => {
      const latestPrice = 1.1;
      const startingPrice = 1;
      const days = 1;
      const result = service.annualizeAPR(latestPrice, startingPrice, days);
      expect(result).toEqual(3650);
    });

    it('should calculate APRs', () => {
      const result = service.calculateVaultAprs([
        {
          vaultAddress: '0x29939b84c28aa3c8f43262b91a9ee42a8e1e7dbf',
          tokenDecimals: 18,
          latestPricePerShareWei: '110000000000000000',
          oneDayPricePerShareWei: '100000000000000000',
          sevenDayPricePerShareWei: '100000000000000000',
          thirtyDayPricePerShareWei: '90000000000000000',
          oldestPricePerShareWei: '110000000000000000',
          inceptionTimestamp: '1630000000',
        },
      ]);
      expect(result[0].vaultAddress).toEqual(
        '0x29939b84c28aa3c8f43262b91a9ee42a8e1e7dbf',
      );
      expect(result[0].oneDayChange).toEqual(10);
      expect(result[0].sevenDayChange).toEqual(10);
      expect(result[0].thirtyDayChange).toEqual(22.22);
      expect(result[0].inceptionChange).toEqual(0);
      expect(result[0].oneDayApr).toEqual(3650);
      expect(result[0].thirtyDayApr).toEqual(270.37);
      expect(result[0].inceptionApr).toEqual(0);
    });

    it('should calculate APRs with equal prices', () => {
      const result = service.calculateVaultAprs([
        {
          vaultAddress: '0x29939b84c28aa3c8f43262b91a9ee42a8e1e7dbf',
          tokenDecimals: 6,
          latestPricePerShareWei: '100000000000000000',
          oneDayPricePerShareWei: '100000000000000000',
          sevenDayPricePerShareWei: '100000000000000000',
          thirtyDayPricePerShareWei: '100000000000000000',
          oldestPricePerShareWei: '100000000000000000',
          inceptionTimestamp: '1630000000',
        },
      ]);
      expect(result[0].vaultAddress).toEqual(
        '0x29939b84c28aa3c8f43262b91a9ee42a8e1e7dbf',
      );
      expect(result[0].oneDayChange).toEqual(0);
      expect(result[0].sevenDayChange).toEqual(0);
      expect(result[0].thirtyDayChange).toEqual(0);
      expect(result[0].inceptionChange).toEqual(0);
      expect(result[0].oneDayApr).toEqual(0);
      expect(result[0].thirtyDayApr).toEqual(0);
      expect(result[0].inceptionApr).toEqual(0);
    });

    it('should calculate APRs with zero values', () => {
      const result = service.calculateVaultAprs([
        {
          vaultAddress: '0x29939b84c28aa3c8f43262b91a9ee42a8e1e7dbf',
          tokenDecimals: 6,
          latestPricePerShareWei: '0',
          oneDayPricePerShareWei: '0',
          sevenDayPricePerShareWei: '0',
          thirtyDayPricePerShareWei: '100000000000000000',
          oldestPricePerShareWei: '0',
          inceptionTimestamp: '1630000000',
        },
      ]);
      expect(result[0].vaultAddress).toEqual(
        '0x29939b84c28aa3c8f43262b91a9ee42a8e1e7dbf',
      );
      expect(result[0].oneDayChange).toEqual(0);
      expect(result[0].sevenDayChange).toEqual(0);
      expect(result[0].thirtyDayChange).toEqual(-100);
      expect(result[0].inceptionChange).toEqual(0);
      expect(result[0].oneDayApr).toEqual(0);
      expect(result[0].thirtyDayApr).toEqual(-1216.67);
      expect(result[0].inceptionApr).toEqual(0);
    });

    it('should calculate APRs with null values', () => {
      const result = service.calculateVaultAprs([
        {
          vaultAddress: '0x29939b84c28aa3c8f43262b91a9ee42a8e1e7dbf',
          tokenDecimals: 6,
          latestPricePerShareWei: null,
          oneDayPricePerShareWei: null,
          sevenDayPricePerShareWei: null,
          thirtyDayPricePerShareWei: null,
          oldestPricePerShareWei: '10000000000000000',
          inceptionTimestamp: '1630000000',
        },
      ]);
      expect(result[0].vaultAddress).toEqual(
        '0x29939b84c28aa3c8f43262b91a9ee42a8e1e7dbf',
      );
      expect(result[0].oneDayChange).toEqual(0);
      expect(result[0].sevenDayChange).toEqual(0);
      expect(result[0].thirtyDayChange).toEqual(0);
      expect(result[0].inceptionChange).toEqual(0);
      expect(result[0].oneDayApr).toEqual(0);
      expect(result[0].thirtyDayApr).toEqual(0);
      expect(result[0].inceptionApr).toEqual(0);
    });
  });

  it('should execute cron job sucessfully', () => {
    vaultReportQueryRepository.getVaultReportSummary.mockResolvedValue([
      {
        vaultAddress: '0x29939b84c28aa3c8f43262b91a9ee42a8e1e7dbf',
        tokenDecimals: 6,
        latestPricePerShareWei: '110000000000000000',
        oneDayPricePerShareWei: '100000000000000000',
        sevenDayPricePerShareWei: '100000000000000000',
        thirtyDayPricePerShareWei: '90000000000000000',
        oldestPricePerShareWei: '110000000000000000',
        inceptionTimestamp: '1630000000',
      },
    ]);
    sequelizeVaultCommandRepository.createVaultAprStats.mockResolvedValue({});
    service.execute();
  });
});
