import {
  IUserVaultData,
  IVaultEventData,
} from '@app/core/domain/robonet/point/point';
import { VAULT_DAILY_POINT_BUDGET } from '@app/core/domain/robonet/vault/vault';
import { EVaultActivityType } from '@app/core/domain/robonet/vault-activity/vault-activity';
import { SequelizeLeaderboardCommandRepository } from '@app/core/infra/repository/robonet/leaderboard/command/sequelize.leaderboard.command.repository';
import { SequelizePointCommandRepository } from '@app/core/infra/repository/robonet/point/command/sequelize.point.command.repository';
import { SequelizePointQueryRepository } from '@app/core/infra/repository/robonet/point/query/sequelize.point.query.repository';
import { SequelizeSeasonQueryRepository } from '@app/core/infra/repository/robonet/season/query/sequelize.season.query.repository';
import { SequelizeVaultActivityRepository } from '@app/core/infra/repository/robonet/vault/query/sequelize.vault-activity.query.repository';
import { Inject, Logger } from '@nestjs/common';
import { BigNumber as BN, ethers } from 'ethers';

const ONE_DAY_MILLI = 24 * 60 * 60 * 1000;

export class RoboNetDailyPointsUseCase {
  private readonly logger = new Logger(RoboNetDailyPointsUseCase.name);

  constructor(
    @Inject(SequelizeVaultActivityRepository)
    private readonly vaultActivityQueryRepository: SequelizeVaultActivityRepository,
    @Inject(SequelizeSeasonQueryRepository)
    private readonly seasonQueryRepository: SequelizeSeasonQueryRepository,
    @Inject(SequelizePointCommandRepository)
    private readonly sequelizePointCommandRepository: SequelizePointCommandRepository,
    @Inject(SequelizePointQueryRepository)
    private readonly sequelizePointQueryRepository: SequelizePointQueryRepository,
    @Inject(SequelizeLeaderboardCommandRepository)
    private readonly sequelizeLeaderboardCommandRepository: SequelizeLeaderboardCommandRepository,
  ) {}

  async execute() {
    const today = new Date();
    // get the current season
    const currentSeason = await this.seasonQueryRepository.getSeason(today);
    if (!currentSeason) {
      throw new Error(
        'No season found for the given date:' + today.toISOString(),
      );
    }
    const seasonStartDate = currentSeason.startDate;
    const { from, to } = await this.getTimeWindow(seasonStartDate);
    // atleast one day must have passed before we can calculate points
    if (today.getTime() - to.getTime() < ONE_DAY_MILLI) {
      const diff = today.getTime() - to.getTime();
      this.logger.log(
        `Not enough time has passed for time window: [${to.toISOString()}, ${today.toISOString()}] - diff milliseconds ${diff}`,
      );
      return;
    }

    const dailyPoints = await this.calculateDailyPoints(from, to);
    // sum up the daily points to a single map of user addresses to points
    const userPointsMap = new Map<string, number>();
    for (const vaultPoints of dailyPoints.values()) {
      for (const [userAddress, userPoints] of vaultPoints.entries()) {
        const points = userPointsMap.get(userAddress) ?? 0;
        userPointsMap.set(userAddress, points + userPoints);
      }
    }
    // increment points to DB
    const increments = Array.from(userPointsMap, ([key, value]) => ({
      seasonId: currentSeason.id,
      userAddress: key,
      liquidityPoints: value,
    }));
    await this.sequelizePointCommandRepository.incrementLiquidityPointsBatch(
      increments,
    );

    // set checkpoint
    await this.sequelizePointCommandRepository.setPointsScheduleCheckpoint(
      new Date(),
      from,
      to,
    );

    // update leaderboard
    await this.sequelizeLeaderboardCommandRepository.updateLeaderboard();
  }

  /**
   * Retrieves the time window for processing points based on the season start date.
   * If a checkpoint exists, the time window will start from the checkpoint's period end date.
   * If no checkpoint exists, the time window will start from the season start date.
   * @param seasonStart The start date of the season.
   * @returns An object containing the start and end dates of the time window.
   */
  async getTimeWindow(seasonStart: Date): Promise<{ from: Date; to: Date }> {
    // get latest points job checkpoint
    const checkpoint =
      await this.sequelizePointQueryRepository.getLatestPointsJobCheckpoint();
    if (!checkpoint) {
      // this is the first time the job is running. Window: [seasonStart, seasonStart + 1 day]
      const to = new Date(seasonStart.getTime() + ONE_DAY_MILLI);
      return {
        from: seasonStart,
        to,
      };
    } else {
      // continue from the last checkpoint
      const to = new Date(checkpoint.periodEnd.getTime() + ONE_DAY_MILLI);
      return {
        from: checkpoint.periodEnd,
        to,
      };
    }
  }

  /**
   * Calculates the daily points for RoboNet vaults within the specified time interval.
   * @param startTime The start time of the interval.
   * @param endTime The end time of the interval.
   * @returns A Promise that resolves to a Map containing the daily points earned, where the keys are vault addresses and the values are Maps containing user addresses and their corresponding points as strings.
   */
  async calculateDailyPoints(
    startTime: Date,
    endTime: Date,
  ): Promise<Map<string, Map<string, number>>> {
    const dailyPointsMap = new Map<string, Map<string, number>>();
    // get the total liquidity per user per vault as of `startTime`
    const liquidityPerVaultPerUser = await this.initializeTotalLiquidity(
      startTime,
    );

    // get all the events that happened since yesterday to now
    const newEventsMap = await this.initializeNewEvents(startTime, endTime);
    // calculate the time weighted average liquidity per vault per user
    for (const vaultAddress of liquidityPerVaultPerUser.keys()) {
      // get the total liquidity per vault
      const vaultData = liquidityPerVaultPerUser.get(vaultAddress);
      if (!vaultData) continue;
      // total time weighted liquidity for the vault
      let vaultTotalTimeWeightedLiquidity = BN.from(0);
      // map to hold the time weighted liquidity per user in the vault
      const liquidityPerUserMap = new Map<
        string,
        { timeWeightedAmount: BN; pointMultiplier: number }
      >();
      // calculate the time weighted average liquidity for each user in the vault
      for (const userAddress of vaultData.keys()) {
        const userVaultData = vaultData.get(userAddress) as IUserVaultData;

        const startAmount = userVaultData.amount;
        const userStartTime = userVaultData.blockTimestamp;
        const userEndTime = Math.floor(endTime.getTime() / 1000); // getTime() returns milliseconds
        // get all the new events for this user in this vault
        const newEvents =
          newEventsMap.get(vaultAddress)?.get(userAddress) ??
          ([] as IVaultEventData[]);
        // calculate the time weighted average liquidity for the user in the vault for interval [startTime, endTime]
        const timeWeightedAmount = this.calculateTimeWeightedAverageLiquidity(
          userStartTime,
          userEndTime,
          startAmount,
          newEvents,
        );
        // append time weighted liquidity to the vault
        vaultTotalTimeWeightedLiquidity =
          vaultTotalTimeWeightedLiquidity.add(timeWeightedAmount);

        // store the time weighted liquidity per vault per user and the vault point multiplier
        liquidityPerUserMap.set(userAddress, {
          timeWeightedAmount: timeWeightedAmount,
          pointMultiplier: userVaultData.pointsMultiplier,
        });
      }
      // calulate points for each user in the vault
      for (const userAddress of liquidityPerUserMap.keys()) {
        const liquidity = liquidityPerUserMap.get(userAddress);
        if (!liquidity) continue;
        const { timeWeightedAmount, pointMultiplier } = liquidity;
        // calculate vault point buget
        const vaultTotalDailyPoints = Math.floor(
          VAULT_DAILY_POINT_BUDGET * pointMultiplier,
        );
        const userPoints = timeWeightedAmount
          .mul(BN.from(vaultTotalDailyPoints))
          .div(vaultTotalTimeWeightedLiquidity);
        // store result
        if (!dailyPointsMap.has(vaultAddress)) {
          dailyPointsMap.set(vaultAddress, new Map());
        }
        const vaultPointsMap = dailyPointsMap.get(vaultAddress);
        if (!vaultPointsMap) continue;
        vaultPointsMap.set(userAddress, userPoints.toNumber());
      }
    }
    return dailyPointsMap;
  }

  /**
   * Calculates the time weighted average liquidity for a user in a vault.
   *
   * @param startAmount The starting amount of liquidity for the user in the vault.
   * @param startTime The starting time of the calculation.
   * @param events The events that occurred since the starting time.
   * @returns The time weighted average liquidity for the user in the vault.
   */
  calculateTimeWeightedAverageLiquidity(
    startTime: number,
    endTime: number,
    startAmount: BN,
    events: IVaultEventData[],
  ): BN {
    let totalTimeWeightedAmount = BN.from(0);
    // sort events by blockTimestamp asc
    events.sort((a, b) => a.blockTimestamp - b.blockTimestamp);
    // insert the starting state as the first event
    events.unshift({ amount: startAmount, blockTimestamp: startTime });
    let accumulatedAmount = BN.from(0);
    for (let i = 0; i < events.length; i++) {
      const event = events[i];
      let nextEventTimestamp = 0;
      // if this is the last event, use the end time as the next event timestamp
      if (i === events.length - 1) {
        nextEventTimestamp = endTime;
      } else {
        nextEventTimestamp = events[i + 1].blockTimestamp;
      }
      if (nextEventTimestamp < event.blockTimestamp) {
        throw new Error(
          'Events are not sorted by blockTimestamp in ascending order',
        );
      }

      // calculate the time difference between this event and the next event
      const timeDiff = nextEventTimestamp - event.blockTimestamp;
      // calculate the new accumulated amount
      accumulatedAmount = accumulatedAmount.add(event.amount);
      // calculate the time weighted amount for the event given the time interval
      const timeWeightedAmount = accumulatedAmount.mul(timeDiff);
      // append the time weighted liquidity to the total
      totalTimeWeightedAmount = totalTimeWeightedAmount.add(timeWeightedAmount);
    }
    // calculate the time weighted average
    return totalTimeWeightedAmount.div(endTime - startTime);
  }

  /**
   * Initializes the total liquidity per vault per user.
   *
   * @param from The starting date.
   * @returns A Promise that resolves to a Map containing the total liquidity per vault per user.
   */
  async initializeTotalLiquidity(
    from: Date,
  ): Promise<Map<string, Map<string, IUserVaultData>>> {
    const liquidityPerVaultPerUser = new Map<
      string,
      Map<string, IUserVaultData>
    >();
    const totalLiquidityPerVaultPerUser =
      await this.vaultActivityQueryRepository.getTotalLiquidityPerVaultPerUser(
        from,
      );
    for (const liquidityData of totalLiquidityPerVaultPerUser) {
      const {
        userAddress,
        vaultAddress,
        lastestBlockTimestampUsed: blockTimestamp,
        pointsMultiplier,
      } = liquidityData;
      const amount = ethers.BigNumber.from(liquidityData.totalLiquidityWei);

      if (!liquidityPerVaultPerUser.has(vaultAddress)) {
        liquidityPerVaultPerUser.set(vaultAddress, new Map());
      }
      const vault = liquidityPerVaultPerUser.get(vaultAddress);
      if (!vault) continue;

      vault.set(userAddress, { amount, blockTimestamp, pointsMultiplier });
    }
    return liquidityPerVaultPerUser;
  }

  /**
   * Initializes new events for a given time range.
   * Retrieves all the events that occurred between the specified `from` and `to` dates,
   * and organizes them into a map of vault addresses to user addresses to event data.
   * Note: Each withdrawal event is represented as a negative amount and each deposit event is represented as a positive amount.
   *
   * @param from - The starting date of the time range.
   * @param to - The ending date of the time range.
   * @returns A Promise that resolves to a map of vault addresses to user addresses to event data.
   */
  async initializeNewEvents(
    from: Date,
    to: Date,
  ): Promise<Map<string, Map<string, IVaultEventData[]>>> {
    const newEventsPerVaultPerUser = new Map<
      string,
      Map<string, IVaultEventData[]>
    >();
    // get all the events that happened since yesterday to now
    const newEvents =
      await this.vaultActivityQueryRepository.getUserActivityEvents(from, to);

    for (const event of newEvents) {
      const { userAddress, vaultAddress, actionType, blockTimestamp } = event;
      let amount = BN.from(event.erc20Amount);

      // if the action is a withdraw, make the amount negative
      if (actionType.includes(EVaultActivityType.WITHDRAW)) {
        amount = amount.mul(BN.from(-1));
      }

      if (!newEventsPerVaultPerUser.has(vaultAddress)) {
        newEventsPerVaultPerUser.set(vaultAddress, new Map());
      }

      const vault = newEventsPerVaultPerUser.get(vaultAddress);
      if (!vault) continue;
      // append the event to the user in the vault
      if (!vault.has(userAddress)) {
        vault.set(userAddress, [] as IVaultEventData[]);
      }
      const user = vault.get(userAddress);
      if (!user) continue;
      user.push({ amount, blockTimestamp });
    }
    return newEventsPerVaultPerUser;
  }
}
