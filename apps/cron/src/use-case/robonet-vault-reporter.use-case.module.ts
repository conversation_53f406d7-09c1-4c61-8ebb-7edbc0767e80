import { SequelizeVaultReportQueryRepository } from '@app/core/infra/repository/robonet/vault/query/sequilize.vault-report.query.repository';
import { Module } from '@nestjs/common';

import { RoboNetVaultReporterUseCase } from './robonet-vault-reporter.use-case';

@Module({
  imports: [],
  providers: [RoboNetVaultReporterUseCase, SequelizeVaultReportQueryRepository],
  exports: [RoboNetVaultReporterUseCase],
})
export class RoboNetVaultReporterUseCaseModule {}
