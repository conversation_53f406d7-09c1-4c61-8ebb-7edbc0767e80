import { MarketDataBucket } from '@app/core/domain/allora/market-data/market-data-bucket';
import {
  MARKET_DATA_BUCKET_COMMAND_REPOSITORY,
  MarketDataBucketCommandRepository,
} from '@app/core/domain/allora/market-data/market-data-bucket.command.repository';
import {
  MARKET_DATA_BUCKET_QUERY_REPOSITORY,
  MarketDataBucketQueryRepository,
} from '@app/core/domain/allora/market-data/market-data-bucket.query.repository';
import {
  MARKET_DATA_HISTORY_QUERY_REPOSITORY,
  MarketDataHistoryQueryRepository,
} from '@app/core/domain/allora/market-data/market-data-history.query.repository';
import {
  MARKET_DATA_UPLOAD_COMMAND_REPOSITORY,
  MarketDataUploadCommandRepository,
} from '@app/core/domain/allora/market-data/market-data-upload.command.repository';
import { ENABLED_TICKER_SYMBOLS_LOWERCASE } from '@app/core/domain/allora/market-data/ticker';
import { Inject, Logger } from '@nestjs/common';

const BUCKETS_TO_UPLOAD_PER_TICKER = 2;

export class AlloraMarketDataBucketUploaderUseCase {
  private readonly logger = new Logger(
    AlloraMarketDataBucketUploaderUseCase.name,
  );

  private tickers: Readonly<string[]> = ENABLED_TICKER_SYMBOLS_LOWERCASE;
  private bucketsToUploadPerTicker: number = BUCKETS_TO_UPLOAD_PER_TICKER;

  constructor(
    @Inject(MARKET_DATA_BUCKET_COMMAND_REPOSITORY)
    private readonly bucketCommand: MarketDataBucketCommandRepository,
    @Inject(MARKET_DATA_BUCKET_QUERY_REPOSITORY)
    private readonly bucketQuery: MarketDataBucketQueryRepository,
    @Inject(MARKET_DATA_HISTORY_QUERY_REPOSITORY)
    private readonly historyQuery: MarketDataHistoryQueryRepository,
    @Inject(MARKET_DATA_UPLOAD_COMMAND_REPOSITORY)
    private readonly uploadCommand: MarketDataUploadCommandRepository,
  ) {}

  async execute() {
    const promises = this.tickers.map((ticker: string) => {
      return this.executeForTicker(ticker);
    });

    await Promise.all(promises);
  }

  private async executeForTicker(ticker: string) {
    const availableBuckets =
      await this.bucketQuery.getAvailableBucketsForTicker(
        ticker,
        this.bucketsToUploadPerTicker,
      );

    if (availableBuckets.length == 0) {
      this.logger.log(
        `[${ticker}] SKIP: no available market data buckets to upload for ticker ${ticker}`,
      );
      return;
    }

    this.logger.log(
      `[${ticker}] UPLOAD ${availableBuckets.length} available market data buckets for ticker ${ticker}`,
    );

    for (const bucket of availableBuckets) {
      if (bucket.periodicity == 'month') {
        await this.uploadMonthlyBucket(bucket);
      } else {
        this.logger.warn(
          `SKIP: bucket ${bucket.id} with periodicity "${bucket.periodicity}" is not supported`,
        );
      }
    }
  }

  private async uploadMonthlyBucket(bucket: MarketDataBucket): Promise<void> {
    const { id, ticker, periodicity, start, end, state } = bucket;

    const logContext = {
      ticker,
      periodicity,
      state,
      start,
      end,
    };
    const logPrefix = `[${ticker}][${id}][${periodicity}][${start}]`;

    this.logger.log(`${logPrefix} uploading market data bucket`, logContext);

    try {
      const candles = await this.historyQuery.getAllDays(ticker, start, end);

      const objectKey = await this.uploadCommand.uploadMonth(
        bucket.ticker,
        bucket.start,
        candles,
      );

      await this.bucketCommand.setBucketReady(bucket.id, objectKey);

      this.logger.log(
        `${logPrefix} SUCCESS: market data bucket uploaded with key ${objectKey}`,
        logContext,
      );
    } catch (error) {
      this.logger.error(
        `${logPrefix} FAILED: upload market data bucket → ${error}`,
        logContext,
      );
    }
  }

  setTickers(tickers: Readonly<string[]>) {
    this.tickers = tickers;
  }

  setBucketsToUploadPerTicker(value: number) {
    this.bucketsToUploadPerTicker = value;
  }
}
