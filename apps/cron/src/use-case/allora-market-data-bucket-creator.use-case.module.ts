import { MARKET_DATA_BUCKET_COMMAND_REPOSITORY } from '@app/core/domain/allora/market-data/market-data-bucket.command.repository';
import { MARKET_DATA_BUCKET_QUERY_REPOSITORY } from '@app/core/domain/allora/market-data/market-data-bucket.query.repository';
import { SequelizeMarketDataBucketCommandRepository } from '@app/core/infra/repository/allora/market-data/command/sequelize.market-data-bucket.command.repository';
import { SequelizeMarketDataBucketQueryRepository } from '@app/core/infra/repository/allora/market-data/query/sequelize.market-data-bucket.query.repository';
import { Module } from '@nestjs/common';

import { AlloraMarketDataBucketCreatorUseCase } from './allora-market-data-bucket-creator.use-case';

@Module({
  providers: [
    AlloraMarketDataBucketCreatorUseCase,
    {
      useClass: SequelizeMarketDataBucketCommandRepository,
      provide: MARKET_DATA_BUCKET_COMMAND_REPOSITORY,
    },
    {
      useClass: SequelizeMarketDataBucketQueryRepository,
      provide: MARKET_DATA_BUCKET_QUERY_REPOSITORY,
    },
  ],
  exports: [AlloraMarketDataBucketCreatorUseCase],
})
export class AlloraMarketDataBucketCreatorUseCaseModule {}
