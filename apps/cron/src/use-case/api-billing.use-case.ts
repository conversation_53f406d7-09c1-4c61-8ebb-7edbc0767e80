import { ApiInvoice } from '@app/core/domain/api/invoice/api-invoice';
import {
  API_INVOICE_REPOSITORY,
  ApiInvoiceRepository,
} from '@app/core/domain/api/invoice/api-invoice.repository';
import {
  REQUEST_BY_ENDPOINT_REPOSITORY,
  RequestByEndpointRepository,
} from '@app/core/domain/api/request-by-endpoint/request-by-endpoint.repository';
import {
  API_USER_REPOSITORY,
  ApiUserRepository,
} from '@app/core/domain/api/user/api-user.repository';
import { Inject, Injectable, Logger } from '@nestjs/common';

@Injectable()
export class ApiBillingUseCase {
  private readonly logger = new Logger(ApiBillingUseCase.name);
  constructor(
    @Inject(API_USER_REPOSITORY)
    private readonly apiUserRepository: ApiUserRepository,
    @Inject(API_INVOICE_REPOSITORY)
    private readonly invoiceRepository: ApiInvoiceRepository,
    @Inject(REQUEST_BY_ENDPOINT_REPOSITORY)
    private readonly requestByEndpointRepository: RequestByEndpointRepository,
  ) {}

  async execute(): Promise<void> {
    const users = await this.apiUserRepository.getAll();
    const today = new Date();
    for (const user of users) {
      if (user.billingDay !== today.getDate()) {
        this.logger.log(
          `Skipping User ${user.id} / ${user.name} / ${user.email} - not on billing day`,
        );
        continue;
      }
      this.logger.log(
        `Resetting monthly usage for user ${user.id} / ${user.name} / ${user.email}`,
      );
      const invoice = new ApiInvoice({
        id: await this.invoiceRepository.getNextId(),
        apiUserId: user.id,
        date: today,
        totalRequests: user.monthlyUsage(),
        monthlyLimit: user.tier.monthlyLimit,
        overageRequests: user.monthlyOverageRequests(),
        overageCostsPerRequest: user.tier.overageCostPerRequest,
        overageCost: user.monthlyOverageCost(),
        tier: user.tier.id,
      });
      user.resetMonthlyUsage();
      user.lastOverageEmailAtPercent = 0;
      await this.requestByEndpointRepository.resetQuotaByApiUserId(user.id);
      await this.apiUserRepository.persist(user);
      await this.invoiceRepository.persist(invoice);
      this.logger.log(
        `Successfully restarted usage for user ${user.id} / ${user.name} / ${user.email}`,
      );
    }
  }
}
