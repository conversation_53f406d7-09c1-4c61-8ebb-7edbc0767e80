{"abi": [{"inputs": [{"components": [{"internalType": "address", "name": "admin", "type": "address"}, {"internalType": "address", "name": "protocolFeeReceiver", "type": "address"}], "internalType": "struct UpshotAdapterConstructorArgs", "name": "args", "type": "tuple"}], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [], "name": "InvalidShortString", "type": "error"}, {"inputs": [{"internalType": "string", "name": "str", "type": "string"}], "name": "StringTooLong", "type": "error"}, {"inputs": [], "name": "UpshotAdapterV2AdminTurnedTopicOff", "type": "error"}, {"inputs": [], "name": "UpshotAdapterV2DuplicateDataProvider", "type": "error"}, {"inputs": [], "name": "UpshotAdapterV2EthTransferFailed", "type": "error"}, {"inputs": [], "name": "UpshotAdapterV2ExtraDataMismatch", "type": "error"}, {"inputs": [], "name": "UpshotAdapterV2InsufficientPayment", "type": "error"}, {"inputs": [], "name": "UpshotAdapterV2InvalidAggregator", "type": "error"}, {"inputs": [], "name": "UpshotAdapterV2InvalidDataProvider", "type": "error"}, {"inputs": [], "name": "UpshotAdapterV2InvalidDataProviderQuorum", "type": "error"}, {"inputs": [], "name": "UpshotAdapterV2InvalidDataTime", "type": "error"}, {"inputs": [], "name": "UpshotAdapterV2InvalidDataValiditySeconds", "type": "error"}, {"inputs": [], "name": "UpshotAdapterV2InvalidFeeHandler", "type": "error"}, {"inputs": [], "name": "UpshotAdapterV2InvalidProtocolFeeReceiver", "type": "error"}, {"inputs": [], "name": "UpshotAdapterV2InvalidTopicTitle", "type": "error"}, {"inputs": [], "name": "UpshotAdapterV2InvalidTotalFee", "type": "error"}, {"inputs": [], "name": "UpshotAdapterV2NoDataProvided", "type": "error"}, {"inputs": [], "name": "UpshotAdapterV2NotEnoughData", "type": "error"}, {"inputs": [], "name": "UpshotAdapterV2NotSwitchedOn", "type": "error"}, {"inputs": [], "name": "UpshotAdapterV2OnlyTopicOwner", "type": "error"}, {"inputs": [], "name": "UpshotAdapterV2OwnerTurnedTopicOff", "type": "error"}, {"inputs": [], "name": "UpshotAdapterV2ProtocolFeeTooHigh", "type": "error"}, {"inputs": [], "name": "UpshotAdapterV2TopicMismatch", "type": "error"}, {"anonymous": false, "inputs": [], "name": "EIP712DomainChanged", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "previousOwner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "OwnershipTransferStarted", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "previousOwner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "OwnershipTransferred", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint256", "name": "topicId", "type": "uint256"}], "name": "UpshotAdapterV2AdapterAdminTopicTurnedOff", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint256", "name": "topicId", "type": "uint256"}], "name": "UpshotAdapterV2AdapterAdminTopicTurnedOn", "type": "event"}, {"anonymous": false, "inputs": [], "name": "UpshotAdapterV2AdapterAdminTurnedOff", "type": "event"}, {"anonymous": false, "inputs": [], "name": "UpshotAdapterV2AdapterAdminTurnedOn", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint256", "name": "newProtocolFee", "type": "uint256"}], "name": "UpshotAdapterV2AdapterAdminUpdatedProtocolFee", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "protocolFeeReceiver", "type": "address"}], "name": "UpshotAdapterV2AdapterAdminUpdatedProtocolFeeReceiver", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint256", "name": "topicId", "type": "uint256"}, {"indexed": false, "internalType": "address", "name": "dataProvider", "type": "address"}], "name": "UpshotAdapterV2AdapterTopicOwnerAddedDataProvider", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "dataProvider", "type": "address"}], "name": "UpshotAdapterV2AdapterTopicOwnerRemovedDataProvider", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint256", "name": "topicId", "type": "uint256"}], "name": "UpshotAdapterV2AdapterTopicOwnerTopicTurnedOff", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint256", "name": "topicId", "type": "uint256"}], "name": "UpshotAdapterV2AdapterTopicOwnerTopicTurnedOn", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint256", "name": "topicId", "type": "uint256"}, {"indexed": false, "internalType": "contract IAggregator", "name": "aggregator", "type": "address"}], "name": "UpshotAdapterV2AdapterTopicOwnerUpdatedAggregator", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint256", "name": "topicId", "type": "uint256"}, {"indexed": false, "internalType": "uint48", "name": "dataProviderQuorum", "type": "uint48"}], "name": "UpshotAdapterV2AdapterTopicOwnerUpdatedDataProviderQuorum", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint256", "name": "topicId", "type": "uint256"}, {"indexed": false, "internalType": "uint48", "name": "dataValiditySeconds", "type": "uint48"}], "name": "UpshotAdapterV2AdapterTopicOwnerUpdatedDataValiditySeconds", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint128", "name": "totalFee", "type": "uint128"}], "name": "UpshotAdapterV2AdapterTopicOwnerUpdatedFee", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint256", "name": "topicId", "type": "uint256"}, {"indexed": false, "internalType": "contract IFeeHandler", "name": "fee<PERSON><PERSON><PERSON>", "type": "address"}], "name": "UpshotAdapterV2AdapterTopicOwnerUpdatedFeeHandler", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint256", "name": "topicId", "type": "uint256"}, {"indexed": false, "internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "UpshotAdapterV2AdapterTopicOwnerUpdatedOwner", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint256", "name": "topicId", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "numericData", "type": "uint256"}, {"indexed": false, "internalType": "address[]", "name": "dataProviders", "type": "address[]"}], "name": "UpshotAdapterV2AdapterVerifiedData", "type": "event"}, {"anonymous": false, "inputs": [{"components": [{"components": [{"internalType": "string", "name": "title", "type": "string"}, {"internalType": "address", "name": "owner", "type": "address"}, {"internalType": "uint48", "name": "recentValueTime", "type": "uint48"}, {"internalType": "uint256", "name": "recentValue", "type": "uint256"}, {"internalType": "uint256", "name": "totalFee", "type": "uint256"}, {"internalType": "contract IAggregator", "name": "aggregator", "type": "address"}, {"internalType": "bool", "name": "ownerSwitchedOn", "type": "bool"}, {"internalType": "bool", "name": "adminSwitchedOn", "type": "bool"}, {"internalType": "contract IFeeHandler", "name": "fee<PERSON><PERSON><PERSON>", "type": "address"}, {"internalType": "uint48", "name": "dataProviderQuorum", "type": "uint48"}, {"internalType": "uint48", "name": "dataValiditySeconds", "type": "uint48"}], "internalType": "struct TopicConfig", "name": "config", "type": "tuple"}, {"internalType": "address[]", "name": "validDataProviders", "type": "address[]"}], "indexed": false, "internalType": "struct TopicView", "name": "topicView", "type": "tuple"}], "name": "UpshotAdapterV2TopicAdded", "type": "event"}, {"inputs": [], "name": "NUMERIC_DATA_TYPEHASH", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "acceptOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "topicId", "type": "uint256"}, {"internalType": "address", "name": "dataProvider", "type": "address"}], "name": "addDataProvider", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"components": [{"components": [{"internalType": "string", "name": "title", "type": "string"}, {"internalType": "address", "name": "owner", "type": "address"}, {"internalType": "uint48", "name": "recentValueTime", "type": "uint48"}, {"internalType": "uint256", "name": "recentValue", "type": "uint256"}, {"internalType": "uint256", "name": "totalFee", "type": "uint256"}, {"internalType": "contract IAggregator", "name": "aggregator", "type": "address"}, {"internalType": "bool", "name": "ownerSwitchedOn", "type": "bool"}, {"internalType": "bool", "name": "adminSwitchedOn", "type": "bool"}, {"internalType": "contract IFeeHandler", "name": "fee<PERSON><PERSON><PERSON>", "type": "address"}, {"internalType": "uint48", "name": "dataProviderQuorum", "type": "uint48"}, {"internalType": "uint48", "name": "dataValiditySeconds", "type": "uint48"}], "internalType": "struct TopicConfig", "name": "config", "type": "tuple"}, {"internalType": "address[]", "name": "validDataProviders", "type": "address[]"}], "internalType": "struct TopicView", "name": "topicView", "type": "tuple"}], "name": "addTopic", "outputs": [{"internalType": "uint256", "name": "newTopicId", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "protocolFee_", "type": "uint256"}], "name": "adminSetProtocolFee", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "protocolFeeReceiver_", "type": "address"}], "name": "adminSetProtocolFeeReceiver", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "adminTurnOffAdapter", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "topicId", "type": "uint256"}], "name": "adminTurnOffTopic", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "adminTurnOnAdapter", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "topicId", "type": "uint256"}], "name": "adminTurnOnTopic", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "eip712Domain", "outputs": [{"internalType": "bytes1", "name": "fields", "type": "bytes1"}, {"internalType": "string", "name": "name", "type": "string"}, {"internalType": "string", "name": "version", "type": "string"}, {"internalType": "uint256", "name": "chainId", "type": "uint256"}, {"internalType": "address", "name": "verifyingContract", "type": "address"}, {"internalType": "bytes32", "name": "salt", "type": "bytes32"}, {"internalType": "uint256[]", "name": "extensions", "type": "uint256[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"components": [{"internalType": "uint256", "name": "topicId", "type": "uint256"}, {"internalType": "uint256", "name": "timestamp", "type": "uint256"}, {"internalType": "uint256", "name": "numericValue", "type": "uint256"}, {"internalType": "bytes", "name": "extraData", "type": "bytes"}], "internalType": "struct NumericData", "name": "numericData", "type": "tuple"}], "name": "getMessage", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "topicId", "type": "uint256"}], "name": "getTopic", "outputs": [{"components": [{"components": [{"internalType": "string", "name": "title", "type": "string"}, {"internalType": "address", "name": "owner", "type": "address"}, {"internalType": "uint48", "name": "recentValueTime", "type": "uint48"}, {"internalType": "uint256", "name": "recentValue", "type": "uint256"}, {"internalType": "uint256", "name": "totalFee", "type": "uint256"}, {"internalType": "contract IAggregator", "name": "aggregator", "type": "address"}, {"internalType": "bool", "name": "ownerSwitchedOn", "type": "bool"}, {"internalType": "bool", "name": "adminSwitchedOn", "type": "bool"}, {"internalType": "contract IFeeHandler", "name": "fee<PERSON><PERSON><PERSON>", "type": "address"}, {"internalType": "uint48", "name": "dataProviderQuorum", "type": "uint48"}, {"internalType": "uint48", "name": "dataValiditySeconds", "type": "uint48"}], "internalType": "struct TopicConfig", "name": "config", "type": "tuple"}, {"internalType": "address[]", "name": "validDataProviders", "type": "address[]"}], "internalType": "struct TopicView", "name": "topicView", "type": "tuple"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "nextTopicId", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "owner", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "pending<PERSON><PERSON>er", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "protocolFee", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "protocolFeeReceiver", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "topicId", "type": "uint256"}, {"internalType": "address", "name": "dataProvider", "type": "address"}], "name": "removeDataProvider", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "renounceOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "switchedOn", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "transferOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "topicId", "type": "uint256"}], "name": "turnOffTopic", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "topicId", "type": "uint256"}], "name": "turnOnTopic", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "topicId", "type": "uint256"}, {"internalType": "contract IAggregator", "name": "aggregator", "type": "address"}], "name": "updateAggregator", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "topicId", "type": "uint256"}, {"internalType": "uint48", "name": "dataProviderQuorum", "type": "uint48"}], "name": "updateDataProviderQuorum", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "topicId", "type": "uint256"}, {"internalType": "uint48", "name": "dataValiditySeconds", "type": "uint48"}], "name": "updateDataValiditySeconds", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "topicId", "type": "uint256"}, {"internalType": "contract IFeeHandler", "name": "fee<PERSON><PERSON><PERSON>", "type": "address"}], "name": "updateFeeHandler", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "topicId", "type": "uint256"}, {"internalType": "address", "name": "owner_", "type": "address"}], "name": "updateTopicOwner", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "topicId", "type": "uint256"}, {"internalType": "uint128", "name": "totalFee", "type": "uint128"}], "name": "updateTotalFee", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"components": [{"components": [{"internalType": "bytes", "name": "signature", "type": "bytes"}, {"components": [{"internalType": "uint256", "name": "topicId", "type": "uint256"}, {"internalType": "uint256", "name": "timestamp", "type": "uint256"}, {"internalType": "uint256", "name": "numericValue", "type": "uint256"}, {"internalType": "bytes", "name": "extraData", "type": "bytes"}], "internalType": "struct NumericData", "name": "numericData", "type": "tuple"}], "internalType": "struct SignedNumericData[]", "name": "signedNumericData", "type": "tuple[]"}, {"internalType": "bytes", "name": "extraData", "type": "bytes"}], "internalType": "struct UpshotAdapterNumericData", "name": "nd", "type": "tuple"}], "name": "verifyData", "outputs": [{"internalType": "uint256", "name": "numericValue", "type": "uint256"}], "stateMutability": "payable", "type": "function"}]}