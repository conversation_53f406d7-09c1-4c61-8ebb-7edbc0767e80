import { MARKET_DATA_BUCKET_COMMAND_REPOSITORY } from '@app/core/domain/allora/market-data/market-data-bucket.command.repository';
import { MARKET_DATA_BUCKET_QUERY_REPOSITORY } from '@app/core/domain/allora/market-data/market-data-bucket.query.repository';
import { MARKET_DATA_HISTORY_QUERY_REPOSITORY } from '@app/core/domain/allora/market-data/market-data-history.query.repository';
import { MARKET_DATA_UPLOAD_COMMAND_REPOSITORY } from '@app/core/domain/allora/market-data/market-data-upload.command.repository';
import { S3MarketDataUploadCommandRepository } from '@app/core/infra/repository/allora/market-data/command/s3.market-data-upload.command.repository';
import { SequelizeMarketDataBucketCommandRepository } from '@app/core/infra/repository/allora/market-data/command/sequelize.market-data-bucket.command.repository';
import { SequelizeMarketDataBucketQueryRepository } from '@app/core/infra/repository/allora/market-data/query/sequelize.market-data-bucket.query.repository';
import { SequelizeMarketDataHistoryQueryRepository } from '@app/core/infra/repository/allora/market-data/query/sequelize.market-data-history.query.repository';
import { Module } from '@nestjs/common';

import { AlloraMarketDataBucketUploaderUseCase } from './allora-market-data-bucket-uploader.use-case';

@Module({
  providers: [
    AlloraMarketDataBucketUploaderUseCase,
    {
      useClass: SequelizeMarketDataBucketCommandRepository,
      provide: MARKET_DATA_BUCKET_COMMAND_REPOSITORY,
    },
    {
      useClass: SequelizeMarketDataBucketQueryRepository,
      provide: MARKET_DATA_BUCKET_QUERY_REPOSITORY,
    },
    {
      useClass: SequelizeMarketDataHistoryQueryRepository,
      provide: MARKET_DATA_HISTORY_QUERY_REPOSITORY,
    },
    {
      useClass: S3MarketDataUploadCommandRepository,
      provide: MARKET_DATA_UPLOAD_COMMAND_REPOSITORY,
    },
  ],
  exports: [AlloraMarketDataBucketUploaderUseCase],
})
export class AlloraMarketDataBucketUploaderUseCaseModule {}
