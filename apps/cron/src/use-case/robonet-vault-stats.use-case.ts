import {
  IVaultAprStats,
  IVaultReportSummary,
} from '@app/core/domain/robonet/vault-report/vault-report.interface';
import { SequelizeVaultCommandRepository } from '@app/core/infra/repository/robonet/vault/command/sequelize.vault.command.repository';
import { SequelizeVaultReportQueryRepository } from '@app/core/infra/repository/robonet/vault/query/sequilize.vault-report.query.repository';
import { Inject, Logger } from '@nestjs/common';
import { ethers } from 'ethers';

export class RoboNetVaultStatsUseCase {
  private readonly logger = new Logger(RoboNetVaultStatsUseCase.name);

  constructor(
    @Inject(SequelizeVaultReportQueryRepository)
    private readonly vaultReportQueryRepository: SequelizeVaultReportQueryRepository,
    @Inject(SequelizeVaultCommandRepository)
    private readonly vaultCommandRepository: SequelizeVaultCommandRepository,
  ) {}

  async execute(): Promise<Promise<void>> {
    this.logger.log(`Fetching vault report summaries...`);
    const vaultReportSummaries =
      await this.vaultReportQueryRepository.getVaultReportSummary();

    this.logger.log(`Calculating APRs...`);
    const vaultAPRs = this.calculateVaultAprs(vaultReportSummaries);

    // update newly calculated APRs in the database
    this.logger.log(`Updating vault APRs...`);
    vaultAPRs.map(async (stats) => {
      await this.vaultCommandRepository.createVaultAprStats(stats);
    });
  }

  calculateVaultAprs(reports: IVaultReportSummary[]): IVaultAprStats[] {
    return reports.map((report) => {
      // calculate the number of days since inception
      const vaultAssetDecimals = report.tokenDecimals;
      const inceptionTimestamp = Number(report.inceptionTimestamp);
      const now = new Date(); // Get the current date and time
      const nowTimestamp = now.getTime() / 1000; // Convert current date to UNIX timestamp (milliseconds to seconds)
      const secondsSinceInception = nowTimestamp - inceptionTimestamp; // Calculate the difference in seconds
      const daysSinceInception = Math.floor(
        secondsSinceInception / (60 * 60 * 24),
      );

      // calculate each % change and APR for the respective time windows
      const oldestPriceEth = Number(
        ethers.utils.formatUnits(
          report.oldestPricePerShareWei,
          vaultAssetDecimals,
        ),
      );

      // if we don't have the latest price per share, we cannot calculate APRs
      if (!report.latestPricePerShareWei) {
        return {
          vaultAddress: report.vaultAddress,
          oneDayChange: 0,
          sevenDayChange: 0,
          thirtyDayChange: 0,
          inceptionChange: 0,
          oneDayApr: 0,
          sevenDayApr: 0,
          thirtyDayApr: 0,
          inceptionApr: 0,
        };
      }
      const latestPriceEth = Number(
        ethers.utils.formatUnits(
          report.latestPricePerShareWei,
          vaultAssetDecimals,
        ),
      );

      let oneDayChange = 0;
      let oneDayApr = 0;
      if (report.oneDayPricePerShareWei) {
        const oneDayPriceEth = Number(
          ethers.utils.formatUnits(
            report.oneDayPricePerShareWei,
            vaultAssetDecimals,
          ),
        );
        oneDayChange = this.changePercentage(latestPriceEth, oneDayPriceEth);
        oneDayApr = this.annualizeAPR(latestPriceEth, oneDayPriceEth, 1);
      }

      let sevenDayChange = 0;
      let sevenDayApr = 0;
      if (report.sevenDayPricePerShareWei) {
        const sevenDayPriceEth = Number(
          ethers.utils.formatUnits(
            report.sevenDayPricePerShareWei,
            vaultAssetDecimals,
          ),
        );
        sevenDayChange = this.changePercentage(
          latestPriceEth,
          sevenDayPriceEth,
        );
        sevenDayApr = this.annualizeAPR(latestPriceEth, sevenDayPriceEth, 7);
      }

      let thirtyDayChange = 0;
      let thirtyDayApr = 0;
      if (report.thirtyDayPricePerShareWei) {
        const thirtyDayPriceEth = Number(
          ethers.utils.formatUnits(
            report.thirtyDayPricePerShareWei,
            vaultAssetDecimals,
          ),
        );
        thirtyDayChange = this.changePercentage(
          latestPriceEth,
          thirtyDayPriceEth,
        );
        thirtyDayApr = this.annualizeAPR(latestPriceEth, thirtyDayPriceEth, 30);
      }

      const inceptionChange = this.changePercentage(
        latestPriceEth,
        oldestPriceEth,
      );
      const inceptionApr = this.annualizeAPR(
        latestPriceEth,
        oldestPriceEth,
        daysSinceInception,
      );

      return {
        vaultAddress: report.vaultAddress,
        oneDayChange,
        sevenDayChange,
        thirtyDayChange,
        inceptionChange,
        oneDayApr,
        sevenDayApr,
        thirtyDayApr,
        inceptionApr,
      };
    });
  }

  changePercentage(latestPrice: number, startPrice: number): number {
    if (startPrice === 0) {
      return 0;
    }
    const change = ((latestPrice - startPrice) / startPrice) * 100;
    return parseFloat(change.toFixed(2));
  }

  annualizeAPR(
    latestPriceEth: number,
    startingPriceEth: number,
    days: number,
  ): number {
    if (startingPriceEth === 0 || days === 0) {
      return 0;
    }
    const dailyReturn = latestPriceEth / startingPriceEth - 1;
    const annualizedAPR = dailyReturn * (365 / days) * 100; // Scaling the daily return to an annual rate
    return Number(annualizedAPR.toFixed(2));
  }
}
