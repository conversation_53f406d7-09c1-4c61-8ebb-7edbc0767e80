import { TOPIC_COMMAND_REPOSITORY } from '@app/core/domain/allora/topic/topic.command.repository';
import { ALLORA_TOPIC_QUERY_REPOSITORY } from '@app/core/domain/allora/topic/topic.query.repository';
import { CONTINUATION_TOKEN_REPOSITORY } from '@app/core/domain/continuation-token/continuation-token.repository';
import { ContinuationTokenRepositoryModule } from '@app/core/domain/continuation-token/continuation-token.repository.module';
import { SequelizeTopicCommandRepository } from '@app/core/infra/repository/allora/topic/command/sequelize.topic.command.repository';
import { SequelizeTopicQueryRepository } from '@app/core/infra/repository/allora/topic/query/sequelize.topic.query.repository';
import { Module } from '@nestjs/common';

import { AlloraTopicMetadataUpdaterUseCase } from './allora-topic-metadata-updater.use-case';

@Module({
  imports: [],
  providers: [
    AlloraTopicMetadataUpdaterUseCase,
    {
      useClass: SequelizeTopicCommandRepository,
      provide: TOPIC_COMMAND_REPOSITORY,
    },
    {
      useClass: ContinuationTokenRepositoryModule,
      provide: CONTINUATION_TOKEN_REPOSITORY,
    },
    {
      useClass: SequelizeTopicQueryRepository,
      provide: ALLORA_TOPIC_QUERY_REPOSITORY,
    },
  ],
  exports: [AlloraTopicMetadataUpdaterUseCase],
})
export class AlloraTopicMetadataUpdaterUseCaseModule {}
