import { AlloraChainConfig } from '@app/core/domain/allora/chain/allora-chain-config';
import {
  ETopicTargetValueType,
  TOPIC_CONFIGS,
  TopicConfig,
} from '@app/core/domain/allora/ground-truth/ground-truth';
import {
  ALLORA_GROUND_TRUTH_COMMAND_REPOSITORY,
  GroundTruthCommandRepository,
} from '@app/core/domain/allora/ground-truth/ground-truth.command.repository';
import {
  ALLORA_GROUND_TRUTH_QUERY_REPOSITORY,
  GroundTruthQueryRepository,
} from '@app/core/domain/allora/ground-truth/ground-truth.query.repository';
import {
  MARKET_DATA_QUERY_REPOSITORY,
  MarketDataQueryRepository,
} from '@app/core/domain/allora/market-data/market-data.query.repository';
import {
  ALLORA_TOPIC_QUERY_REPOSITORY,
  TopicQueryRepository,
} from '@app/core/domain/allora/topic/topic.query.repository';
import {
  METRICS_LOGGER,
  MetricsLogger,
} from '@app/core/domain/metrics-logger/metrics-logger';
import { EStatName } from '@app/core/domain/metrics-logger/stat-name.type';
import { parseTimeInterval } from '@app/core/domain/utils';
import { AlloraApiClient } from '@app/core/infra/allora/allora-api-client';
import {
  PROMETHEUS_METRICS_LOGGER,
  PrometheusMetricsLogger,
} from '@app/core/infra/prometheus/prometheus-metrics.logger';
import { Inject, Injectable, Logger } from '@nestjs/common';
import { backOff } from 'exponential-backoff';

interface GroundTruthResult {
  value: number;
  timestamp: number;
}

@Injectable()
export class AlloraGroundTruthUpdaterUseCase {
  private readonly logger = new Logger(AlloraGroundTruthUpdaterUseCase.name);

  private alloraApiClient: AlloraApiClient;

  private readonly SUPPORTED_TARGET_VALUE_TYPES = [
    ETopicTargetValueType.PRICE,
    ETopicTargetValueType.RETURN,
  ];

  constructor(
    @Inject(ALLORA_GROUND_TRUTH_COMMAND_REPOSITORY)
    private readonly groundTruthCommandRepository: GroundTruthCommandRepository,
    @Inject(ALLORA_GROUND_TRUTH_QUERY_REPOSITORY)
    private readonly groundTruthQueryRepository: GroundTruthQueryRepository,
    @Inject(MARKET_DATA_QUERY_REPOSITORY)
    private readonly marketDataQueryRepository: MarketDataQueryRepository,
    @Inject(ALLORA_TOPIC_QUERY_REPOSITORY)
    private readonly topicQueryRepository: TopicQueryRepository,
    @Inject(PROMETHEUS_METRICS_LOGGER)
    private readonly prometheusMetricsLogger: PrometheusMetricsLogger,
    @Inject(METRICS_LOGGER)
    private readonly metricsLogger: MetricsLogger,
  ) {}

  async execute(alloraChainConfig: AlloraChainConfig): Promise<void> {
    this.logger.log(
      `Starting ground truth updater for chain: ${alloraChainConfig.chainId}`,
    );

    this.alloraApiClient = new AlloraApiClient(
      this.prometheusMetricsLogger,
      alloraChainConfig,
    );

    let successCount = 0;
    let errorCount = 0;

    const isActiveTopic: Record<string, boolean> = {};
    // fetch all topics for the chain
    const topics = await this.topicQueryRepository.getAllTopics(
      alloraChainConfig,
    );

    for (const topic of topics) {
      isActiveTopic[topic.id] = topic.isActive ?? false;
    }

    // Process each topic based on the topics config
    for (const topicConfig of TOPIC_CONFIGS[alloraChainConfig.chainId]) {
      try {
        // skip inactive topics
        if (!isActiveTopic[topicConfig.topicId]) {
          this.logger.log(
            `Skipping topic ${topicConfig.topicId} for chain ${alloraChainConfig.chainId} because it is not active`,
          );
          continue;
        }

        // skip topics that have not supported target value types
        if (!this.SUPPORTED_TARGET_VALUE_TYPES.includes(topicConfig.type)) {
          this.logger.warn(
            `Skipping topic ${topicConfig.topicId} for chain ${alloraChainConfig.chainId} because it is not supported`,
          );
          continue;
        }

        // process topic ground truth data
        await this.processGroundTruthData(topicConfig, alloraChainConfig);

        successCount++;
        this.metricsLogger.success(
          EStatName.ALLORA,
          `GroundTruthUpdaterUseCase_${alloraChainConfig.chainId}`,
        );

        // timeout for 1 second to avoid getting rate limited by the RPC API
        await new Promise((resolve) => setTimeout(resolve, 1000));
      } catch (error) {
        errorCount++;
        this.logger.warn(
          `Error processing topic ${topicConfig.topicId} for chain ${alloraChainConfig.chainId}: ${error.message}`,
        );

        this.metricsLogger.failure(
          EStatName.ALLORA,
          `GroundTruthUpdaterUseCase_${alloraChainConfig.chainId}`,
        );
      }
    }

    this.logger.log(
      `Ground truth updater completed for chain: ${alloraChainConfig.chainId}. Success: ${successCount}, Errors: ${errorCount}`,
    );
  }

  public async processGroundTruthData(
    topicConfig: TopicConfig,
    alloraChainConfig: AlloraChainConfig,
  ): Promise<void> {
    const { topicId, type, ticker, timeInterval } = topicConfig;

    const topicIntervalMs = parseTimeInterval(timeInterval);

    // fetch topic details from the chain
    const topicDetails = await this.requestWithBackoff(() =>
      this.alloraApiClient.getTopicDetails(topicId),
    );

    const epochLastEndedHeight = topicDetails.topic.epoch_last_ended;

    // check if the ground truth is already stored for the topic
    const latestGroundTruth =
      await this.groundTruthQueryRepository.getLatestGroundTruthByTopicId(
        topicId,
        alloraChainConfig,
      );

    if (
      latestGroundTruth &&
      latestGroundTruth.epochLastEndedHeight === epochLastEndedHeight
    ) {
      this.logger.log(
        `Latest ground truth is already stored for topic ${topicId} for chain ${alloraChainConfig.chainId}. Epoch last ended height: ${epochLastEndedHeight}`,
      );
      // latest ground truth is already stored for the topic, so we can skip it
      return;
    }

    // get the last epoch start date
    const lastReputerCommitInfo = await this.requestWithBackoff(() =>
      this.alloraApiClient.getTopicLastReputerCommitInfo(topicId),
    );

    const lastEpochStartBlockHeight =
      lastReputerCommitInfo.last_commit.nonce.block_height;

    const epochStartBlockInfo = await this.requestWithBackoff(() =>
      this.alloraApiClient.getBlockByHeight(lastEpochStartBlockHeight),
    );
    const epochStartDate = new Date(epochStartBlockInfo.block.header.time);

    // get the last reputation date
    let reputationDate = new Date(epochStartDate.getTime() + topicIntervalMs);
    const currentTime = new Date();

    // if the reputation date is in the future, use the current time
    if (reputationDate.getTime() > currentTime.getTime()) {
      this.logger.warn(
        `Reputation date is in the future (${reputationDate}) for topic ${topicId} for chain ${alloraChainConfig.chainId}. Using current time (${currentTime}).`,
      );
      // use the current time minus 3 minutes rather than just the current time
      // because we want to make sure that there is OHLC data available for the reputation date
      reputationDate = new Date(currentTime.getTime() - 3 * 60 * 1000);
    }

    // Get OHLC data using the market data query repository
    const ohlcData = await this.marketDataQueryRepository.getLatestMarketData(
      [ticker],
      reputationDate,
      {
        continuationToken: undefined,
      },
    );

    if (ohlcData.data.length === 0) {
      throw new Error(
        `No OHLC data found for ticker: ${ticker} on chain: ${alloraChainConfig.chainId} at reputation date: ${reputationDate}`,
      );
    }

    // Remove the most recent (incomplete) candle
    const ohlcCandles = ohlcData.data.slice(1);

    if (ohlcCandles.length === 0) {
      throw new Error(
        `No complete candles available for ticker: ${ticker} on chain: ${alloraChainConfig.chainId} at reputation date: ${reputationDate}`,
      );
    }

    let result: GroundTruthResult;
    const reputationCandle = ohlcCandles[0];

    switch (type) {
      case ETopicTargetValueType.PRICE:
        result = {
          value: Number(reputationCandle.close),
          timestamp: Math.floor(
            new Date(reputationCandle.date).getTime() / 1000,
          ),
        };
        break;
      case ETopicTargetValueType.RETURN:
        // get ohlc data at the epoch start date
        const epochStartOhlcData =
          await this.marketDataQueryRepository.getLatestMarketData(
            [ticker],
            epochStartDate,
            {
              continuationToken: undefined,
            },
          );

        const epochStartCandle = epochStartOhlcData.data[0];
        result = {
          value: Math.log(
            Number(reputationCandle.close) / Number(epochStartCandle.close),
          ),
          timestamp: Math.floor(
            new Date(reputationCandle.date).getTime() / 1000,
          ),
        };
        break;
      default:
        throw new Error(`Unsupported metric type: ${type}`);
    }

    // Save the ground truth to the database for this specific chain
    await this.groundTruthCommandRepository.insertGroundTruth(
      {
        topicId,
        gtValue: `${result.value}`,
        timestamp: result.timestamp,
        epochLastEndedHeight: topicDetails.topic.epoch_last_ended,
        reputationTimestamp: Math.floor(reputationDate.getTime() / 1000),
      },
      alloraChainConfig,
    );
  }

  private async requestWithBackoff<T>(fn: () => Promise<T>): Promise<T> {
    return backOff(fn, {
      maxDelay: 1000,
      numOfAttempts: 3,
      timeMultiple: 1.5,
    });
  }
}
