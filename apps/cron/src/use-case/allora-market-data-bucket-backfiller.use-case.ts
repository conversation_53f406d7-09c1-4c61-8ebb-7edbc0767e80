import {
  MARKET_DATA_PROVIDER_MAIN_REPOSITORY,
  MarketDataProviderRepository,
} from '@app/core/domain/allora/market-data/marker-data.provider.repository';
import { computeFullMonthDataAvailability } from '@app/core/domain/allora/market-data/market-data';
import {
  MarketDataBucket,
  MINIMUM_ACCEPTABLE_MARKET_DATA_BUCKET_AVAILABILITY,
  PERFECT_MARKET_DATA_BUCKET_AVAILABILITY,
  ZERO_MARKET_DATA_BUCKET_AVAILABILITY,
} from '@app/core/domain/allora/market-data/market-data-bucket';
import {
  MARKET_DATA_BUCKET_COMMAND_REPOSITORY,
  MarketDataBucketCommandRepository,
} from '@app/core/domain/allora/market-data/market-data-bucket.command.repository';
import {
  MARKET_DATA_BUCKET_QUERY_REPOSITORY,
  MarketDataBucketQueryRepository,
} from '@app/core/domain/allora/market-data/market-data-bucket.query.repository';
import {
  MARKET_DATA_HISTORY_COMMAND_REPOSITORY,
  MarketDataHistoryCommandRepository,
} from '@app/core/domain/allora/market-data/market-data-history.command.repository';
import {
  MARKET_DATA_HISTORY_QUERY_REPOSITORY,
  MarketDataHistoryQueryRepository,
} from '@app/core/domain/allora/market-data/market-data-history.query.repository';
import {
  ENABLED_TICKERS,
  TickerConfig,
} from '@app/core/domain/allora/market-data/ticker';
import { Inject, Logger } from '@nestjs/common';

const BUCKETS_TO_BACKFILL_PER_TICKER = 1;

const BACKOFF_BETWEEN_TICKERS_MS = 500;
const BACKOFF_BETWEEN_BUCKETS_MS = 500;

const backoff = (ms: number) =>
  new Promise((resolve) => setTimeout(resolve, ms));

export class AlloraMarketDataBucketBackfillerUseCase {
  private readonly logger = new Logger(
    AlloraMarketDataBucketBackfillerUseCase.name,
  );

  private tickers: Readonly<TickerConfig[]> = ENABLED_TICKERS;
  private backoffBetweenTickers: number = BACKOFF_BETWEEN_TICKERS_MS;
  private backoffBetweenBuckets: number = BACKOFF_BETWEEN_BUCKETS_MS;
  private bucketsToBackfillPerTicker: number = BUCKETS_TO_BACKFILL_PER_TICKER;

  constructor(
    @Inject(MARKET_DATA_BUCKET_COMMAND_REPOSITORY)
    private readonly bucketCommand: MarketDataBucketCommandRepository,

    @Inject(MARKET_DATA_BUCKET_QUERY_REPOSITORY)
    private readonly bucketQuery: MarketDataBucketQueryRepository,

    @Inject(MARKET_DATA_HISTORY_COMMAND_REPOSITORY)
    private readonly historyCommand: MarketDataHistoryCommandRepository,

    @Inject(MARKET_DATA_HISTORY_QUERY_REPOSITORY)
    private readonly historyQuery: MarketDataHistoryQueryRepository,

    @Inject(MARKET_DATA_PROVIDER_MAIN_REPOSITORY)
    private readonly mainProvider: MarketDataProviderRepository,
  ) {}

  async execute() {
    for (let i = 0; i < this.tickers.length; i++) {
      await this.backfillTicker(this.tickers[i]);

      if (i != this.tickers.length - 1) {
        await backoff(this.backoffBetweenTickers);
      }
    }
  }

  private async backfillTicker(tickerConfig: TickerConfig): Promise<void> {
    const ticker = tickerConfig.symbol.toLowerCase();

    const pendingBuckets = await this.bucketQuery.getPendingBucketsForTicker(
      ticker,
      this.bucketsToBackfillPerTicker,
    );

    if (pendingBuckets.length == 0) {
      this.logger.log(
        `[${ticker}] no pending market data buckets to backfill for ticker ${ticker}`,
      );
      return;
    }

    this.logger.log(
      `[${ticker}] backfilling ${pendingBuckets.length} pending market data buckets for ticker ${ticker}`,
    );

    for (let i = 0; i < pendingBuckets.length; i++) {
      await this.backfillBucket(tickerConfig, pendingBuckets[i]);
      if (i != pendingBuckets.length - 1) {
        await backoff(this.backoffBetweenBuckets);
      }
    }
  }

  private async backfillBucket(
    tickerConfig: TickerConfig,
    bucket: MarketDataBucket,
  ) {
    const { id, ticker, periodicity, start, end, state } = bucket;

    const logContext = {
      ticker,
      periodicity,
      state,
      start,
      end,
    };
    const logPrefix = `[${ticker}][${id}][${periodicity}][${start}]`;

    try {
      const currentCandles = await this.historyQuery.getAllDays(
        ticker,
        start,
        end,
      );
      const currentAvailability = computeFullMonthDataAvailability(
        currentCandles,
        start,
      );

      if (currentAvailability == PERFECT_MARKET_DATA_BUCKET_AVAILABILITY) {
        await this.bucketCommand.setBucketAvailable(id, currentAvailability);
        this.logger.log(
          `${logPrefix} SUCCESS: market data bucket already has perfect OHLC data availability and ` +
            `backfill was not necessary; status was set to "available"`,
          logContext,
        );
        return;
      }

      const providerName = this.mainProvider.getProviderName();
      this.logger.debug(
        `${logPrefix} backfilling market data bucket using provider ${providerName} ` +
          `(current data availability: ${currentAvailability})`,
        logContext,
      );

      const backfillCandles = await this.mainProvider.getMarketDataFullMonth(
        tickerConfig,
        start,
      );
      const backfillAvailability = computeFullMonthDataAvailability(
        backfillCandles,
        start,
      );

      if (backfillAvailability == ZERO_MARKET_DATA_BUCKET_AVAILABILITY) {
        await this.bucketCommand.setBucketUnavailable(
          id,
          ZERO_MARKET_DATA_BUCKET_AVAILABILITY,
        );
        this.logger.log(
          `${logPrefix} UNAVAILABLE: market data provider ${providerName} did not return any OHLC candles; ` +
            `status was set to "unavailable"`,
          logContext,
        );
        return;
      }

      await this.historyCommand.upsertBatch(backfillCandles);

      const newCandles = await this.historyQuery.getAllDays(ticker, start, end);
      const newAvailability = computeFullMonthDataAvailability(
        newCandles,
        start,
      );

      if (
        newAvailability < MINIMUM_ACCEPTABLE_MARKET_DATA_BUCKET_AVAILABILITY
      ) {
        await this.bucketCommand.setBucketUnavailable(id, newAvailability);
        this.logger.warn(
          `${logPrefix} UNAVAILABLE: even after the backfill, market data bucket has insufficient OHLC ` +
            `data availability of ${newAvailability} (minimum: ${MINIMUM_ACCEPTABLE_MARKET_DATA_BUCKET_AVAILABILITY}); ` +
            `status was set to "unavailable"`,
          logContext,
        );
        return;
      }

      await this.bucketCommand.setBucketAvailable(id, newAvailability);

      this.logger.log(
        `${logPrefix} SUCCESS: backfilled market data bucket with new availability of ${newAvailability}; ` +
          `upserted ${backfillCandles.length} OHLC candles from provider ${providerName}; ` +
          `status was set to "available"`,
      );
    } catch (error) {
      this.logger.error(
        `${logPrefix} FAILED: could not backfill market data bucket (will be retried later) → ${error}`,
        logContext,
      );
    }
  }

  setTickers(tickers: Readonly<TickerConfig[]>) {
    this.tickers = tickers;
  }

  setBackoffBetweenTickers(value: number) {
    this.backoffBetweenTickers = value;
  }

  setBackoffBetweenBuckets(value: number) {
    this.backoffBetweenBuckets = value;
  }

  setBucketsToBackfillPerTicker(value: number) {
    this.bucketsToBackfillPerTicker = value;
  }
}
