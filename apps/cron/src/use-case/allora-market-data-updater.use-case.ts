import { MarketData } from '@app/core/domain/allora/market-data/market-data';
import {
  MARKET_DATA_COMMAND_REPOSITORY,
  MarketDataCommandRepository,
} from '@app/core/domain/allora/market-data/market-data.command.repository';
import {
  MARKET_DATA_QUERY_REPOSITORY,
  MarketDataQueryRepository,
} from '@app/core/domain/allora/market-data/market-data.query.repository';
import {
  MARKET_DATA_HISTORY_COMMAND_REPOSITORY,
  MarketDataHistoryCommandRepository,
} from '@app/core/domain/allora/market-data/market-data-history.command.repository';
import {
  ENABLED_TICKERS,
  TickerConfig,
} from '@app/core/domain/allora/market-data/ticker';
import { Inject, Logger } from '@nestjs/common';
import axios from 'axios';

import { TIINGO_API_KEY } from '../env.config';

export class AlloraMarketDataUpdaterUseCase {
  private readonly logger = new Logger(AlloraMarketDataUpdaterUseCase.name);
  private readonly tickerConfig: Readonly<TickerConfig[]>;
  private readonly maxBackfillDays = 14;
  private readonly maxTickersPerRequest = 5;
  private didBackfill = false;

  constructor(
    @Inject(MARKET_DATA_COMMAND_REPOSITORY)
    private readonly marketDataCommandRepository: MarketDataCommandRepository,
    @Inject(MARKET_DATA_QUERY_REPOSITORY)
    private readonly marketDataQueryRepository: MarketDataQueryRepository,
    @Inject(MARKET_DATA_HISTORY_COMMAND_REPOSITORY)
    private readonly marketDataHistoryCommandRepository: MarketDataHistoryCommandRepository,
  ) {
    this.tickerConfig = ENABLED_TICKERS;
  }

  async execute(startTime: Date, endTime: Date) {
    // when not backfilling we'd want to fetch the T - 1 minute candle too so
    // that we get the correct finalized end-state of the previous minute candle
    startTime.setMinutes(startTime.getMinutes() - 1);

    if (!this.didBackfill) {
      const latestCandle =
        await this.marketDataQueryRepository.getLatestCandle();
      const latestCandleDate = latestCandle
        ? new Date(latestCandle.date)
        : null;
      // if we have existing data and it is before the start time, we need to backfill
      if (latestCandleDate && latestCandleDate < startTime) {
        const diffInMillis = startTime.getTime() - latestCandleDate.getTime();
        const diffInMinutes = Math.floor(diffInMillis / (1000 * 60));

        // if the backfill is greater than 7 days, we skip backfilling for practical purposes
        if (diffInMinutes > this.maxBackfillDays * 24 * 60) {
          this.logger.log(
            `Aborting market data download as the data in the db is too old`,
          );
          return;
        }

        this.logger.log(
          `Need to backfill ${diffInMinutes} minutes of OHLC price data..`,
        );
        startTime = latestCandleDate;
      }
      this.didBackfill = true;
    }

    const tickerSymbols = this.tickerConfig.map(
      (tickerConfig) => tickerConfig.symbol,
    );

    this.logger.log(
      `Fetching tickers ${tickerSymbols} from ${startTime.toISOString()} to ${endTime.toISOString()}`,
    );

    const marketData = await this.fetchCryptoOhlcData(
      tickerSymbols,
      startTime,
      endTime,
      '1Min',
      TIINGO_API_KEY!,
    );

    if (!marketData || marketData.length === 0) {
      this.logger.warn(
        `No valid data found for ${
          this.tickerConfig
        } from ${startTime.toISOString()} to ${endTime.toISOString()}`,
      );
      return;
    }

    // convert tickers that need conversion
    const convertedMarketData = this.convertMarketData(marketData);

    if (convertedMarketData.length === 0) {
      this.logger.warn(
        `No data found for any ticker from ${startTime.toISOString()} to ${endTime.toISOString()}`,
      );
      return;
    }

    await this.saveMarketData(convertedMarketData);

    this.logger.log(
      `Completed fetching and storing OHLC data from ${startTime.toISOString()} to ${endTime.toISOString()}`,
    );
  }

  /**
  /**
   * Fetches OHLC (Open, High, Low, Close) data for a given cryptocurrency ticker
   * from the Tiingo API within a specified date range and resample frequency.
   * Note: Tiingo returns both aggregated and exchange-specific data. If
   * includeRawExchangeData is true, the exchange-specific data is also stored
   * together with the aggregated data.
   *
   * @param {string} ticker - The cryptocurrency ticker symbol (e.g., 'BTCUSD').
   * @param {Date} startDate - The start date for fetching data.
   * @param {Date} endDate - The end date for fetching data.
   * @param {string} resampleFreq - The frequency to resample the data (e.g., '1Min').
   * @param {string} apiKey - The API key for authenticating with the Tiingo API.
   * @returns {Promise<MarketData[]>} - A promise that resolves to an array of MarketData objects.
   */
  async fetchCryptoOhlcData(
    tickers: string[],
    startDate: Date,
    endDate: Date,
    resampleFreq: string,
    apiKey: string,
    includeRawExchangeData = false,
  ): Promise<MarketData[]> {
    // check that startDate is no older than one week
    const oneWeekAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
    if (startDate < oneWeekAgo) {
      this.logger.warn(
        `Start date is too old. Must be no older than one week.`,
      );
      return [];
    }

    const allMarketData: MarketData[] = [];
    // Split tickers into chunks
    for (let i = 0; i < tickers.length; i += this.maxTickersPerRequest) {
      const tickerChunk = tickers.slice(i, i + this.maxTickersPerRequest);
      const tickersString = tickerChunk.join(',');

      const baseUrl = 'https://api.tiingo.com/tiingo/crypto/prices';
      const headers = {
        'Content-Type': 'application/json',
      };

      const params = {
        tickers: tickersString,
        startDate: startDate.toISOString(),
        endDate: endDate.toISOString(),
        resampleFreq: resampleFreq,
        token: apiKey,
        includeRawExchangeData: includeRawExchangeData,
      };

      try {
        const response = await axios.get(baseUrl, { headers, params });
        const data = response.data;

        if (!data || data.length < 1) {
          this.logger.warn(`No data found for tickers: ${tickersString}`);
          continue;
        }

        for (const record of data) {
          const ticker = record.ticker;
          // first parse the aggregated metrics
          const priceData = record.priceData;
          allMarketData.push(...this.parseToMarketData(ticker, priceData));

          // if exchange specific data was fetched, store that also
          if (includeRawExchangeData && record?.exchangeData) {
            for (const exchange of record.exchangeData) {
              allMarketData.push(...this.parseToMarketData(ticker, exchange));
            }
          }
        }
      } catch (error) {
        this.logger.error(
          `Error fetching data for tickers ${tickersString}: ${error.message}`,
        );
        // Continue with next batch even if this one failed
        continue;
      }
    }

    return allMarketData;
  }

  /**
   * Converts market data for tickers that have a conversion configured.
   * Note: there may be multiple candles per ticker that need to be converted.
   *
   * @param marketData The market data to convert.
   * @returns The converted market data.
   */
  convertMarketData(marketData: MarketData[]): MarketData[] {
    const result: MarketData[] = [...marketData];

    // Get configs that need conversion
    const conversionsNeeded = this.tickerConfig.filter(
      (config) => config.conversion,
    );

    for (const config of conversionsNeeded) {
      const conversionTicker = config.conversion!.ticker.toLowerCase();
      const sourceTicker = config.symbol.toLowerCase();

      // Find all candles that need conversion
      const candlesToConvert = result.filter(
        (data) => data.ticker === sourceTicker,
      );

      for (const candle of candlesToConvert) {
        // Find the conversion rate candle for the same timestamp
        const conversionCandle = result.find(
          (data) =>
            data.ticker === conversionTicker && data.date === candle.date,
        );

        if (!conversionCandle) {
          this.logger.warn(
            `No conversion data found for ${config.conversion!.ticker} at ${
              candle.date
            }`,
          );
          continue;
        }

        // Update the candle in place
        const candleIndex = result.findIndex(
          (data) => data.ticker === candle.ticker && data.date === candle.date,
        );

        if (candleIndex === -1) continue;

        const conversionRate = conversionCandle.open;
        result[candleIndex] = {
          ...candle,
          ticker: candle.ticker.replace(
            config.conversion!.from.toLowerCase(),
            config.conversion!.to.toLowerCase(),
          ),
          open: candle.open * conversionRate,
          high: candle.high * conversionRate,
          low: candle.low * conversionRate,
          close: candle.close * conversionRate,
          volume: candle.volume, // volume stays in base currency units
          volumeNotional: candle.volumeNotional
            ? candle.volumeNotional * conversionRate
            : null,
        };
      }
    }

    return result;
  }

  parseToMarketData(
    ticker: string,
    records: Record<string, any>[],
  ): MarketData[] {
    return records.map((record) => ({
      ticker: ticker.toLowerCase(),
      exchangeCode: record.exchangeCode ? record.exchangeCode : 'TIINGO', // either request included exchangeCode or it's the aggregated price by TIINGO across all exchanges
      date: record.date,
      open: record.open,
      high: record.high,
      low: record.low,
      close: record.close,
      tradesDone: record.tradesDone,
      volume: record.volume,
      volumeNotional: record.volumeNotional,
    }));
  }

  private async saveMarketData(marketData: MarketData[]) {
    //
    // We store the market data in two tables:
    // - allora_ohlc_timeseries (last 30 days)
    // - allora_ohlc_timeseries_history (full history, never deleted)
    //

    await this.marketDataCommandRepository.upsertMarketDataBatch(marketData);

    await this.marketDataHistoryCommandRepository.upsertBatch(marketData);
  }
}
