import { GroundTruthCommandRepositoryModule } from '@app/core/domain/allora/ground-truth/ground-truth.command.repository.module';
import { GroundTruthQueryRepositoryModule } from '@app/core/domain/allora/ground-truth/ground-truth.query.repository.module';
import { MARKET_DATA_QUERY_REPOSITORY } from '@app/core/domain/allora/market-data/market-data.query.repository';
import { TopicQueryRepositoryModule } from '@app/core/domain/allora/topic/topic.query.repository.module';
import { ContinuationTokenRepositoryModule } from '@app/core/domain/continuation-token/continuation-token.repository.module';
import { MetricsLoggerModule } from '@app/core/domain/metrics-logger/metrics-logger.module';
import { PrometheusMetricsModule } from '@app/core/infra/prometheus/prometheus-metrics.module';
import { SequelizeMarketDataQueryRepository } from '@app/core/infra/repository/allora/market-data/query/sequelize.market-data.query.repository';
import { Module } from '@nestjs/common';

import { AlloraGroundTruthUpdaterUseCase } from './allora-ground-truth-updater.use-case';

@Module({
  imports: [
    GroundTruthCommandRepositoryModule,
    GroundTruthQueryRepositoryModule,
    TopicQueryRepositoryModule,
    ContinuationTokenRepositoryModule,
    MetricsLoggerModule,
    PrometheusMetricsModule.forRoot({ appName: 'cron-ground-truth-updater' }),
  ],
  providers: [
    AlloraGroundTruthUpdaterUseCase,
    {
      useClass: SequelizeMarketDataQueryRepository,
      provide: MARKET_DATA_QUERY_REPOSITORY,
    },
  ],
  exports: [AlloraGroundTruthUpdaterUseCase],
})
export class AlloraGroundTruthUpdaterUseCaseModule {}
