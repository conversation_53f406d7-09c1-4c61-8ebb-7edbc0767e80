import { TOPIC_COMMAND_REPOSITORY } from '@app/core/domain/allora/topic/topic.command.repository';
import { SequelizeTopicCommandRepository } from '@app/core/infra/repository/allora/topic/command/sequelize.topic.command.repository';
import { Modu<PERSON> } from '@nestjs/common';

import { AlloraStudioDataUpdaterUseCase } from './allora-studio-data-updater.use-case';

@Module({
  imports: [],
  providers: [
    AlloraStudioDataUpdaterUseCase,
    {
      useClass: SequelizeTopicCommandRepository,
      provide: TOPIC_COMMAND_REPOSITORY,
    },
  ],
  exports: [AlloraStudioDataUpdaterUseCase],
})
export class AlloraStudioDataUpdaterUseCaseModule {}
