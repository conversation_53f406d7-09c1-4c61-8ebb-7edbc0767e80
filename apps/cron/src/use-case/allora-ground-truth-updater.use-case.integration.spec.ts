import { ALLOR<PERSON>_GROUND_TRUTH_COMMAND_REPOSITORY } from '@app/core/domain/allora/ground-truth/ground-truth.command.repository';
import { ALLORA_GROUND_TRUTH_QUERY_REPOSITORY } from '@app/core/domain/allora/ground-truth/ground-truth.query.repository';
import { MARKET_DATA_QUERY_REPOSITORY } from '@app/core/domain/allora/market-data/market-data.query.repository';
import { TOPIC_COMMAND_REPOSITORY } from '@app/core/domain/allora/topic/topic.command.repository';
import { ALLORA_TOPIC_QUERY_REPOSITORY } from '@app/core/domain/allora/topic/topic.query.repository';
import { METRICS_LOGGER } from '@app/core/domain/metrics-logger/metrics-logger';
import { PROMETHEUS_METRICS_LOGGER } from '@app/core/infra/prometheus/prometheus-metrics.logger';
import { Test, TestingModule } from '@nestjs/testing';

import { AlloraGroundTruthUpdaterUseCase } from './allora-ground-truth-updater.use-case';

describe('AlloraGroundTruthUpdaterUseCase', () => {
  let service: AlloraGroundTruthUpdaterUseCase;

  const topicCommandRepository = {
    updateAllTopicsTotalStake: jest.fn(),
  };

  const groundTruthCommandRepository = {
    insertGroundTruth: jest.fn(),
  };

  const groundTruthQueryRepository = {
    getLatestGroundTruthByTopicId: jest.fn(),
  };

  const marketDataQueryRepository = {
    getLatestMarketData: jest.fn(),
  };

  const topicQueryRepository = {
    getTopicById: jest.fn(),
  };

  const prometheusMetricsLogger = {
    log: jest.fn(),
  };

  const metricsLogger = {
    log: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [AlloraGroundTruthUpdaterUseCase],
    })
      .useMocker((token) => {
        if (token === TOPIC_COMMAND_REPOSITORY) return topicCommandRepository;
        if (token === ALLORA_GROUND_TRUTH_COMMAND_REPOSITORY)
          return groundTruthCommandRepository;
        if (token === ALLORA_GROUND_TRUTH_QUERY_REPOSITORY)
          return groundTruthQueryRepository;
        if (token === MARKET_DATA_QUERY_REPOSITORY)
          return marketDataQueryRepository;
        if (token === ALLORA_TOPIC_QUERY_REPOSITORY)
          return topicQueryRepository;
        if (token === PROMETHEUS_METRICS_LOGGER) return prometheusMetricsLogger;
        if (token === METRICS_LOGGER) return metricsLogger;
        return {};
      })
      .compile();
    service = module.get<AlloraGroundTruthUpdaterUseCase>(
      AlloraGroundTruthUpdaterUseCase,
    );
  });

  afterEach(async () => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });
});
