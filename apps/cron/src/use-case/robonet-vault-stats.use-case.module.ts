import { SequelizeVaultCommandRepository } from '@app/core/infra/repository/robonet/vault/command/sequelize.vault.command.repository';
import { SequelizeVaultReportQueryRepository } from '@app/core/infra/repository/robonet/vault/query/sequilize.vault-report.query.repository';
import { Module } from '@nestjs/common';

import { RoboNetVaultStatsUseCase } from './robonet-vault-stats.use-case';

@Module({
  imports: [],
  providers: [
    RoboNetVaultStatsUseCase,
    SequelizeVaultCommandRepository,
    SequelizeVaultReportQueryRepository,
  ],
  exports: [RoboNetVaultStatsUseCase],
})
export class RoboNetVaultStatsUseCaseModule {}
