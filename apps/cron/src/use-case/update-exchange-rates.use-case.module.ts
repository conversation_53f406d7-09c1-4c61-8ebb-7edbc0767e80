import { ExchangeRateRepositoryModule } from '@app/core/domain/exchange-rate/exchange-rate.repository.module';
import { Module } from '@nestjs/common';
import { ClsModule } from 'nestjs-cls';

import { UpdateExchangeRatesUseCase } from './update-exchange-rates.use-case';

@Module({
  imports: [ExchangeRateRepositoryModule, ClsModule],
  providers: [UpdateExchangeRatesUseCase],
  exports: [UpdateExchangeRatesUseCase],
})
export class UpdateExchangeRatesUseCaseModule {}
