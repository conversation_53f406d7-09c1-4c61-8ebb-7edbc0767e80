import { newS3Client } from '@app/core/infra/db/s3.client';
import {
  downloadS3Object,
  parseCsvRows,
} from '@app/core/infra/repository/allora/market-data/command/s3.market-data-upload.command.repository.integration.spec.shared';
import { PROVIDER_NAME } from '@app/core/infra/repository/allora/market-data/providers/tiingo.market-data.provider.repository';
import { SequelizeModule } from '@nestjs/sequelize';
import { Test, TestingModule } from '@nestjs/testing';
import { S3 } from 'aws-sdk';
import { Sequelize } from 'sequelize-typescript';

import { reloadFixtures } from '../../../../test-integration/fixtures';
import { sequelizeOptions } from '../../../rest-api/src/config';
import { AlloraMarketDataBucketUploaderUseCase } from './allora-market-data-bucket-uploader.use-case';
import { AlloraMarketDataBucketUploaderUseCaseModule } from './allora-market-data-bucket-uploader.use-case.module';

describe('AlloraMarketDataBucketUploaderUseCase', () => {
  let service: AlloraMarketDataBucketUploaderUseCase;
  let sequelize: Sequelize;
  let s3Client: S3;

  beforeEach(async () => {
    sequelize = new Sequelize({
      ...sequelizeOptions,
      dialectOptions: {
        multipleStatements: true,
      },
    });

    s3Client = newS3Client();

    const module: TestingModule = await Test.createTestingModule({
      imports: [
        SequelizeModule.forRoot(sequelizeOptions),
        AlloraMarketDataBucketUploaderUseCaseModule,
      ],
    }).compile();

    await module.init();

    service = module.get(AlloraMarketDataBucketUploaderUseCase);

    service.setBucketsToUploadPerTicker(2);

    await reloadFixtures(sequelize, ['allora-ohlc-timeseries-history']);

    await sequelize.query(`TRUNCATE TABLE allora_ohlc_buckets`);
  });

  afterAll(async () => {
    await sequelize.close();
  });

  test('execute for one ticker, uploading a bucket that is available [btcusd][2025-02]', async () => {
    const ticker = 'btcusd';

    service.setTickers([ticker]);

    await sequelize.query(`
        INSERT INTO allora_ohlc_buckets
            ("ticker", "periodicity", "state", "start", "end")
        VALUES
            ('${ticker}', 'month', 'available', '2025-02-01', '2025-02-28');
    `);

    await service.execute();

    const [results] = await sequelize.query(`
        SELECT * FROM allora_ohlc_buckets 
        WHERE ticker='${ticker}'
          AND start='2025-02-01';
    `);

    expect(results).toHaveLength(1);

    const bucket = results[0] as any;
    expect(bucket).toHaveProperty('ticker', ticker);
    expect(bucket).toHaveProperty('state', 'ready');

    expect(bucket.s3_object_key).toBe('btcusd_month_2025-02.csv');

    const uploadedContents = await downloadS3Object(
      s3Client,
      bucket.s3_object_key,
    );
    const uploadedRows = await parseCsvRows(uploadedContents);

    const [expectedCandles] = await sequelize.query(`
        SELECT * FROM allora_ohlc_timeseries_history 
        WHERE ticker='${ticker}'
          AND date::DATE >= DATE '2025-02-01'
          AND date::DATE <= DATE '2025-02-28';
    `);

    expect(expectedCandles).toHaveLength(60 * 24 * 28);
    expect(uploadedRows).toHaveLength(expectedCandles.length);

    const sampleRow = uploadedRows[33];
    expect(sampleRow).toEqual({
      ticker: ticker,
      date: '2025-02-01T00:33:00.000Z',
      exchange_code: PROVIDER_NAME,
      open: '102353.35771527256',
      high: '102380.00663321537',
      low: '102334.36861044224',
      close: '102379.97587314947',
      trades_done: '424',
      volume: '1.5901606700000006',
      volume_notional: '162800.61102903125',
    });
  });

  test('execute for one ticker, ignoring if no buckets are available [lunausdt]', async () => {
    const ticker = 'lunausdt';

    service.setTickers([ticker]);

    await sequelize.query(`
        INSERT INTO allora_ohlc_buckets
            ("ticker", "periodicity", "state", "start", "end")
        VALUES
            ('${ticker}', 'month', 'pending', '2023-06-01', '2023-06-30');
    `);

    await service.execute();

    const [results] = await sequelize.query(`
        SELECT * FROM allora_ohlc_buckets 
        WHERE ticker='${ticker}'
          AND start='2023-06-01';
    `);

    expect(results).toHaveLength(1);

    const bucket = results[0] as any;
    expect(bucket).toHaveProperty('ticker', ticker);
    expect(bucket).toHaveProperty('state', 'pending');
    expect(bucket).toHaveProperty('s3_object_key', null);
  });

  test('execute for multiple tickers, uploading only buckets that are available', async () => {
    const tickers = ['btcusd', 'ethusd', 'lunausdt', 'solusd'];

    service.setTickers(tickers);

    await sequelize.query(`
        INSERT INTO allora_ohlc_buckets
            ("ticker", "periodicity", "state", "start", "end", "s3_object_key")
        VALUES
            ('btcusd', 'month', 'ready',     '2024-11-01', '2025-11-30', 'foo.bar.csv'),
            ('btcusd', 'month', 'pending',   '2024-12-01', '2025-12-31', NULL),
            ('btcusd', 'month', 'pending',   '2025-01-01', '2025-01-31', NULL),
            ('btcusd', 'month', 'available', '2025-02-01', '2025-02-28', NULL),
            ('btcusd', 'month', 'pending',   '2025-03-01', '2025-03-30', NULL),

            ('ethusd', 'month', 'ready',     '2024-11-01', '2025-11-30', 'foo.bar.csv'),
            ('ethusd', 'month', 'pending',   '2024-12-01', '2025-12-31', NULL),
            ('ethusd', 'month', 'pending',   '2025-01-01', '2025-01-31', NULL),
            ('ethusd', 'month', 'available', '2025-02-01', '2025-02-28', NULL),
            ('ethusd', 'month', 'pending',   '2025-03-01', '2025-03-30', NULL),

            ('solusd', 'month', 'pending',   '2024-11-01', '2025-11-30', NULL),
            ('solusd', 'month', 'pending',   '2024-12-01', '2025-12-31', NULL),
            ('solusd', 'month', 'pending',   '2025-01-01', '2025-01-31', NULL),
            ('solusd', 'month', 'pending',   '2025-02-01', '2025-02-28', NULL),
            ('solusd', 'month', 'pending',   '2025-03-01', '2025-03-30', NULL);
    `);

    await service.execute();

    const [btcusd] = (await sequelize.query(`
        SELECT * FROM allora_ohlc_buckets 
        WHERE ticker = 'btcusd'
        ORDER BY start ASC;
    `)) as any;

    expect(btcusd[0].start).toBe('2024-11-01');
    expect(btcusd[0].state).toBe('ready');
    expect(btcusd[0].s3_object_key).toBe('foo.bar.csv');

    expect(btcusd[1].start).toBe('2024-12-01');
    expect(btcusd[1].state).toBe('pending');
    expect(btcusd[1].s3_object_key).toBeNull();

    expect(btcusd[2].start).toBe('2025-01-01');
    expect(btcusd[2].state).toBe('pending');
    expect(btcusd[2].s3_object_key).toBeNull();

    expect(btcusd[3].start).toBe('2025-02-01');
    expect(btcusd[3].state).toBe('ready');
    expect(btcusd[3].s3_object_key).toBe('btcusd_month_2025-02.csv');

    expect(btcusd[4].start).toBe('2025-03-01');
    expect(btcusd[4].state).toBe('pending');
    expect(btcusd[4].s3_object_key).toBeNull();

    const [ethusd] = (await sequelize.query(`
        SELECT * FROM allora_ohlc_buckets 
        WHERE ticker = 'ethusd'
        ORDER BY start ASC;
    `)) as any;

    expect(ethusd[0].start).toBe('2024-11-01');
    expect(ethusd[0].state).toBe('ready');
    expect(ethusd[0].s3_object_key).toBe('foo.bar.csv');

    expect(ethusd[1].start).toBe('2024-12-01');
    expect(ethusd[1].state).toBe('pending');
    expect(ethusd[1].s3_object_key).toBeNull();

    expect(ethusd[2].start).toBe('2025-01-01');
    expect(ethusd[2].state).toBe('pending');
    expect(ethusd[2].s3_object_key).toBeNull();

    expect(ethusd[3].start).toBe('2025-02-01');
    expect(ethusd[3].state).toBe('ready');
    expect(ethusd[3].s3_object_key).toBe('ethusd_month_2025-02.csv');

    expect(ethusd[4].start).toBe('2025-03-01');
    expect(ethusd[4].state).toBe('pending');
    expect(ethusd[4].s3_object_key).toBeNull();

    const [solusd] = (await sequelize.query(`
        SELECT * FROM allora_ohlc_buckets 
        WHERE ticker = 'solusd'
        ORDER BY start ASC;
    `)) as any;

    expect(solusd[0].start).toBe('2024-11-01');
    expect(solusd[0].state).toBe('pending');
    expect(solusd[0].s3_object_key).toBeNull();

    expect(solusd[1].start).toBe('2024-12-01');
    expect(solusd[1].state).toBe('pending');
    expect(solusd[1].s3_object_key).toBeNull();

    expect(solusd[2].start).toBe('2025-01-01');
    expect(solusd[2].state).toBe('pending');
    expect(solusd[2].s3_object_key).toBeNull();

    expect(solusd[3].start).toBe('2025-02-01');
    expect(solusd[3].state).toBe('pending');
    expect(solusd[3].s3_object_key).toBeNull();

    expect(solusd[4].start).toBe('2025-03-01');
    expect(solusd[4].state).toBe('pending');
    expect(solusd[4].s3_object_key).toBeNull();
  });
});
