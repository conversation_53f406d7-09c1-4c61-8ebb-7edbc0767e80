import { VAULT_REPORTER_PK } from '@app/core/config/env';
import { CHAIN_ID_TO_NETWORK_URL } from '@app/core/domain/robonet/robonet-common/robonet-utils';
import { SequelizeVaultReportQueryRepository } from '@app/core/infra/repository/robonet/vault/query/sequilize.vault-report.query.repository';
import { Inject, Logger } from '@nestjs/common';
import { ethers } from 'ethers';

export class RoboNetVaultReporterUseCase {
  private readonly logger = new Logger(RoboNetVaultReporterUseCase.name);

  constructor(
    @Inject(SequelizeVaultReportQueryRepository)
    private readonly vaultReportQueryRepository: SequelizeVaultReportQueryRepository,
  ) {}

  async execute(): Promise<void> {
    this.logger.log(`Fetching vault to report on...`);
    const vaultsToReport =
      await this.vaultReportQueryRepository.getVaultsToReport();

    vaultsToReport.map(async (item) => {
      const vaultAddress = item.vaultAddress;
      const chainId = item.chainId;
      const vaultContract = this.getVaultContract(vaultAddress, chainId);
      // report transcation will fail if the our wallet is not whitelisted
      try {
        await vaultContract.report();
        this.logger.log(`Reported vault: ${vaultAddress}`);
      } catch (error) {
        this.logger.error(`Vault: ${vaultAddress} failed to report: ${error}`);
      }
    });
  }

  getVaultContract(vaultAddress: string, chainId: number): ethers.Contract {
    const provider = new ethers.providers.JsonRpcProvider(
      CHAIN_ID_TO_NETWORK_URL[chainId],
    );
    const wallet = new ethers.Wallet(VAULT_REPORTER_PK, provider);
    const abi = [
      {
        type: 'function',
        name: 'report',
        inputs: [],
        outputs: [
          { name: '_profit', type: 'uint256', internalType: 'uint256' },
          { name: '_loss', type: 'uint256', internalType: 'uint256' },
        ],
        stateMutability: 'nonpayable',
      },
    ];
    return new ethers.Contract(vaultAddress, abi, wallet);
  }
}
