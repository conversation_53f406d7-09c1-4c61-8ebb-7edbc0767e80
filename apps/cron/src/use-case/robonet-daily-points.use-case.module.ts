import { ContinuationTokenRepositoryModule } from '@app/core/domain/continuation-token/continuation-token.repository.module';
import { SequelizeLeaderboardCommandRepository } from '@app/core/infra/repository/robonet/leaderboard/command/sequelize.leaderboard.command.repository';
import { SequelizePointCommandRepository } from '@app/core/infra/repository/robonet/point/command/sequelize.point.command.repository';
import { SequelizePointQueryRepository } from '@app/core/infra/repository/robonet/point/query/sequelize.point.query.repository';
import { SequelizeSeasonQueryRepository } from '@app/core/infra/repository/robonet/season/query/sequelize.season.query.repository';
import { SequelizeVaultActivityRepository } from '@app/core/infra/repository/robonet/vault/query/sequelize.vault-activity.query.repository';
import { Module } from '@nestjs/common';

import { RoboNetDailyPointsUseCase } from './robonet-daily-points.use-case';

@Module({
  imports: [ContinuationTokenRepositoryModule],
  providers: [
    RoboNetDailyPointsUseCase,
    SequelizeVaultActivityRepository,
    SequelizePointCommandRepository,
    SequelizePointQueryRepository,
    SequelizeLeaderboardCommandRepository,
    SequelizeSeasonQueryRepository,
  ],
  exports: [RoboNetDailyPointsUseCase],
})
export class RoboNetDailyPointsUseCaseModule {}
