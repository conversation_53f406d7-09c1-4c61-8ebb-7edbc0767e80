import { IVaultEventData } from '@app/core/domain/robonet/point/point';
import { SequelizeSeasonQueryRepository } from '@app/core/infra/repository/robonet/season/query/sequelize.season.query.repository';
import { SequelizeVaultActivityRepository } from '@app/core/infra/repository/robonet/vault/query/sequelize.vault-activity.query.repository';
import { Test, TestingModule } from '@nestjs/testing';
import { BigNumber as BN, ethers } from 'ethers';
import { Sequelize } from 'sequelize-typescript';

import { reloadFixtures } from '../../../../test-integration/fixtures';
import { sequelizeOptions } from '../../../rest-api/src/config';
import { RoboNetDailyPointsUseCase } from './robonet-daily-points.use-case';

describe('RoboNetDailyPointsUseCase', () => {
  let service: RoboNetDailyPointsUseCase;
  let sequelize: Sequelize;

  const startTime = 1713946200;
  const vault1 = '******************************************';
  const vault2 = '******************************************';
  const user1 = '******************************************';
  const user2 = '******************************************';
  const user3 = '******************************************';
  const user4 = '******************************************';

  const vaultAcitivtyQueryRepository = {
    getTotalLiquidityPerVaultPerUser: jest.fn(),
    getUserActivityEvents: jest.fn(),
  };

  const seasonQueryRepository = {
    getSeason: jest.fn(),
  };

  beforeAll(async () => {
    sequelize = new Sequelize({
      ...sequelizeOptions,
      dialectOptions: {
        multipleStatements: true,
      },
    });
    await reloadFixtures(sequelize, [
      'vaults',
      'vault-activity',
      'robonet-seasons',
    ]);
  });

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [RoboNetDailyPointsUseCase],
    })
      .useMocker((token) => {
        if (token === SequelizeVaultActivityRepository)
          return vaultAcitivtyQueryRepository;
        if (token === SequelizeSeasonQueryRepository)
          return seasonQueryRepository;
        return {};
      })
      .compile();
    service = module.get<RoboNetDailyPointsUseCase>(RoboNetDailyPointsUseCase);
    // mock functions
    seasonQueryRepository.getSeason.mockResolvedValue({
      id: 1,
      name: 'Test Season',
      startDate: new Date('2023-04-26T16:52:33.385Z'),
      endDate: new Date('2024-04-25T16:52:33.385Z'),
    });
    // aggregated total liquidity per user per vault
    vaultAcitivtyQueryRepository.getTotalLiquidityPerVaultPerUser.mockResolvedValue(
      [
        {
          totalLiquidityWei: ethers.utils.parseEther('9').toString(),
          userAddress: user1,
          vaultAddress: vault1,
          lastestBlockTimestampUsed: startTime,
          pointsMultiplier: 1,
        },
        {
          totalLiquidityWei: ethers.utils.parseEther('1').toString(),
          userAddress: user2,
          vaultAddress: vault1,
          lastestBlockTimestampUsed: startTime,
          pointsMultiplier: 1,
        },
        {
          totalLiquidityWei: ethers.utils.parseEther('500').toString(),
          userAddress: user3,
          vaultAddress: vault2,
          lastestBlockTimestampUsed: startTime,
          pointsMultiplier: 1,
        },
        {
          totalLiquidityWei: '0',
          userAddress: user4,
          vaultAddress: vault2,
          lastestBlockTimestampUsed: startTime,
          pointsMultiplier: 1,
        },
      ],
    );
    // new events
    vaultAcitivtyQueryRepository.getUserActivityEvents.mockResolvedValue([
      {
        userAddress: user1,
        vaultAddress: vault1,
        actionType: 'DEPOSIT',
        erc20Amount: ethers.utils.parseEther('10').toString(),
        blockTimestamp: startTime + 200,
      },
      {
        userAddress: user1,
        vaultAddress: vault1,
        actionType: 'DEPOSIT',
        erc20Amount: ethers.utils.parseEther('10').toString(),
        blockTimestamp: startTime + 400,
      },
      {
        userAddress: user2,
        vaultAddress: vault1,
        actionType: 'WITHDRAW',
        erc20Amount: ethers.utils.parseEther('1').toString(),
        blockTimestamp: startTime + 100,
      },
    ]);
  });

  afterEach(async () => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('Distribute daily points', () => {
    it('calculateDailyPoints()', async () => {
      const startTimestamp = new Date(startTime * 1000);
      const endTimestamp = new Date((startTime + 600) * 1000);
      const res = await service.calculateDailyPoints(
        startTimestamp,
        endTimestamp,
      );
      // max 1000 points per day per vault
      expect(res.get(vault1)?.get(user1)?.toString()).toBe('991');
      expect(res.get(vault1)?.get(user2)?.toString()).toBe('8');
      expect(res.get(vault2)?.get(user3)?.toString()).toBe('1000');
      expect(res.get(vault2)?.get(user4)?.toString()).toBe('0');
    });

    it('initializeTotalLiquidity()', async () => {
      const today = new Date();
      const from = new Date();
      from.setDate(today.getDate() - 1);
      const res = await service.initializeTotalLiquidity(from);
      expect(res.get(vault1)?.get(user1)?.amount.toString()).toBe(
        ethers.utils.parseEther('9').toString(),
      );
      expect(res.get(vault1)?.get(user2)?.amount.toString()).toBe(
        ethers.utils.parseEther('1').toString(),
      );
      expect(res.get(vault2)?.get(user3)?.amount.toString()).toBe(
        ethers.utils.parseEther('500').toString(),
      );
      expect(res.get(vault2)?.get(user4)?.amount.toString()).toBe('0');
    });

    it('initializeNewEvents()', async () => {
      const today = new Date();
      const from = new Date();
      const to = new Date();
      from.setDate(today.getDate() - 2);
      to.setDate(today.getDate() - 1);
      const res = await service.initializeNewEvents(from, to);
      expect(res.get(vault1)?.get(user1)?.[0].amount.toString()).toBe(
        ethers.utils.parseEther('10').toString(),
      );
      expect(res.get(vault1)?.get(user1)?.[1].amount.toString()).toBe(
        ethers.utils.parseEther('10').toString(),
      );
      expect(res.get(vault1)?.get(user2)?.[0].amount.toString()).toBe(
        ethers.utils.parseEther('-1').toString(),
      );
    });

    it('calculateTimeWeightedAverageLiquidity()', async () => {
      const startAmount = BN.from('1000');
      const startTime = 1713946100;
      const endTime = 1713946400;
      const newEvents: IVaultEventData[] = [
        {
          amount: ethers.utils.parseEther('100'),
          blockTimestamp: 1713946200,
        },
        {
          amount: ethers.utils.parseEther('150'),
          blockTimestamp: 1713946250,
        },
        {
          amount: ethers.utils.parseEther('-100'),
          blockTimestamp: 1713946300,
        },
      ];
      const liquidityWei = service.calculateTimeWeightedAverageLiquidity(
        startTime,
        endTime,
        startAmount,
        newEvents,
      );
      const liquidityEth = Math.floor(
        Number(ethers.utils.formatEther(liquidityWei)),
      );
      expect(liquidityEth).toBe(108);
    });

    it('calculateTimeWeightedAverageLiquidity() - with ending liqudity of 0', async () => {
      const startAmount = BN.from('100');
      const startTime = 1713946200;
      const endTime = 1713946600;
      const newEvents: IVaultEventData[] = [
        {
          amount: BN.from('-100'),
          blockTimestamp: 1713946400,
        },
      ];
      const res = service.calculateTimeWeightedAverageLiquidity(
        startTime,
        endTime,
        startAmount,
        newEvents,
      );
      expect(res.toString()).toBe('50');
    });
  });
});
