import {
  newTiingoRequestScope,
  stubMarketDataRequestFullDay,
} from '@app/core/infra/repository/allora/market-data/providers/tiingo.market-data.provider.repository.integration.spec.shared';
import { SequelizeModule } from '@nestjs/sequelize';
import { Test, TestingModule } from '@nestjs/testing';
import * as nock from 'nock';
import { Sequelize } from 'sequelize-typescript';

import { reloadFixtures } from '../../../../test-integration/fixtures';
import { sequelizeOptions } from '../../../rest-api/src/config';
import { AlloraMarketDataBucketBackfillerUseCase } from './allora-market-data-bucket-backfiller.use-case';
import { AlloraMarketDataBucketBackfillerUseCaseModule } from './allora-market-data-bucket-backfiller.use-case.module';

describe('AlloraMarketDataBucketBackfillerUseCase', () => {
  let service: AlloraMarketDataBucketBackfillerUseCase;
  let sequelize: Sequelize;

  beforeEach(async () => {
    sequelize = new Sequelize({
      ...sequelizeOptions,
      dialectOptions: {
        multipleStatements: true,
      },
    });

    const module: TestingModule = await Test.createTestingModule({
      imports: [
        SequelizeModule.forRoot(sequelizeOptions),
        AlloraMarketDataBucketBackfillerUseCaseModule,
      ],
    }).compile();

    await module.init();

    service = module.get(AlloraMarketDataBucketBackfillerUseCase);

    service.setBackoffBetweenBuckets(10);
    service.setBackoffBetweenTickers(10);
    service.setBucketsToBackfillPerTicker(2);

    await reloadFixtures(sequelize, ['allora-ohlc-timeseries-history']);

    await sequelize.query(`TRUNCATE TABLE allora_ohlc_buckets`);

    nock.disableNetConnect();
  });

  afterEach(() => {
    nock.cleanAll();
  });

  afterAll(async () => {
    await sequelize.close();
    nock.enableNetConnect();
  });

  test('do nothing when there are no pending buckets', async () => {
    const tickers = ['solusd'];

    service.setTickers(
      tickers.map((i) => ({
        symbol: i,
      })),
    );

    await sequelize.query(`
        INSERT INTO allora_ohlc_buckets
            ("ticker", "periodicity", "state", "start", "end", "s3_object_key")
        VALUES
            ('solusd', 'month', 'available',   '2024-11-01', '2025-11-30', NULL),
            ('solusd', 'month', 'available',   '2024-12-01', '2025-12-31', NULL),
            ('solusd', 'month', 'available',   '2025-01-01', '2025-01-31', NULL),
            ('solusd', 'month', 'ready',   '2025-02-01', '2025-02-28', 'foo.bar.csv'),
            ('solusd', 'month', 'available',   '2025-03-01', '2025-03-30', NULL);
    `);

    await service.execute();

    const [solusd] = (await sequelize.query(`
        SELECT * FROM allora_ohlc_buckets 
        WHERE ticker = 'solusd'
        ORDER BY start ASC;
    `)) as any;

    expect(solusd[0].start).toBe('2024-11-01');
    expect(solusd[0].state).toBe('available');

    expect(solusd[1].start).toBe('2024-12-01');
    expect(solusd[1].state).toBe('available');

    expect(solusd[2].start).toBe('2025-01-01');
    expect(solusd[2].state).toBe('available');

    expect(solusd[3].start).toBe('2025-02-01');
    expect(solusd[3].state).toBe('ready');

    expect(solusd[4].start).toBe('2025-03-01');
    expect(solusd[4].state).toBe('available');
  });

  test('transition one pending bucket to available when all OHLC data is already present [perfect availability]', async () => {
    const tickers = ['btcusd'];

    service.setTickers(
      tickers.map((i) => ({
        symbol: i,
      })),
    );

    await sequelize.query(`
        INSERT INTO allora_ohlc_buckets
            ("ticker", "periodicity", "state", "start", "end", "s3_object_key")
        VALUES
            ('btcusd', 'month', 'pending',   '2025-02-01', '2025-02-28', NULL);
    `);

    await service.execute();

    const [bucket] = (await sequelize.query(`
        SELECT * FROM allora_ohlc_buckets 
        WHERE ticker = 'btcusd' AND
              start = '2025-02-01'
        ORDER BY start ASC;
    `)) as any;

    expect(bucket[0].state).toBe('available');
    expect(bucket[0].availability).toBe('1');
  });

  test('backfill missing OHLC data when OHLC data is missing [solusd]', async () => {
    const ticker = 'solusd';

    service.setTickers([
      {
        symbol: ticker,
      },
    ]);

    await sequelize.query(`
        INSERT INTO allora_ohlc_buckets
            ("ticker", "periodicity", "state", "start", "end", "availability")
        VALUES
            ('solusd', 'month', 'pending',   '2024-11-01', '2024-11-30', 0);
    `);

    let scope = newTiingoRequestScope();
    scope = await stubMarketDataRequestFullDay(scope, ticker, '2024-11-01');
    scope = await stubMarketDataRequestFullDay(scope, ticker, '2024-11-02');
    scope = await stubMarketDataRequestFullDay(scope, ticker, '2024-11-03');
    scope = await stubMarketDataRequestFullDay(scope, ticker, '2024-11-04');
    scope = await stubMarketDataRequestFullDay(scope, ticker, '2024-11-05');
    scope = await stubMarketDataRequestFullDay(scope, ticker, '2024-11-06');
    scope = await stubMarketDataRequestFullDay(scope, ticker, '2024-11-07');
    scope = await stubMarketDataRequestFullDay(scope, ticker, '2024-11-08');
    scope = await stubMarketDataRequestFullDay(scope, ticker, '2024-11-09');
    scope = await stubMarketDataRequestFullDay(scope, ticker, '2024-11-10');
    scope = await stubMarketDataRequestFullDay(scope, ticker, '2024-11-11');
    scope = await stubMarketDataRequestFullDay(scope, ticker, '2024-11-12');
    scope = await stubMarketDataRequestFullDay(scope, ticker, '2024-11-13');
    scope = await stubMarketDataRequestFullDay(scope, ticker, '2024-11-14');
    scope = await stubMarketDataRequestFullDay(scope, ticker, '2024-11-15');
    scope = await stubMarketDataRequestFullDay(scope, ticker, '2024-11-16');
    scope = await stubMarketDataRequestFullDay(scope, ticker, '2024-11-17');
    scope = await stubMarketDataRequestFullDay(scope, ticker, '2024-11-18');
    scope = await stubMarketDataRequestFullDay(scope, ticker, '2024-11-19');
    scope = await stubMarketDataRequestFullDay(scope, ticker, '2024-11-20');
    scope = await stubMarketDataRequestFullDay(scope, ticker, '2024-11-21');
    scope = await stubMarketDataRequestFullDay(scope, ticker, '2024-11-22');
    scope = await stubMarketDataRequestFullDay(scope, ticker, '2024-11-23');
    scope = await stubMarketDataRequestFullDay(scope, ticker, '2024-11-24');
    scope = await stubMarketDataRequestFullDay(scope, ticker, '2024-11-25');
    scope = await stubMarketDataRequestFullDay(scope, ticker, '2024-11-26');
    scope = await stubMarketDataRequestFullDay(scope, ticker, '2024-11-27');
    scope = await stubMarketDataRequestFullDay(scope, ticker, '2024-11-28');
    scope = await stubMarketDataRequestFullDay(scope, ticker, '2024-11-29');
    scope = await stubMarketDataRequestFullDay(scope, ticker, '2024-11-30');

    await service.execute();

    const [bucket] = (await sequelize.query(`
        SELECT * FROM allora_ohlc_buckets 
        WHERE ticker = 'solusd' AND
              start = '2024-11-01'
        ORDER BY start ASC;
    `)) as any;

    expect(bucket[0].state).toBe('available');
    expect(bucket[0].availability).toBe('1');

    expect(scope.isDone()).toBe(true);
  });
});
