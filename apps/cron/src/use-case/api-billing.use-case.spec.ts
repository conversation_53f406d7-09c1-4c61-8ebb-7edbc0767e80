import { Tier } from '@app/core/domain/api/api-tier/tier';
import { TierId } from '@app/core/domain/api/api-tier/tier.id';
import { API_INVOICE_REPOSITORY } from '@app/core/domain/api/invoice/api-invoice.repository';
import { REQUEST_BY_ENDPOINT_REPOSITORY } from '@app/core/domain/api/request-by-endpoint/request-by-endpoint.repository';
import { ApiKey } from '@app/core/domain/api/user/api-key';
import { ApiKeyId } from '@app/core/domain/api/user/api-key.id';
import { ApiUser } from '@app/core/domain/api/user/api-user';
import { ApiUserId } from '@app/core/domain/api/user/api-user.id';
import { API_USER_REPOSITORY } from '@app/core/domain/api/user/api-user.repository';
import { Test, TestingModule } from '@nestjs/testing';
import { v4 } from 'uuid';

import { ApiBillingUseCase } from './api-billing.use-case';

describe('ApiBillingUseCase', () => {
  let service: ApiBillingUseCase;

  const apiUserRepository = {
    getAll: jest.fn(),
    persist: jest.fn(),
  };

  const invoiceRepository = {
    persist: jest.fn(),
    getNextId: jest.fn(),
  };

  const requestByEndpointRepository = {
    resetQuotaByApiUserId: jest.fn(),
  };

  let userWithBilling: ApiUser, userWithoutBilling: ApiUser;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [ApiBillingUseCase],
    })
      .useMocker((token) => {
        if (token === API_USER_REPOSITORY) return apiUserRepository;
        if (token === API_INVOICE_REPOSITORY) return invoiceRepository;
        if (token === REQUEST_BY_ENDPOINT_REPOSITORY)
          return requestByEndpointRepository;
        return {};
      })
      .compile();
    service = module.get<ApiBillingUseCase>(ApiBillingUseCase);

    userWithBilling = new ApiUser({
      id: new ApiUserId(v4()),
      email: '<EMAIL>',
      name: 'test',
      active: true,
      overageWarningAlerts: true,
      _overageRequestCap: 1000,
      apiKeys: [
        new ApiKey({
          id: new ApiKeyId(v4()),
          key: 'test',
          monthlyUsage: 100,
          name: 'name',
          active: true,
          createdAt: new Date(),
        }),
      ],
      tier: new Tier({
        id: new TierId(1),
        name: 'Test',
        monthlyLimit: 100,
        requestsPerSecond: 2,
        overageCostPerRequest: 0.1,
        paymentProductId: null,
        createdAt: new Date(),
      }),
      billingDay: new Date().getDate(),
      createdAt: new Date(),
      password: null,
      refreshToken: null,
      googleId: null,
      paymentAccount: null,
      userId: null,
      oneTimeLoginToken: null,
      isEmailConfirmed: true,
      lastOverageEmailAtPercent: 0,
    });

    userWithoutBilling = new ApiUser({
      id: new ApiUserId(v4()),
      email: '<EMAIL>',
      name: 'test',
      active: true,
      overageWarningAlerts: true,
      _overageRequestCap: 1000,
      apiKeys: [
        new ApiKey({
          id: new ApiKeyId(v4()),
          key: 'test',
          monthlyUsage: 100,
          name: 'name',
          active: true,
          createdAt: new Date(),
        }),
      ],
      tier: new Tier({
        id: new TierId(1),
        name: 'Test',
        monthlyLimit: 100,
        requestsPerSecond: 2,
        overageCostPerRequest: 0.1,
        paymentProductId: null,
        createdAt: new Date(),
      }),
      billingDay: new Date().getDate() + 1,
      createdAt: new Date(),
      password: null,
      refreshToken: null,
      googleId: null,
      paymentAccount: null,
      userId: null,
      oneTimeLoginToken: null,
      isEmailConfirmed: true,
      lastOverageEmailAtPercent: 0,
    });
  });

  afterEach(async () => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('Processes API billing', () => {
    it('only processes users with billing', async () => {
      apiUserRepository.getAll.mockResolvedValue([
        userWithBilling,
        userWithoutBilling,
      ]);
      await service.execute();
      expect(apiUserRepository.persist).toHaveBeenCalledTimes(1);
      expect(apiUserRepository.persist).toHaveBeenCalledWith(userWithBilling);
      expect(invoiceRepository.persist).toHaveBeenCalledTimes(1);
    });
    it('resets monthly usage', async () => {
      apiUserRepository.getAll.mockResolvedValue([userWithBilling]);
      await service.execute();
      expect(apiUserRepository.persist).toHaveBeenCalledTimes(1);
      expect(apiUserRepository.persist).toHaveBeenCalledWith(userWithBilling);
      expect(
        requestByEndpointRepository.resetQuotaByApiUserId,
      ).toHaveBeenCalledTimes(1);
      expect(userWithBilling.monthlyUsage()).toBe(0);
    });
  });
});
