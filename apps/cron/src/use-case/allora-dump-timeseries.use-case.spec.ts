import { MARKET_DATA_COMMAND_REPOSITORY } from '@app/core/domain/allora/market-data/market-data.command.repository';
import { TOPIC_COMMAND_REPOSITORY } from '@app/core/domain/allora/topic/topic.command.repository';
import { Test, TestingModule } from '@nestjs/testing';

import { AlloraDumpTimeseriesUseCase } from './allora-dump-timeseries.use-case';

describe('AlloraDumpTimeseriesUseCase', () => {
  let service: AlloraDumpTimeseriesUseCase;

  const topicCommandRepository = {
    deleteTimeseriesData: jest.fn(),
  };

  const marketDataCommandRepository = {
    deleteTimeseriesData: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [AlloraDumpTimeseriesUseCase],
    })
      .useMocker((token) => {
        if (token === TOPIC_COMMAND_REPOSITORY) return topicCommandRepository;
        if (token === MARKET_DATA_COMMAND_REPOSITORY)
          return marketDataCommandRepository;
        return {};
      })
      .compile();
    service = module.get<AlloraDumpTimeseriesUseCase>(
      AlloraDumpTimeseriesUseCase,
    );
  });

  afterEach(async () => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  it('should delete timeseries data', async () => {
    await service.execute();
    expect(topicCommandRepository.deleteTimeseriesData).toHaveBeenCalledTimes(
      27,
    );
    expect(
      marketDataCommandRepository.deleteTimeseriesData,
    ).toHaveBeenCalledTimes(1);
  });
});
