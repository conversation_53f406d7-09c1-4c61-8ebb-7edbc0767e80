import { TOPIC_COMMAND_REPOSITORY } from '@app/core/domain/allora/topic/topic.command.repository';
import { Test, TestingModule } from '@nestjs/testing';

import { AlloraStudioDataUpdaterUseCase } from './allora-studio-data-updater.use-case';

describe('AlloraStudioDataUpdaterUseCase', () => {
  let service: AlloraStudioDataUpdaterUseCase;

  const topicCommandRepository = {
    updateAllTopicsTotalStake: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [AlloraStudioDataUpdaterUseCase],
    })
      .useMocker((token) => {
        if (token === TOPIC_COMMAND_REPOSITORY) return topicCommandRepository;
        return {};
      })
      .compile();
    service = module.get<AlloraStudioDataUpdaterUseCase>(
      AlloraStudioDataUpdaterUseCase,
    );
  });

  afterEach(async () => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });
});
