import { ModelTokenInference } from '@app/core/domain/allora/inference/inference';
import {
  INFERENCE_COMMAND_REPOSITORY,
  InferenceCommandRepository,
} from '@app/core/domain/allora/inference/inference.command.repository';
import {
  INFERENCE_QUERY_REPOSITORY,
  InferenceQueryRepository,
} from '@app/core/domain/allora/inference/inference.query.repository';
import { ModelQueryRepository } from '@app/core/domain/allora/model/model.query.repository';
import { MODEL_QUERY_REPOSITORY } from '@app/core/domain/allora/model/model.query.repository';
import { Inject, Logger } from '@nestjs/common';
import pLimit from 'p-limit';

const runConcurrently = <T>(
  promises: (Promise<any> | any)[],
  maxConcurrency: number,
): Promise<T[]> => {
  const limit = pLimit(maxConcurrency);
  return Promise.all(promises.map((promise) => limit(() => promise)));
};

export class AlloraModelDataUpdaterUseCase {
  private readonly logger = new Logger(AlloraModelDataUpdaterUseCase.name);
  DEBUG = false;

  constructor(
    @Inject(INFERENCE_COMMAND_REPOSITORY)
    private readonly inferenceCommandRepository: InferenceCommandRepository,
    @Inject(INFERENCE_QUERY_REPOSITORY)
    private readonly inferenceQueryRepository: InferenceQueryRepository,
    @Inject(MODEL_QUERY_REPOSITORY)
    private readonly modelQueryRepository: ModelQueryRepository,
  ) {}

  async execute() {
    if (this.DEBUG) {
      this.logger.log('Starting Allora Model Data Updater Use Case');
    }

    const models = await this.modelQueryRepository.getAllTokenPriceModels();
    if (this.DEBUG) {
      this.logger.log('Models:');
      this.logger.log(models);
    }

    const inferences = await runConcurrently<ModelTokenInference | null>(
      models.map((model) => {
        try {
          return this.inferenceQueryRepository.generateModelTokenInferenceFromModel(
            model,
          );
        } catch (error) {
          this.logger.error(
            `Error generating model token inference for ${model.token.value} ${model.isoDuration.value}: ${error}`,
          );
          return null;
        }
      }),
      3,
    );
    if (this.DEBUG) {
      this.logger.log('Inferences:');
      this.logger.log(inferences);
    }

    for (const inference of inferences) {
      if (!inference) continue;

      this.inferenceCommandRepository.addModelInference(inference);
      this.logger.log(
        `Updated ${inference.token.value} ${inference.isoDuration.value} model inference`,
      );
      // sleep for 0.5 second
      await new Promise((resolve) => setTimeout(resolve, 500));
    }

    if (this.DEBUG) {
      this.logger.log('Allora Model Data Updater Use Case completed');
    }
  }
}
