import {
  getLast<PERSON><PERSON><PERSON>,
  getMonthEnd,
  getMonthStart,
  toISODate,
} from '@app/core/domain/allora/market-data/date';
import {
  MARKET_DATA_BUCKET_COMMAND_REPOSITORY,
  MarketDataBucketCommandRepository,
} from '@app/core/domain/allora/market-data/market-data-bucket.command.repository';
import {
  MARKET_DATA_BUCKET_QUERY_REPOSITORY,
  MarketDataBucketQueryRepository,
} from '@app/core/domain/allora/market-data/market-data-bucket.query.repository';
import { ENABLED_TICKER_SYMBOLS_LOWERCASE } from '@app/core/domain/allora/market-data/ticker';
import { Inject, Logger } from '@nestjs/common';

export class AlloraMarketDataBucketCreatorUseCase {
  private readonly logger = new Logger(
    AlloraMarketDataBucketCreatorUseCase.name,
  );

  private tickers: Readonly<string[]>;

  constructor(
    @Inject(MARKET_DATA_BUCKET_COMMAND_REPOSITORY)
    private readonly marketDataBucketCommandRepository: MarketDataBucketCommandRepository,
    @Inject(MARKET_DATA_BUCKET_QUERY_REPOSITORY)
    private readonly marketDataBucketQueryRepository: MarketDataBucketQueryRepository,
  ) {
    this.tickers = ENABLED_TICKER_SYMBOLS_LOWERCASE;
  }

  async execute() {
    const now = new Date(); // returns UTC date-time

    const promises = this.tickers.map((ticker: string) => {
      return this.createLastMonthBucketIfDoesNotExist(ticker, now);
    });

    await Promise.all(promises);
  }

  async createLastMonthBucketIfDoesNotExist(ticker: string, now: Date) {
    const lastMonth = getLastMonth(now);
    const start = toISODate(getMonthStart(lastMonth));
    const end = toISODate(getMonthEnd(lastMonth));

    const logContext = {
      ticker: ticker,
      periodicity: 'month',
      start: start,
      end: end,
    };
    const logPrefix = `[${ticker}][month][${start}]`;

    const id = await this.marketDataBucketCommandRepository.createNewBucket({
      ticker,
      periodicity: 'month',
      start,
      end,
    });

    if (id) {
      this.logger.log(
        `${logPrefix} created new market data bucket with id ${id}`,
        logContext,
      );
    } else {
      this.logger.log(
        `${logPrefix} market data bucket already exists; skip`,
        logContext,
      );
    }
  }

  setTickers(tickers: Readonly<string[]>) {
    this.tickers = tickers;
  }
}
