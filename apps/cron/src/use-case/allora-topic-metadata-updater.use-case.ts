import {
  Allora<PERSON>hainConfig,
  EAlloraChainId,
} from '@app/core/domain/allora/chain/allora-chain-config';
import { TOPIC_COMMAND_REPOSITORY } from '@app/core/domain/allora/topic/topic.command.repository';
import { ALLORA_TOPIC_QUERY_REPOSITORY } from '@app/core/domain/allora/topic/topic.query.repository';
import { TopicMetadata } from '@app/core/domain/allora/topic/topic-details';
import { SequelizeTopicCommandRepository } from '@app/core/infra/repository/allora/topic/command/sequelize.topic.command.repository';
import { SequelizeTopicQueryRepository } from '@app/core/infra/repository/allora/topic/query/sequelize.topic.query.repository';
import { Inject, Logger } from '@nestjs/common';
import axios from 'axios';
import { ClsService } from 'nestjs-cls';

export class AlloraTopicMetadataUpdaterUseCase {
  private readonly logger = new Logger(AlloraTopicMetadataUpdaterUseCase.name);

  constructor(
    @Inject(TOPIC_COMMAND_REPOSITORY)
    private readonly topicCommandRepository: SequelizeTopicCommandRepository,
    @Inject(ALLORA_TOPIC_QUERY_REPOSITORY)
    private readonly topicQueryRepository: SequelizeTopicQueryRepository,
    private readonly cls: ClsService,
  ) {}

  async execute() {
    // for each supported chain, update topic metadata
    for (const chainId of Object.values(EAlloraChainId)) {
      const alloraChainConfig = new AlloraChainConfig(chainId);
      this.logger.log(`Updating allora topic metdata for ${chainId}...`);
      // validate that the config has a rpc endpoint
      if (!alloraChainConfig.rpcUrl) {
        this.logger.warn(`Skipping ${chainId} because it has no RPC URL`);
        continue;
      }
      // get the current set of topics from DB
      const topics = await this.topicQueryRepository.getAllTopics(
        alloraChainConfig,
      );
      // fetch new topics we don't already have in DB
      const newTopics = await this.getNewTopics(alloraChainConfig, topics);
      if (!newTopics) {
        this.logger.log(`No new topics to fetch for ${chainId}`);
        continue;
      }
      // update the DB with the new topics
      for (const topic of newTopics) {
        await this.topicCommandRepository.updateTopicMetadata(
          topic,
          alloraChainConfig,
        );
      }
      // iterate through all topics to refresh the is_active state
      const allTopics = [...topics, ...newTopics];
      for (const topic of allTopics) {
        const isActive = await this.getTopicIsActiveState(
          topic.id,
          alloraChainConfig,
        );
        if (isActive == null) {
          this.logger.warn(
            `Could not get isActive state for topic ${topic.id}`,
          );
          continue;
        }
        // if the active state is different from the current state in DB, update it
        if (isActive !== topic.isActive) {
          this.logger.log(
            `Updating topic ${topic.id} isActive state to ${isActive}`,
          );
          topic.isActive = isActive;
          await this.topicCommandRepository.updateTopicMetadata(
            topic,
            alloraChainConfig,
          );
        }
        // sleep for 1 second between each topic
        await new Promise((resolve) => setTimeout(resolve, 1000));
      }
    }
  }

  async getNewTopics(
    alloraChainConfig: AlloraChainConfig,
    topics: TopicMetadata[],
  ): Promise<TopicMetadata[] | null> {
    // sort asc by id
    topics = topics.sort((a, b) => a.id - b.id);
    let currentTopicId: number;
    if (topics.length < 1) {
      currentTopicId = 0;
    } else {
      currentTopicId = topics[topics.length - 1].id;
    }
    // get the next topic id from the chain
    const nextTopicId = await this.getNextTopicId(alloraChainConfig);
    if (!nextTopicId) {
      this.logger.error(`Could not get next topic ID from chain.`);
      return null;
    }
    // if there are no topics on chain
    if (nextTopicId <= 1) {
      this.logger.log(`No topics on chain.`);
      return null;
    }

    const latestNetworkTopicId = nextTopicId - 1;
    this.logger.log(
      `Latest topic ID in DB: ${currentTopicId} - Latest topic ID on chain: ${latestNetworkTopicId}`,
    );
    // fetch and store metadata for each missing topic ID
    const newTopics: TopicMetadata[] = [];
    const missingTopicIds = latestNetworkTopicId - currentTopicId;
    if (missingTopicIds > 0) {
      for (let i = currentTopicId + 1; i <= latestNetworkTopicId; i++) {
        this.logger.log(`Fetching and storing metadata for topic ID: ${i}`);
        const topicMetadata = await this.getTopicMetdata(i, alloraChainConfig);
        if (!topicMetadata) {
          this.logger.error(`Could not get metadata for topic ID: ${i}`);
          continue;
        }
        newTopics.push(topicMetadata);
        // sleep for 1 second between each topic
        await new Promise((resolve) => setTimeout(resolve, 1000));
      }
    } else {
      this.logger.log(`No missing topic IDs to fetch metadata for.`);
      return [];
    }
    return newTopics;
  }

  async getTopicIsActiveState(
    topicId: number,
    alloraChainConfig: AlloraChainConfig,
  ): Promise<boolean | null> {
    try {
      if (!alloraChainConfig.rpcUrl) {
        this.logger.warn(`No RPC URL for chain ${alloraChainConfig.chainId}`);
        return null;
      }
      const url = `${alloraChainConfig.rpcUrl}/emissions/v9/is_topic_active/${topicId}`;
      const res = await axios.get(url);
      const isActive = Boolean(res.data.is_active);
      return isActive;
    } catch (error: any) {
      this.logger.error(`Error getting topic is active state: ${error}`);
      return null;
    }
  }

  async getTopicMetdata(
    topicId: number,
    alloraChainConfig: AlloraChainConfig,
  ): Promise<TopicMetadata | null> {
    try {
      if (!alloraChainConfig.rpcUrl) {
        this.logger.warn(`No RPC URL for chain ${alloraChainConfig.chainId}`);
        return null;
      }
      const url = `${alloraChainConfig.rpcUrl}/emissions/v9/topics/${topicId}`;
      const res = await axios.get(url);
      const apiResponse = res.data.topic;
      const topicMetadata: TopicMetadata = {
        id: apiResponse.id,
        creator: apiResponse.creator,
        name: apiResponse.metadata,
        lossMethod: apiResponse.loss_method,
        epochLength: apiResponse.epoch_length,
        groundTruthLag: apiResponse.ground_truth_lag,
        pNorm: apiResponse.p_norm,
        alphaRegret: apiResponse.alpha_regret,
        allowNegative: apiResponse.allow_negative,
        epsilon: apiResponse.epsilon,
        initialRegret: apiResponse.initial_regret,
        workerSubmissionWindow: apiResponse.worker_submission_window,
        meritSortitionAlpha: apiResponse.merit_sortition_alpha,
        activeInfererQuantile: apiResponse.active_inferer_quantile,
        activeForecastorQuantile: apiResponse.active_forecaster_quantile,
        activeReputerQuantile: apiResponse.active_reputer_quantile,
        isActive: true, // new topics are active
      };
      return topicMetadata;
    } catch (e) {
      this.logger.error(`Error getting topic metadata: ${e}`);
      return null;
    }
  }

  async getNextTopicId(
    alloraChainConfig: AlloraChainConfig,
  ): Promise<number | null> {
    try {
      if (!alloraChainConfig.rpcUrl) {
        this.logger.warn(`No RPC URL for chain ${alloraChainConfig.chainId}`);
        return null;
      }
      const url = `${alloraChainConfig.rpcUrl}/emissions/v9/next_topic_id`;
      const res = await axios.get(url);
      const nextTopicId = Number(res.data.next_topic_id);
      return nextTopicId;
    } catch (e) {
      this.logger.error(`Error getting next topic id: ${e}`);
      return null;
    }
  }
}
