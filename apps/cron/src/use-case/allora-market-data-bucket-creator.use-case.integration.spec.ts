import { SequelizeModule } from '@nestjs/sequelize';
import { Test, TestingModule } from '@nestjs/testing';
import MockDate from 'mockdate';
import { Sequelize } from 'sequelize-typescript';

import { reloadFixtures } from '../../../../test-integration/fixtures';
import { sequelizeOptions } from '../../../rest-api/src/config';
import { AlloraMarketDataBucketCreatorUseCase } from './allora-market-data-bucket-creator.use-case';
import { AlloraMarketDataBucketCreatorUseCaseModule } from './allora-market-data-bucket-creator.use-case.module';

describe('AlloraMarketDataBucketCreatorUseCase', () => {
  let service: AlloraMarketDataBucketCreatorUseCase;
  let sequelize: Sequelize;

  beforeEach(async () => {
    sequelize = new Sequelize({
      ...sequelizeOptions,
      dialectOptions: {
        multipleStatements: true,
      },
    });

    const module: TestingModule = await Test.createTestingModule({
      imports: [
        SequelizeModule.forRoot(sequelizeOptions),
        AlloraMarketDataBucketCreatorUseCaseModule,
      ],
    }).compile();

    await module.init();

    service = module.get(AlloraMarketDataBucketCreatorUseCase);

    await reloadFixtures(sequelize, ['allora-ohlc-buckets']);
  });

  afterEach(() => {
    MockDate.reset();
  });

  afterAll(async () => {
    await sequelize.close();
  });

  test('execute for one ticker, creating last month bucket when it does not exist [2025-02]', async () => {
    MockDate.set('2025-03-10T05:11:33Z');

    const ticker = 'arbusd';

    service.setTickers([ticker]);

    await service.execute();

    const [results] = await sequelize.query(
      `SELECT * FROM allora_ohlc_buckets WHERE ticker='${ticker}'`,
    );

    expect(results).toHaveLength(1);

    expect(results[0]).toHaveProperty('ticker', ticker);
    expect(results[0]).toHaveProperty('start', '2025-02-01');
    expect(results[0]).toHaveProperty('end', '2025-02-28');
    expect(results[0]).toHaveProperty('state', 'pending');
    expect(results[0]).toHaveProperty('s3_object_key', null);
  });

  test('execute for one ticker, creating last month bucket when it does not exist [2025-05]', async () => {
    MockDate.set('2025-06-11T05:11:33Z');

    const ticker = 'arbusd';

    service.setTickers([ticker]);

    await service.execute();

    const [results] = await sequelize.query(
      `SELECT * FROM allora_ohlc_buckets WHERE ticker='${ticker}'`,
    );

    expect(results).toHaveLength(1);

    expect(results[0]).toHaveProperty('ticker', ticker);
    expect(results[0]).toHaveProperty('start', '2025-05-01');
    expect(results[0]).toHaveProperty('end', '2025-05-31');
    expect(results[0]).toHaveProperty('state', 'pending');
    expect(results[0]).toHaveProperty('s3_object_key', null);
  });

  test('execute for one ticker, creating last month bucket when it does not exist [2024-12]', async () => {
    MockDate.set('2025-01-11T05:11:33Z');

    const ticker = 'aixbtusdt';

    service.setTickers([ticker]);

    await service.execute();

    const [results] = await sequelize.query(
      `SELECT * FROM allora_ohlc_buckets WHERE ticker='${ticker}'`,
    );

    expect(results).toHaveLength(1);

    expect(results[0]).toHaveProperty('ticker', ticker);
    expect(results[0]).toHaveProperty('start', '2024-12-01');
    expect(results[0]).toHaveProperty('end', '2024-12-31');
    expect(results[0]).toHaveProperty('state', 'pending');
    expect(results[0]).toHaveProperty('s3_object_key', null);
  });

  test('does not create last month bucket when it already exists [2025-02]', async () => {
    MockDate.set('2025-03-10T05:11:33Z');

    const ticker = 'arbusd';

    service.setTickers([ticker]);

    await service.execute();

    const [results] = await sequelize.query(
      `SELECT * FROM allora_ohlc_buckets WHERE ticker='${ticker}'`,
    );

    expect(results).toHaveLength(1);

    expect(results[0]).toHaveProperty('ticker', ticker);
    expect(results[0]).toHaveProperty('start', '2025-02-01');
    expect(results[0]).toHaveProperty('end', '2025-02-28');
    expect(results[0]).toHaveProperty('state', 'pending');
    expect(results[0]).toHaveProperty('s3_object_key', null);
  });

  test('execute for multiple tickers, last month bucket may or may not exist already [2025-02]', async () => {
    MockDate.set('2025-03-10T05:11:33Z');

    const tickers = ['btcusd', 'arbusd', 'methusdt', 'solusd', 'ethusd'];

    service.setTickers(tickers);

    await service.execute();

    const [buckets] = await sequelize.query(
      `SELECT * FROM allora_ohlc_buckets 
         WHERE 
            ticker IN (${tickers.map((i) => `'${i}'`).join(',')}) AND
            start = '2025-02-01' 
        `,
    );

    expect(buckets).toHaveLength(tickers.length);

    for (const bucket of buckets) {
      expect(bucket).toHaveProperty('start', '2025-02-01');
      expect(bucket).toHaveProperty('end', '2025-02-28');
    }
  });
});
