import {
  AlloraChainConfig,
  EAlloraChainId,
} from '@app/core/domain/allora/chain/allora-chain-config';
import {
  TOPIC_COMMAND_REPOSITORY,
  TopicCommandRepository,
} from '@app/core/domain/allora/topic/topic.command.repository';
import { Inject, Logger } from '@nestjs/common';

export class AlloraStudioDataUpdaterUseCase {
  private readonly logger = new Logger(AlloraStudioDataUpdaterUseCase.name);

  constructor(
    @Inject(TOPIC_COMMAND_REPOSITORY)
    private readonly topicCommandRepository: TopicCommandRepository,
  ) {}

  async execute() {
    for (const chainId of Object.values(EAlloraChainId)) {
      const alloraChainConfig = new AlloraChainConfig(chainId);
      if (!alloraChainConfig.networkSuffix) {
        this.logger.warn(
          `Skipping ${chainId} because it has no network suffix`,
        );
        continue;
      }
      // get the current epoch timestamp in seconds
      const timestamp = Math.floor(new Date().getTime() / 1000);
      // update the total stake timeseries for all topics
      await this.topicCommandRepository.updateAllTopicsTotalStake(
        timestamp,
        alloraChainConfig,
      );
      this.logger.log(`Updated total stake timeseries for ${chainId}`);
      // sleep for 1 second
      await new Promise((resolve) => setTimeout(resolve, 1000));
    }
  }
}
