import { MARKET_DATA_COMMAND_REPOSITORY } from '@app/core/domain/allora/market-data/market-data.command.repository';
import { TOPIC_COMMAND_REPOSITORY } from '@app/core/domain/allora/topic/topic.command.repository';
import { SequelizeMarketDataCommandRepository } from '@app/core/infra/repository/allora/market-data/command/sequelize.market-data.command.repository';
import { SequelizeTopicCommandRepository } from '@app/core/infra/repository/allora/topic/command/sequelize.topic.command.repository';
import { Module } from '@nestjs/common';

import { AlloraDumpTimeseriesUseCase } from './allora-dump-timeseries.use-case';

@Module({
  imports: [],
  providers: [
    AlloraDumpTimeseriesUseCase,
    {
      useClass: SequelizeTopicCommandRepository,
      provide: TOPIC_COMMAND_REPOSITORY,
    },
    {
      useClass: SequelizeMarketDataCommandRepository,
      provide: MARKET_DATA_COMMAND_REPOSITORY,
    },
  ],
  exports: [AlloraDumpTimeseriesUseCase],
})
export class AlloraDumpTimeseriesUseCasModule {}
