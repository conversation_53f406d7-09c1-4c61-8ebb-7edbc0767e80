import { INFERENCE_COMMAND_REPOSITORY } from '@app/core/domain/allora/inference/inference.command.repository';
import { INFERENCE_QUERY_REPOSITORY } from '@app/core/domain/allora/inference/inference.query.repository';
import { MODEL_QUERY_REPOSITORY } from '@app/core/domain/allora/model/model.query.repository';
import { ApiInferenceQueryRepository } from '@app/core/infra/repository/allora/inference/api.inference.query.repository';
import { SequelizeInferenceCommandRepository } from '@app/core/infra/repository/allora/inference/sequelize.inference.command.repository';
import { SequelizeModelQueryRepository } from '@app/core/infra/repository/allora/model/sequelize.model.query.repository';
import { Module } from '@nestjs/common';

import { AlloraModelDataUpdaterUseCase } from './allora-model-data-updater.use-case';

@Module({
  imports: [],
  providers: [
    AlloraModelDataUpdaterUseCase,
    {
      useClass: SequelizeInferenceCommandRepository,
      provide: INFERENCE_COMMAND_REPOSITORY,
    },
    {
      useClass: ApiInferenceQueryRepository,
      provide: INFERENCE_QUERY_REPOSITORY,
    },
    {
      useClass: SequelizeModelQueryRepository,
      provide: MODEL_QUERY_REPOSITORY,
    },
  ],
  exports: [AlloraModelDataUpdaterUseCase],
})
export class AlloraModelDataUpdaterUseCaseModule {}
