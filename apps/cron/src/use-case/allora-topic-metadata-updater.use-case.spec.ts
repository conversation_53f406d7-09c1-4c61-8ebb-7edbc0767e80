import { TOPIC_COMMAND_REPOSITORY } from '@app/core/domain/allora/topic/topic.command.repository';
import { ALLORA_TOPIC_QUERY_REPOSITORY } from '@app/core/domain/allora/topic/topic.query.repository';
import { Test, TestingModule } from '@nestjs/testing';

import { AlloraTopicMetadataUpdaterUseCase } from './allora-topic-metadata-updater.use-case';

describe('AlloraTopicMetdataUpdaterUseCase', () => {
  let service: AlloraTopicMetadataUpdaterUseCase;

  const topicQueryRepository = {
    getAllTopics: jest.fn(),
  };

  const topicCommandRepository = {
    updateTopicMetadata: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [AlloraTopicMetadataUpdaterUseCase],
    })
      .useMocker((token) => {
        if (token === TOPIC_COMMAND_REPOSITORY) return topicCommandRepository;
        if (token === ALLORA_TOPIC_QUERY_REPOSITORY)
          return topicQueryRepository;
        return {};
      })
      .compile();
    service = module.get<AlloraTopicMetadataUpdaterUseCase>(
      AlloraTopicMetadataUpdaterUseCase,
    );

    service.getNextTopicId = jest.fn();
    service.getTopicMetdata = jest.fn();
  });

  afterEach(async () => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  it('should fetch and store metadata with 0 existing topics', async () => {
    // setup mocks
    topicQueryRepository.getAllTopics.mockResolvedValue([]);
    service.getTopicIsActiveState = jest.fn().mockReturnValue(true);
    service.getNextTopicId = jest.fn().mockReturnValue(2);
    service.getTopicMetdata = jest.fn().mockReturnValue({
      id: 1,
      creator: 'user123',
      name: 'Market Analysis',
      lossMethod: 'mse',
      epochLength: '30',
      groundTruthLag: '5',
      pNorm: '2',
      alphaRegret: '0.1',
      allowNegative: true,
      epsilon: '0.01',
      initialRegret: 'Initial regret data for Market Analysis',
      workerSubmissionWindow: '60',
      meritSortitionAlpha: '0.5',
      activeInfererQuantile: '0.3',
      activeForecasterQuantile: '0.2',
      activeReputerQuantile: '0.4',
      isActive: true,
    });
    await service.execute();

    expect(topicQueryRepository.getAllTopics).toBeCalledTimes(2);
    expect(service.getNextTopicId).toBeCalledTimes(2);
    expect(service.getTopicMetdata).toBeCalledTimes(2);
    expect(topicCommandRepository.updateTopicMetadata).toBeCalledTimes(2);
  });

  it('should not fetch more topics if we are up to date', async () => {
    topicQueryRepository.getAllTopics.mockResolvedValue([
      {
        id: 16,
        creator: 'user123',
        name: 'Market Analysis',
        lossMethod: 'mse',
        epochLength: '30',
        groundTruthLag: '5',
        pNorm: '2',
        alphaRegret: '0.1',
        allowNegative: true,
        epsilon: '0.01',
        initialRegret: 'Initial regret data for Market Analysis',
        workerSubmissionWindow: '60',
        meritSortitionAlpha: '0.5',
        activeInfererQuantile: '0.3',
        activeForecasterQuantile: '0.2',
        activeReputerQuantile: '0.4',
        isActive: true,
        createdAt: new Date('2024-09-30T12:34:56Z'),
      },
    ]);
    service.getNextTopicId = jest.fn().mockReturnValue(17);
    service.getTopicIsActiveState = jest.fn().mockResolvedValue(true);
    await service.execute();
    expect(topicQueryRepository.getAllTopics).toBeCalledTimes(2);
    expect(service.getNextTopicId).toBeCalledTimes(2);
    expect(service.getTopicMetdata).toBeCalledTimes(0);
    expect(topicCommandRepository.updateTopicMetadata).toBeCalledTimes(0);
  });
});
