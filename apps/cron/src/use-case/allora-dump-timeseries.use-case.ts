import { MARKET_DATA_COMMAND_REPOSITORY } from '@app/core/domain/allora/market-data/market-data.command.repository';
import { TOPIC_COMMAND_REPOSITORY } from '@app/core/domain/allora/topic/topic.command.repository';
import { SequelizeMarketDataCommandRepository } from '@app/core/infra/repository/allora/market-data/command/sequelize.market-data.command.repository';
import { SequelizeTopicCommandRepository } from '@app/core/infra/repository/allora/topic/command/sequelize.topic.command.repository';
import { Inject, Logger } from '@nestjs/common';

const ALLORA_TIMESERIES_TABLES = [
  // testnet
  'allora_topic_worker_losses_allora_testnet_1',
  'allora_topic_reputer_losses_allora_testnet_1',
  'allora_topic_worker_count_allora_testnet_1',
  'allora_topic_total_staked_allora_testnet_1',
  'allora_topic_emissions_allora_testnet_1',
  'allora_wallet_balance_allora_testnet_1',
  'allora_network_inferences_allora_testnet_1',
  'allora_network_ground_truth_allora_testnet_1',
  'allora_rewards_allora_testnet_1',
  // devnet
  'allora_topic_worker_losses_allora_devnet_1',
  'allora_topic_reputer_losses_allora_devnet_1',
  'allora_topic_worker_count_allora_devnet_1',
  'allora_topic_total_staked_allora_devnet_1',
  'allora_topic_emissions_allora_devnet_1',
  'allora_wallet_balance_allora_devnet_1',
  'allora_network_inferences_allora_devnet_1',
  'allora_network_ground_truth_allora_devnet_1',
  'allora_rewards_allora_devnet_1',
  // mainnet
  'allora_topic_worker_losses_allora_mainnet_1',
  'allora_topic_reputer_losses_allora_mainnet_1',
  'allora_topic_worker_count_allora_mainnet_1',
  'allora_topic_total_staked_allora_mainnet_1',
  'allora_topic_emissions_allora_mainnet_1',
  'allora_wallet_balance_allora_mainnet_1',
  'allora_network_inferences_allora_mainnet_1',
  'allora_network_ground_truth_allora_mainnet_1',
  'allora_rewards_allora_mainnet_1',
];

const ALLORA_OHLC_TIMESERIES_TABLES = ['allora_ohlc_timeseries'];

const SEVEN_DAYS_AGO = 7 * 24 * 60 * 60;
const SIX_MONTHS_AGO = new Date(Date.now() - 6 * 30 * 24 * 60 * 60 * 1000);

export class AlloraDumpTimeseriesUseCase {
  private readonly logger = new Logger(AlloraDumpTimeseriesUseCase.name);

  constructor(
    @Inject(TOPIC_COMMAND_REPOSITORY)
    private readonly topicCommandRepository: SequelizeTopicCommandRepository,
    @Inject(MARKET_DATA_COMMAND_REPOSITORY)
    private readonly marketDataCommandRepository: SequelizeMarketDataCommandRepository,
  ) {}

  async execute() {
    this.logger.log(`Starting to dump timeseries data...`);

    // ALLORA STUDIO TIMESERIES
    for (const tableName of ALLORA_TIMESERIES_TABLES) {
      this.logger.log(`Deleting data older than 7 days from ${tableName}...`);
      const sevenDaysAgoTimestamp =
        Math.floor(Date.now() / 1000) - SEVEN_DAYS_AGO;
      // delete data older than 7 days
      try {
        await this.topicCommandRepository.deleteTimeseriesData(
          tableName,
          sevenDaysAgoTimestamp,
        );
      } catch (error) {
        this.logger.warn(`Error deleting data from ${tableName}: ${error}`);
      }
    }

    // ALLORA OHLC TIMESERIES
    for (const tableName of ALLORA_OHLC_TIMESERIES_TABLES) {
      this.logger.log(`Deleting data older than 30 days from ${tableName}...`);
      try {
        await this.marketDataCommandRepository.deleteTimeseriesData(
          SIX_MONTHS_AGO,
        );
      } catch (error) {
        this.logger.warn(`Error deleting data from ${tableName}: ${error}`);
      }
    }
  }
}
