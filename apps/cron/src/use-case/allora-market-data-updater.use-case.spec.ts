import { MarketData } from '@app/core/domain/allora/market-data/market-data';
import { MARKET_DATA_COMMAND_REPOSITORY } from '@app/core/domain/allora/market-data/market-data.command.repository';
import { MARKET_DATA_QUERY_REPOSITORY } from '@app/core/domain/allora/market-data/market-data.query.repository';
import { MARKET_DATA_HISTORY_COMMAND_REPOSITORY } from '@app/core/domain/allora/market-data/market-data-history.command.repository';
import { Test, TestingModule } from '@nestjs/testing';

import { AlloraMarketDataUpdaterUseCase } from './allora-market-data-updater.use-case';

describe('AlloraMarketDataUpdaterUseCase', () => {
  let service: AlloraMarketDataUpdaterUseCase;

  const marketDataCommandRepository = {
    upsertMarketDataBatch: jest.fn(),
  };

  const marketDataQueryRepository = {
    getLatestCandle: jest.fn().mockResolvedValue(null),
  };

  const marketDataHistoryCommandRepository = {
    upsertBatch: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [AlloraMarketDataUpdaterUseCase],
    })
      .useMocker((token) => {
        if (token === MARKET_DATA_COMMAND_REPOSITORY)
          return marketDataCommandRepository;
        if (token === MARKET_DATA_QUERY_REPOSITORY)
          return marketDataQueryRepository;
        if (token === MARKET_DATA_HISTORY_COMMAND_REPOSITORY)
          return marketDataHistoryCommandRepository;
        return {};
      })
      .compile();

    service = module.get<AlloraMarketDataUpdaterUseCase>(
      AlloraMarketDataUpdaterUseCase,
    );

    service.fetchCryptoOhlcData = jest.fn().mockResolvedValue([
      {
        ticker: 'btcusd',
        baseCurrency: 'btc',
        quoteCurrency: 'usd',
        priceData: [
          {
            date: '2024-10-27T00:00:00+00:00',
            open: 67052.67596059141,
            high: 68261.84991397605,
            low: 66837.68477013022,
            close: 67950.71211947028,
            volume: 4134.411258879999,
            volumeNotional: 280936189.2356515,
            tradesDone: 215335.0,
          },
          {
            date: '2024-10-28T00:00:00+00:00',
            open: 67945.40699637755,
            high: 67994.96634803887,
            low: 67945.40699637755,
            close: 67978.89638652017,
            volume: 16.42651459000001,
            volumeNotional: 1116656.3333052725,
            tradesDone: 493.0,
          },
        ],
      },
    ]);
  });

  describe('convertMarketData', () => {
    const baseDate = new Date('2024-01-01T00:00:00Z');
    const createMarketData = (
      ticker: string,
      price: number,
      date = baseDate,
    ): MarketData => ({
      ticker,
      date: date.toISOString(),
      open: price,
      high: price * 1.1,
      low: price * 0.9,
      close: price * 1.05,
      volume: 100,
      volumeNotional: 100 * price,
      exchangeCode: 'TEST',
      tradesDone: 10,
    });

    it('should correctly convert ETH pairs to USD', () => {
      const vaderEthPrice = 0.1;
      const ethUsdPrice = 2000;
      const marketData = [
        createMarketData('vadereth', vaderEthPrice),
        createMarketData('ethusd', ethUsdPrice),
      ];

      const result = service.convertMarketData(marketData);
      const convertedVader = result.find((d) => d.ticker === 'vaderusdt');
      expect(convertedVader).toBeDefined();
      expect(convertedVader?.open).toBe(vaderEthPrice * ethUsdPrice);
      expect(convertedVader?.high).toBe(vaderEthPrice * 1.1 * ethUsdPrice);
      expect(convertedVader?.low).toBe(vaderEthPrice * 0.9 * ethUsdPrice);
      expect(convertedVader?.close).toBe(vaderEthPrice * 1.05 * ethUsdPrice);
      expect(convertedVader?.volume).toBe(100);
      expect(convertedVader?.volumeNotional).toBe(10 * ethUsdPrice);
    });

    it('should handle multiple conversion pairs at the same time', () => {
      const vaderEthPrice = 0.1;
      const gameEthPrice = 0.2;
      const ethUsdPrice = 2000;
      const marketData = [
        createMarketData('vadereth', vaderEthPrice),
        createMarketData('gameveth', gameEthPrice),
        createMarketData('ethusd', ethUsdPrice),
      ];

      const result = service.convertMarketData(marketData);
      const convertedVader = result.find((d) => d.ticker === 'vaderusdt');
      const convertedGame = result.find((d) => d.ticker === 'gamevusdt');
      expect(convertedVader?.open).toBe(vaderEthPrice * ethUsdPrice);
      expect(convertedGame?.open).toBe(gameEthPrice * ethUsdPrice);
    });

    it('should handle missing conversion rate data', () => {
      const marketData = [
        createMarketData('vadereth', 0.1),
        // Missing ETHUSDT data
      ];
      const result = service.convertMarketData(marketData);
      expect(result).toEqual(marketData);
    });

    it('should handle null volume and volumeNotional', () => {
      const marketData = [
        {
          ...createMarketData('vadereth', 0.1),
          volume: null,
          volumeNotional: null,
        },
        createMarketData('ethusd', 2000),
      ];
      const result = service.convertMarketData(marketData);
      const convertedVader = result.find((d) => d.ticker === 'vaderusdt');
      expect(convertedVader?.volume).toBeNull();
      expect(convertedVader?.volumeNotional).toBeNull();
    });

    it('should handle multiple timestamps', () => {
      const vaderEthPrice = 0.1;
      const ethUsdPrice = 2000;

      const date1 = new Date('2024-01-01T00:00:00Z');
      const date2 = new Date('2024-01-01T00:01:00Z');
      const marketData = [
        createMarketData('vadereth', vaderEthPrice, date1),
        createMarketData('ethusd', ethUsdPrice, date1),
        createMarketData('vadereth', vaderEthPrice, date2),
        createMarketData('ethusd', ethUsdPrice, date2),
      ];

      const result = service.convertMarketData(marketData);
      const convertedVader1 = result.find(
        (d) => d.ticker === 'vaderusdt' && d.date === date1.toISOString(),
      );
      const convertedVader2 = result.find(
        (d) => d.ticker === 'vaderusdt' && d.date === date2.toISOString(),
      );

      expect(convertedVader1?.open).toBe(vaderEthPrice * ethUsdPrice);
      expect(convertedVader2?.open).toBe(vaderEthPrice * ethUsdPrice);
    });

    it('should preserve original data for non-conversion pairs', () => {
      const marketData = [
        createMarketData('btcusd', 40000),
        createMarketData('vadereth', 0.1),
        createMarketData('ethusd', 2000),
      ];
      const result = service.convertMarketData(marketData);
      const btcData = result.find((d) => d.ticker === 'btcusd');
      expect(btcData).toEqual(marketData[0]);
    });
  });

  describe('execute', () => {
    it('should be defined', () => {
      expect(service).toBeDefined();
    });

    it('should execute market data update', async () => {
      const now = new Date();
      const oneDayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000);
      await service.execute(oneDayAgo, now);
    });
  });
});
