import { AppLoggerModule } from '@app/app-logger/logger.module';
import { UnitOfWorkModule } from '@app/core/app/unit-of-work/unit-of-work.module';
import { MetricsLoggerModule } from '@app/core/domain/metrics-logger/metrics-logger.module';
import { PrometheusMetricsModule } from '@app/core/infra/prometheus/prometheus-metrics.module';
import { SlackBotModule } from '@app/core/infra/slack/slackbot.module';
import { DatadogClientModule } from '@app/datadog-client';
import { Module } from '@nestjs/common';
import { ScheduleModule } from '@nestjs/schedule';
import { SequelizeModule } from '@nestjs/sequelize';
import { ClsModule } from 'nestjs-cls';

import { sequelizeOptions } from '../../rest-api/src/config';
import { CronController } from './cron.controller';
import { AlloraDumpTimeseriesUseCasModule } from './use-case/allora-dump-timeseries.module';
import { AlloraGroundTruthUpdaterUseCaseModule } from './use-case/allora-ground-truth-updater.use-case.module';
import { AlloraMarketDataBucketBackfillerUseCaseModule } from './use-case/allora-market-data-bucket-backfiller.use-case.module';
import { AlloraMarketDataBucketCreatorUseCaseModule } from './use-case/allora-market-data-bucket-creator.use-case.module';
import { AlloraMarketDataBucketUploaderUseCaseModule } from './use-case/allora-market-data-bucket-uploader.use-case.module';
import { AlloraMarketDataUpdaterUseCaseModule } from './use-case/allora-market-data-updater.use-case.module';
import { AlloraModelDataUpdaterUseCaseModule } from './use-case/allora-model-data-updater.use-case.module';
import { AlloraStudioDataUpdaterUseCaseModule } from './use-case/allora-studio-data-updater.use-case.module';
import { AlloraTopicMetadataUpdaterUseCaseModule } from './use-case/allora-topic-metadata-updater.use-case.module';
import { ApiBillingUseCaseModule } from './use-case/api-billing.use-case.module';
import { RoboNetDailyPointsUseCaseModule } from './use-case/robonet-daily-points.use-case.module';
import { RoboNetVaultReporterUseCaseModule } from './use-case/robonet-vault-reporter.use-case.module';
import { RoboNetVaultStatsUseCaseModule } from './use-case/robonet-vault-stats.use-case.module';
import { UpdateExchangeRatesUseCaseModule } from './use-case/update-exchange-rates.use-case.module';

@Module({
  imports: [
    SequelizeModule.forRoot(sequelizeOptions),
    ScheduleModule.forRoot(),
    UpdateExchangeRatesUseCaseModule,
    ApiBillingUseCaseModule,
    RoboNetVaultStatsUseCaseModule,
    DatadogClientModule,
    AppLoggerModule,
    MetricsLoggerModule,
    UnitOfWorkModule,
    RoboNetVaultReporterUseCaseModule,
    RoboNetDailyPointsUseCaseModule,
    AlloraDumpTimeseriesUseCasModule,
    AlloraTopicMetadataUpdaterUseCaseModule,
    AlloraMarketDataUpdaterUseCaseModule,
    AlloraMarketDataBucketUploaderUseCaseModule,
    AlloraMarketDataBucketCreatorUseCaseModule,
    AlloraMarketDataBucketBackfillerUseCaseModule,
    AlloraStudioDataUpdaterUseCaseModule,
    AlloraModelDataUpdaterUseCaseModule,
    AlloraGroundTruthUpdaterUseCaseModule,
    SlackBotModule,
    PrometheusMetricsModule.forRoot({ appName: 'cron' }),
    ClsModule.forRoot({
      global: true,
      middleware: {
        mount: true,
        generateId: true,
      },
    }),
  ],
  providers: [],
  controllers: [CronController],
})
export class CronModule {}
