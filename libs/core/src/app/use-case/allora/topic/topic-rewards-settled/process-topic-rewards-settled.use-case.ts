import { UnitOfWork } from '@app/core/app/unit-of-work/unit-of-work.decorator';
import { AlloraForgePointTransaction } from '@app/core/domain/allora/allora-forge/allora-forge';
import { ALLORA_FORGE_COMMAND_REPOSITORY } from '@app/core/domain/allora/allora-forge/allora-forge.command.repository';
import { ALLORA_FORGE_QUERY_REPOSITORY } from '@app/core/domain/allora/allora-forge/allora-forge.query.repository';
import { ALLORA_USER_COMMAND_REPOSITORY } from '@app/core/domain/allora/allora-user/allora-user.command.repository';
import { Campaign } from '@app/core/domain/allora/campaign/campaign';
import { CAMPAIGN_QUERY_REPOSITORY } from '@app/core/domain/allora/campaign/campaign.query.repository';
import {
  AlloraChainConfig,
  EAlloraChainId,
} from '@app/core/domain/allora/chain/allora-chain-config';
import { ALLORA_LEADERBOARD_COMMAND_REPOSITORY } from '@app/core/domain/allora/leaderboard/leaderboard.command.repository';
import { PointTransaction } from '@app/core/domain/allora/point/point-transaction';
import { POINT_TRANSACTION_COMMAND_REPOSITORY } from '@app/core/domain/allora/point/point-transaction.command.repository';
import { TOPIC_COMMAND_REPOSITORY } from '@app/core/domain/allora/topic/topic.command.repository';
import { ETopicStatsType } from '@app/core/domain/allora/topic/topic-details';
import {
  METRICS_LOGGER,
  MetricsLogger,
} from '@app/core/domain/metrics-logger/metrics-logger';
import { EStatName } from '@app/core/domain/metrics-logger/stat-name.type';
import { AlloraEntityLogger } from '@app/core/infra/logger/allora-entity.logger';
import { SequelizeAlloraForgeCommandRepository } from '@app/core/infra/repository/allora/allora-forge/command/sequelize.allora-forge.command.repository';
import { SequelizeAlloraForgeQueryRepository } from '@app/core/infra/repository/allora/allora-forge/query/sequelize.allora-forge.query.repository';
import { SequelizeAlloraUserCommandRepository } from '@app/core/infra/repository/allora/allora-user/command/sequelize.allora-user.command.repository';
import { SequelizeCampaignQueryRepository } from '@app/core/infra/repository/allora/campaign/query/sequelize.campaign.query.repository';
import { SequelizeLeaderboardCommandRepository } from '@app/core/infra/repository/allora/leaderboard/command/sequilize.leaderboard.command.repository';
import { SequelizePointTransactionCommandRepository } from '@app/core/infra/repository/allora/point/command/sequelize.point-transaction.command.repository';
import { SequelizeTopicCommandRepository } from '@app/core/infra/repository/allora/topic/command/sequelize.topic.command.repository';
import { Inject } from '@nestjs/common';
import { CommandHandler, ICommandHandler } from '@nestjs/cqrs';
import { BigNumber as BN, ethers } from 'ethers';

import { ProcessTopicRewardsSettledsCommand } from './process-topic-rewards-settled.command';

const POINTS_MULTIPLIER_FACTOR = 10;

export const enum EActorTypes {
  INFERER = 'inferer',
  REPUTER = 'reputer',
  FORECASTOR = 'forecaster',
}

@CommandHandler(ProcessTopicRewardsSettledsCommand)
export class ProcessTopicRewardsSettledUseCase
  implements ICommandHandler<ProcessTopicRewardsSettledsCommand>
{
  private readonly logger = new AlloraEntityLogger(
    ProcessTopicRewardsSettledsCommand.name,
  );

  constructor(
    @Inject(METRICS_LOGGER)
    private readonly metricsLogger: MetricsLogger,
    @Inject(TOPIC_COMMAND_REPOSITORY)
    private readonly topicCommandRepository: SequelizeTopicCommandRepository,
    @Inject(ALLORA_USER_COMMAND_REPOSITORY)
    private readonly alloraUserCommandRepository: SequelizeAlloraUserCommandRepository,
    @Inject(CAMPAIGN_QUERY_REPOSITORY)
    private readonly alloraCampaignQueryRepository: SequelizeCampaignQueryRepository,
    @Inject(POINT_TRANSACTION_COMMAND_REPOSITORY)
    private readonly alloraPointTransactionCommandRepository: SequelizePointTransactionCommandRepository,
    @Inject(ALLORA_LEADERBOARD_COMMAND_REPOSITORY)
    private readonly alloraLeaderboardCommandRepository: SequelizeLeaderboardCommandRepository,
    @Inject(ALLORA_FORGE_QUERY_REPOSITORY)
    private readonly alloraForgeQueryRepository: SequelizeAlloraForgeQueryRepository,
    @Inject(ALLORA_FORGE_COMMAND_REPOSITORY)
    private readonly alloraForgeCommandRepository: SequelizeAlloraForgeCommandRepository,
  ) {}

  @UnitOfWork()
  async execute(command: ProcessTopicRewardsSettledsCommand): Promise<void> {
    const event = command.message.payload.data;
    const metadata = command.message.payload.metadata;
    const alloraChainConfig = new AlloraChainConfig(
      metadata.block_metadata.chain_id as EAlloraChainId,
    );

    try {
      const blockNumber = Number(metadata.block_metadata.height);
      const isoTimestamp = metadata.block_metadata.time;
      const unixTimestamp = Math.floor(new Date(isoTimestamp).getTime() / 1000);
      const topicId = Number(event.topic_id);
      const data: { address: string; value: number }[] = [];
      const actor = event.actor_type ? event.actor_type.toLowerCase() : null;
      if (!actor) {
        this.logger.warn(
          `Found unknown actor type in event: ${JSON.stringify(
            command.message,
          )}`,
        );
        return;
      }

      let currentActorType: string;
      if (actor.includes(EActorTypes.INFERER)) {
        currentActorType = EActorTypes.INFERER.toUpperCase();
      } else if (actor.includes(EActorTypes.REPUTER)) {
        currentActorType = EActorTypes.REPUTER.toUpperCase();
      } else if (actor.includes(EActorTypes.FORECASTOR)) {
        currentActorType = EActorTypes.FORECASTOR.toUpperCase();
      } else {
        throw new Error(`Unknown actor type: ${actor}`);
      }

      // check if event is rewarding a batch of addresses or a single address
      if (event.addresses.length === 1) {
        const address = event.addresses[0];
        const addressReward = String(event.rewards);
        const reward = this.parseUalloAmount(addressReward);
        data.push({
          address: address,
          value: reward,
        });
      } else {
        for (let i = 0; i < event.addresses.length; i++) {
          const address = event.addresses[i];
          const addressReward = event.rewards[i];
          const reward = this.parseUalloAmount(addressReward);
          data.push({
            address: address,
            value: reward,
          });
        }
      }
      // update reward timeseries for all actor types
      await this.topicCommandRepository.insertTopicRewards(
        topicId,
        currentActorType,
        data,
        blockNumber,
        unixTimestamp,
        alloraChainConfig,
      );
      // update stats based on actor type
      if (actor.includes(EActorTypes.INFERER)) {
        // update worker stats
        await this.topicCommandRepository.incrementTopicWorkerStats(
          topicId,
          ETopicStatsType.TOTAL_EARNED,
          data,
          alloraChainConfig,
        );
        // distribute points for workers according to the allora points program (only for testnet-1)
        if (alloraChainConfig.chainId === EAlloraChainId.ALLORA_TESTNET_1) {
          // process forge point transactions
          const forgePointTransactions =
            await this.createForgePointTransactions(topicId, blockNumber, data);
          if (forgePointTransactions.length > 0) {
            // save forge point transactions
            await this.alloraForgeCommandRepository.insertPointTransactionBatch(
              forgePointTransactions,
            );
            // increment points to leaderboard
            await this.alloraForgeCommandRepository.incrementPointsBatch(
              forgePointTransactions,
            );
          }
        }
      } else if (actor.includes(EActorTypes.REPUTER)) {
        // update reputer stats
        await this.topicCommandRepository.incrementTopicReputerStats(
          topicId,
          ETopicStatsType.TOTAL_EARNED,
          data,
          alloraChainConfig,
        );
      } else {
        this.logger.log(`Found unknown actor type: ${actor}. Skipping..`);
        return;
      }
      // update topic emissions timeseries
      const totalRewards = data.reduce((acc, curr) => acc + curr.value, 0);
      await this.topicCommandRepository.insertTopicEmissions(
        topicId,
        unixTimestamp,
        totalRewards,
        alloraChainConfig,
      );
      this.metricsLogger.success(
        EStatName.ALLORA,
        'ProcessTopicRewardsSettledUseCase',
      );
    } catch (error) {
      this.logger.failure(command.message.toString(), error);
      this.metricsLogger.failure(
        EStatName.ALLORA,
        'ProcessTopicRewardsSettledUseCase',
      );
      throw error;
    }
  }

  async createForgePointTransactions(
    topicId: number,
    blockNumber: number,
    data: { address: string; value: number }[],
  ): Promise<AlloraForgePointTransaction[]> {
    const pointTransactions: AlloraForgePointTransaction[] = [];
    // get the current forge season
    const currentSeason =
      await this.alloraForgeQueryRepository.getCurrentSeason();
    if (!currentSeason) {
      return [] as AlloraForgePointTransaction[];
    }
    // get the competition using the topic id
    const competition =
      await this.alloraForgeQueryRepository.getCompetitionByTopicId(topicId);
    if (!competition) {
      return [] as AlloraForgePointTransaction[];
    }

    // validate the start and end dates of the competition
    if (
      new Date(competition.startDate) > new Date() ||
      new Date(competition.endDate) < new Date()
    ) {
      return [] as AlloraForgePointTransaction[];
    }

    // iterate through each worker and create a point transaction
    for (const worker of data) {
      const userAddress = worker.address.toLowerCase();
      const reward = worker.value;
      // validate if the user is active in the competition
      const userCompetition =
        await this.alloraForgeQueryRepository.getUserCompetition(
          userAddress,
          competition.id,
        );
      if (!userCompetition || !userCompetition.approvedRegistration) {
        continue;
      }
      // we use the same reward multiplier as in the points program
      const rewardWithMultiplier = reward * POINTS_MULTIPLIER_FACTOR;
      const finalReward = Math.round(rewardWithMultiplier * 1000) / 1000;
      // create the point transaction
      pointTransactions.push(
        new AlloraForgePointTransaction({
          userId: userCompetition.alloraUserId,
          points: finalReward,
          competitionId: competition.id,
          blockNumber,
        }),
      );
    }
    return pointTransactions;
  }

  async createPointTransactions(
    topicId: number,
    blockNumber: number,
    data: { address: string; value: number }[],
  ): Promise<PointTransaction[]> {
    const pointTransactions: PointTransaction[] = [];
    // get the campaign
    const campaigns =
      await this.alloraCampaignQueryRepository.getCampaignsByTopicId(
        Number(topicId),
      );
    for (const campaign of campaigns) {
      const validCampaign = await this.validateCampaign(campaign);
      if (!validCampaign) {
        this.logger.warn(
          `Campaign ${campaign.slug} is not valid for rewards event for topicId: ${topicId}`,
        );
        continue;
      }
      // iterate through each worker and create a point transaction
      for (const worker of data) {
        const userAddress = worker.address;
        const reward = worker.value;
        const rewardWithMultiplier = reward * validCampaign.multiplier;
        const rewardWithMultiplierAndFactor =
          rewardWithMultiplier * POINTS_MULTIPLIER_FACTOR;
        const finalReward =
          Math.round(rewardWithMultiplierAndFactor * 1000) / 1000;
        const user =
          await this.alloraUserCommandRepository.getOrCreateAlloraUserIfDne(
            userAddress,
          );
        pointTransactions.push(
          new PointTransaction({
            userId: user.id,
            points: finalReward,
            campaignSlug: validCampaign.slug,
            topicId,
            blockNumber,
          }),
        );
      }
    }
    return pointTransactions;
  }

  async validateCampaign(campaign: Campaign): Promise<Campaign | null> {
    const today = new Date();
    if (
      new Date(campaign.startDate) > today ||
      new Date(campaign.endDate) < today
    ) {
      return null;
    }
    return campaign;
  }

  parseUalloAmount(amount: string): number {
    let rewardBN;
    if (amount.includes('.')) {
      rewardBN = BN.from(amount.split('.')[0]); // only use the left of the decimal point
    } else {
      rewardBN = BN.from(amount);
    }
    const reward = Number(ethers.utils.formatUnits(rewardBN, 18));
    return reward;
  }
}
