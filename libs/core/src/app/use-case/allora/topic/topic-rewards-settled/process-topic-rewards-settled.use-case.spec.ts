import { MockUnitOfWork } from '@app/core/app/unit-of-work/mock.unit-of-work';
import { UNIT_OF_WORK } from '@app/core/app/unit-of-work/unit-of-work';
import { AlloraForgePointTransaction } from '@app/core/domain/allora/allora-forge/allora-forge';
import { ALLORA_FORGE_COMMAND_REPOSITORY } from '@app/core/domain/allora/allora-forge/allora-forge.command.repository';
import { ALLORA_FORGE_QUERY_REPOSITORY } from '@app/core/domain/allora/allora-forge/allora-forge.query.repository';
import { AlloraUser } from '@app/core/domain/allora/allora-user/allora-user';
import { ALLORA_USER_COMMAND_REPOSITORY } from '@app/core/domain/allora/allora-user/allora-user.command.repository';
import { PointTransaction } from '@app/core/domain/allora/point/point-transaction';
import {
  MOCK_REPUTER_TOPIC_REWARDS_SETTLED_1,
  MOC<PERSON>_REPUTER_TOPIC_REWARDS_SETTLED_2,
  MOC<PERSON>_WORKER_TOPIC_REWARDS_SETTLED,
} from '@app/core/domain/allora/topic/events/allora-topic-event-rewards-settled';
import { TOPIC_COMMAND_REPOSITORY } from '@app/core/domain/allora/topic/topic.command.repository';
import { METRICS_LOGGER } from '@app/core/domain/metrics-logger/metrics-logger';
import { Test, TestingModule } from '@nestjs/testing';
import { v4 } from 'uuid';

import { ProcessTopicRewardsSettledsCommand } from './process-topic-rewards-settled.command';
import { ProcessTopicRewardsSettledUseCase } from './process-topic-rewards-settled.use-case';

describe('ProcessTopicWorkerLossUseCase', () => {
  let useCase: ProcessTopicRewardsSettledUseCase;

  // mock users
  const alloraUserCommandRepository = {
    getOrCreateAlloraUserIfDne: jest.fn().mockImplementation(
      (address) =>
        new AlloraUser({
          id: v4(),
          privyId: null,
          evmAddress: null,
          cosmosAddress: address,
          referrerId: null,
        }),
    ),
  };

  // mock point transactions
  const mockPointTransactions: PointTransaction[] = [
    {
      userId: 'fec2e0c0-baa8-438c-a52c-81169d83615e',
      points: 5.042,
      campaignSlug: 'process-inference',
      topicId: 10,
      blockNumber: 1259056,
    },
    {
      userId: 'c994caab-c1c2-4474-b32b-06308ce956e0',
      points: 94.156,
      campaignSlug: 'process-inference',
      topicId: 10,
      blockNumber: 1259056,
    },
    {
      userId: '24ecae0d-56c7-4083-bb00-953dd508a428',
      points: 88.604,
      campaignSlug: 'process-inference',
      topicId: 10,
      blockNumber: 1259056,
    },
    {
      userId: '244d7263-51c7-47f1-8be3-4a3db6e4a03f',
      points: 90.089,
      campaignSlug: 'process-inference',
      topicId: 10,
      blockNumber: 1259056,
    },
    {
      userId: '40c804db-5e3c-41b6-920f-c99813679419',
      points: 92.872,
      campaignSlug: 'process-inference',
      topicId: 10,
      blockNumber: 1259056,
    },
    {
      userId: '99b8e34a-2950-4b15-8327-5f8ad3f862e2',
      points: 97.239,
      campaignSlug: 'process-inference',
      topicId: 10,
      blockNumber: 1259056,
    },
    {
      userId: '60417067-56a3-47fc-838c-9137f3375012',
      points: 93.353,
      campaignSlug: 'process-inference',
      topicId: 10,
      blockNumber: 1259056,
    },
    {
      userId: '358b7010-553b-499c-8365-456dcad5f486',
      points: 91.512,
      campaignSlug: 'process-inference',
      topicId: 10,
      blockNumber: 1259056,
    },
    {
      userId: '7f88da8d-0b7d-46a8-bab7-3e825796de8c',
      points: 106.433,
      campaignSlug: 'process-inference',
      topicId: 10,
      blockNumber: 1259056,
    },
    {
      userId: '2f3534c3-cfa1-410c-93b1-28547ce3c72c',
      points: 92.99,
      campaignSlug: 'process-inference',
      topicId: 10,
      blockNumber: 1259056,
    },
  ];

  const metricsLogger = {
    success: jest.fn(),
    failure: jest.fn(),
  };

  const topicCommandRepository = {
    incrementTopicWorkerStats: jest.fn(),
    incrementTopicReputerStats: jest.fn(),
    insertTopicEmissions: jest.fn(),
    insertTopicRewards: jest.fn(),
  };

  // Add mock forge repositories
  const alloraForgeQueryRepository = {
    getCurrentSeason: jest.fn().mockResolvedValue({ id: 1 }),
    getCompetitionByTopicId: jest.fn().mockResolvedValue({
      id: 1,
      startDate: '2021-01-01',
      endDate: '2030-01-01',
    }),
    getUserCompetition: jest.fn().mockResolvedValue({
      id: 1,
      alloraUserId: 'test-user-id',
      approvedRegistration: true,
    }),
  };

  const alloraForgeCommandRepository = {
    insertPointTransactionBatch: jest.fn(),
    incrementPointsBatch: jest.fn(),
  };

  // Mock forge point transactions
  const mockForgePointTransactions: AlloraForgePointTransaction[] = [
    {
      userId: 'test-user-id',
      points: 5.042,
      competitionId: 1,
      blockNumber: 1259056,
    },
  ];

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [ProcessTopicRewardsSettledUseCase],
    })
      .useMocker((token) => {
        if (token == METRICS_LOGGER) return metricsLogger;
        if (token === TOPIC_COMMAND_REPOSITORY) return topicCommandRepository;
        if (token === UNIT_OF_WORK) return new MockUnitOfWork();
        if (token === ALLORA_USER_COMMAND_REPOSITORY)
          return alloraUserCommandRepository;
        if (token === ALLORA_FORGE_QUERY_REPOSITORY)
          return alloraForgeQueryRepository;
        if (token === ALLORA_FORGE_COMMAND_REPOSITORY)
          return alloraForgeCommandRepository;
        return {};
      })
      .compile();

    useCase = module.get<ProcessTopicRewardsSettledUseCase>(
      ProcessTopicRewardsSettledUseCase,
    );

    // spys
    jest
      .spyOn(useCase, 'createPointTransactions')
      .mockResolvedValue(mockPointTransactions);
    jest
      .spyOn(useCase, 'createForgePointTransactions')
      .mockResolvedValue(mockForgePointTransactions);
  });

  afterEach(async () => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(useCase).toBeDefined();
  });

  describe('process rewards use case', () => {
    it('Should parse uALLO correctly', async () => {
      const ualloAmount = '5700000000000000000.902553991979552252975516';
      const res = useCase.parseUalloAmount(ualloAmount);
      expect(res).toBe(5.7);
    });

    it('Should process and store new worker rewards and distribute points', async () => {
      const command = new ProcessTopicRewardsSettledsCommand(
        MOCK_WORKER_TOPIC_REWARDS_SETTLED,
      );
      await useCase.execute(command);
      // rewards timeseries
      expect(topicCommandRepository.insertTopicRewards).toHaveBeenCalledTimes(
        1,
      );
      // rewards
      expect(
        topicCommandRepository.incrementTopicWorkerStats,
      ).toHaveBeenCalledTimes(1);
      // points
      expect(useCase.createForgePointTransactions).toHaveBeenCalledTimes(1);
      // emissions
      expect(topicCommandRepository.insertTopicEmissions).toHaveBeenCalledTimes(
        1,
      );
    });

    it('Should process and store new reputer rewards and not distribute points', async () => {
      const command = new ProcessTopicRewardsSettledsCommand(
        MOCK_REPUTER_TOPIC_REWARDS_SETTLED_1,
      );
      await useCase.execute(command);
      // rewards timeseries
      expect(topicCommandRepository.insertTopicRewards).toHaveBeenCalledTimes(
        1,
      );
      // rewards
      expect(
        topicCommandRepository.incrementTopicReputerStats,
      ).toHaveBeenCalledTimes(1);
      // points
      expect(useCase.createForgePointTransactions).toHaveBeenCalledTimes(0);
      // emissions
      expect(topicCommandRepository.insertTopicEmissions).toHaveBeenCalledTimes(
        1,
      );
    });

    it('Should process and store new reputer rewards part 2', async () => {
      const command = new ProcessTopicRewardsSettledsCommand(
        MOCK_REPUTER_TOPIC_REWARDS_SETTLED_2,
      );
      await useCase.execute(command);
      // rewards timeseries
      expect(topicCommandRepository.insertTopicRewards).toHaveBeenCalledTimes(
        1,
      );
      // rewards
      expect(
        topicCommandRepository.incrementTopicReputerStats,
      ).toHaveBeenCalledTimes(1);
      // points
      expect(useCase.createForgePointTransactions).toHaveBeenCalledTimes(0);
      // emissions
      expect(topicCommandRepository.insertTopicEmissions).toHaveBeenCalledTimes(
        1,
      );
    });

    it('Should process worker rewards and distribute both points and forge points', async () => {
      const command = new ProcessTopicRewardsSettledsCommand(
        MOCK_WORKER_TOPIC_REWARDS_SETTLED,
      );
      await useCase.execute(command);
      // rewards timeseries
      expect(topicCommandRepository.insertTopicRewards).toHaveBeenCalledTimes(
        1,
      );
      // Check regular points processing
      expect(useCase.createForgePointTransactions).toHaveBeenCalledTimes(1);

      // Check forge points processing
      expect(useCase.createForgePointTransactions).toHaveBeenCalledTimes(1);
      expect(
        alloraForgeCommandRepository.insertPointTransactionBatch,
      ).toHaveBeenCalledTimes(1);
      expect(
        alloraForgeCommandRepository.incrementPointsBatch,
      ).toHaveBeenCalledTimes(1);
    });
  });
});
