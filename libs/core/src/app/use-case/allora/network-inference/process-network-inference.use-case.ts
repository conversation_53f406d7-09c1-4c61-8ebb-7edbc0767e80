import { UnitOfWork } from '@app/core/app/unit-of-work/unit-of-work.decorator';
import {
  AlloraChainConfig,
  EAlloraChainId,
} from '@app/core/domain/allora/chain/allora-chain-config';
import { NetworkInferenceData } from '@app/core/domain/allora/network-inference/network-inference';
import {
  ALLORA_NETWORK_INFERENCE_COMMAND_REPOSITORY,
  NetworkInferenceCommandRepository,
} from '@app/core/domain/allora/network-inference/network-inference.command.repository';
import {
  METRICS_LOGGER,
  MetricsLogger,
} from '@app/core/domain/metrics-logger/metrics-logger';
import { EStatName } from '@app/core/domain/metrics-logger/stat-name.type';
import { AlloraEntityLogger } from '@app/core/infra/logger/allora-entity.logger';
import { Inject, Injectable } from '@nestjs/common';
import { <PERSON><PERSON><PERSON><PERSON>, ICommandHandler } from '@nestjs/cqrs';

import { ProcessNetworkInferenceCommand } from './process-network-inference.command';

@Injectable()
@CommandHandler(ProcessNetworkInferenceCommand)
export class ProcessNetworkInferenceUseCase
  implements ICommandHandler<ProcessNetworkInferenceCommand>
{
  private readonly logger = new AlloraEntityLogger(
    ProcessNetworkInferenceUseCase.name,
  );

  constructor(
    @Inject(METRICS_LOGGER)
    private readonly metricsLogger: MetricsLogger,
    @Inject(ALLORA_NETWORK_INFERENCE_COMMAND_REPOSITORY)
    private readonly networkInferenceCommandRepository: NetworkInferenceCommandRepository,
  ) {}

  @UnitOfWork()
  async execute(command: ProcessNetworkInferenceCommand): Promise<void> {
    const event = command.message.payload.data;
    const metadata = command.message.payload.metadata;
    const alloraChainConfig = new AlloraChainConfig(
      metadata.block_metadata.chain_id as EAlloraChainId,
    );

    try {
      const valueBundle = event?.value_bundle;
      if (!valueBundle) {
        this.logger.warn(
          `Found no value bundle in event: ${JSON.stringify(command.message)}`,
        );
        return;
      }

      const topicId = Number(valueBundle.topic_id);
      const combinedValue = valueBundle.combined_value;
      const naiveValue = valueBundle.naive_value;
      const isoTimestamp = metadata.block_metadata.time;
      const unixTimestamp = Math.floor(new Date(isoTimestamp).getTime() / 1000);

      if (!topicId || !combinedValue || !naiveValue) {
        this.logger.warn(
          `Found missing required fields in event: ${JSON.stringify(
            command.message,
          )}`,
        );
        return;
      }

      const networkInference = new NetworkInferenceData({
        topicId,
        combinedValue,
        naiveValue,
        timestamp: unixTimestamp,
      });

      // Insert network inference into database
      await this.networkInferenceCommandRepository.insertNetworkInference(
        networkInference,
        alloraChainConfig,
      );

      this.metricsLogger.success(
        EStatName.ALLORA,
        'ProcessNetworkInferenceUseCase',
      );
    } catch (error) {
      this.logger.failure(command.message.toString(), error);
      this.metricsLogger.failure(
        EStatName.ALLORA,
        'ProcessNetworkInferenceUseCase',
      );
      throw error;
    }
  }
}
