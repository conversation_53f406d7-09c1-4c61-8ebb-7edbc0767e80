import { MockUnitOfWork } from '@app/core/app/unit-of-work/mock.unit-of-work';
import { UNIT_OF_WORK } from '@app/core/app/unit-of-work/unit-of-work';
import { ALLORA_NETWORK_INFERENCE_COMMAND_REPOSITORY } from '@app/core/domain/allora/network-inference/network-inference.command.repository';
import { METRICS_LOGGER } from '@app/core/domain/metrics-logger/metrics-logger';
import { Test, TestingModule } from '@nestjs/testing';

import {
  NetworkInferenceMessage,
  ProcessNetworkInferenceCommand,
} from './process-network-inference.command';
import { ProcessNetworkInferenceUseCase } from './process-network-inference.use-case';

const MOCK_NETWORK_INFERENCE_EVENT: NetworkInferenceMessage = {
  payload: {
    metadata: {
      block_metadata: {
        height: 4275474,
        chain_id: 'allora-testnet-1',
        hash: 'B8BE6EC3635935FC9B3A83CA3E9EA62595B7139A958B6880B36E6114A886281A',
        time: '2025-06-16T17:35:48.142650602Z',
      },
      transaction_metadata: {
        type: 'emissions.v9.EventNetworkInferences',
      },
    },
    data: {
      topic_id: '47',
      block_height: '4275465',
      value_bundle: {
        topic_id: '47',
        reputer_request_nonce: {
          reputer_nonce: {
            block_height: '4275465',
          },
        },
        reputer: 'allo1qqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqas6usy',
        extra_data: null,
        combined_value: '107891.86',
        inferer_values: [
          {
            worker: 'allo1tddqezsrxs3l0xj7rzlkrt35e3yu7zpqzvjalj',
            value: '107891.86',
          },
        ],
        forecaster_values: [],
        naive_value: '107891.86',
        one_out_inferer_values: [],
        one_out_forecaster_values: [],
        one_in_forecaster_values: [],
        one_out_inferer_forecaster_values: [],
      },
    },
  },
};

describe('ProcessNetworkInferenceUseCase', () => {
  let useCase: ProcessNetworkInferenceUseCase;

  const networkInferenceCommandRepository = {
    insertNetworkInference: jest.fn(),
  };

  const metricsLogger = {
    success: jest.fn(),
    failure: jest.fn(),
    notChanged: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [ProcessNetworkInferenceUseCase],
    })
      .useMocker((token) => {
        if (token === UNIT_OF_WORK) return new MockUnitOfWork();
        if (token === ALLORA_NETWORK_INFERENCE_COMMAND_REPOSITORY)
          return networkInferenceCommandRepository;
        if (token === METRICS_LOGGER) return metricsLogger;
        return {};
      })
      .compile();

    useCase = module.get<ProcessNetworkInferenceUseCase>(
      ProcessNetworkInferenceUseCase,
    );
  });

  afterEach(async () => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(useCase).toBeDefined();
  });

  describe('process network inference use case', () => {
    it('Should process and store new network inferences', async () => {
      const command = new ProcessNetworkInferenceCommand(
        MOCK_NETWORK_INFERENCE_EVENT,
      );
      await useCase.execute(command);

      expect(
        networkInferenceCommandRepository.insertNetworkInference,
      ).toHaveBeenCalledTimes(1);
      expect(
        networkInferenceCommandRepository.insertNetworkInference,
      ).toHaveBeenCalledWith(
        expect.objectContaining({
          topicId: 47,
          combinedValue: '107891.86',
          naiveValue: '107891.86',
          timestamp: expect.any(Number),
        }),
        expect.any(Object), // AlloraChainConfig
      );
    });

    it('Should handle missing value bundle gracefully', async () => {
      const eventWithoutValueBundle: NetworkInferenceMessage = {
        payload: {
          metadata: MOCK_NETWORK_INFERENCE_EVENT.payload.metadata,
          data: {
            topic_id: '47',
            block_height: '4275465',
            value_bundle: null as any,
          },
        },
      };

      const command = new ProcessNetworkInferenceCommand(
        eventWithoutValueBundle,
      );
      await useCase.execute(command);

      expect(
        networkInferenceCommandRepository.insertNetworkInference,
      ).toHaveBeenCalledTimes(0);
    });

    it('Should handle missing required fields gracefully', async () => {
      const eventWithMissingFields: NetworkInferenceMessage = {
        payload: {
          metadata: MOCK_NETWORK_INFERENCE_EVENT.payload.metadata,
          data: {
            topic_id: '47',
            block_height: '4275465',
            value_bundle: {
              ...MOCK_NETWORK_INFERENCE_EVENT.payload.data.value_bundle,
              combined_value: null as any,
            },
          },
        },
      };

      const command = new ProcessNetworkInferenceCommand(
        eventWithMissingFields,
      );
      await useCase.execute(command);

      expect(
        networkInferenceCommandRepository.insertNetworkInference,
      ).toHaveBeenCalledTimes(0);
    });
  });
});
