import { AlloraChainConfig } from '../chain/allora-chain-config';
import { NetworkInferenceData } from './network-inference';

export const ALLORA_NETWORK_INFERENCE_COMMAND_REPOSITORY = Symbol(
  'NetworkInferenceCommandRepository',
);

export interface NetworkInferenceCommandRepository {
  insertNetworkInference(
    networkInference: NetworkInferenceData,
    alloraChainConfig: AlloraChainConfig,
  ): Promise<void>;
}
