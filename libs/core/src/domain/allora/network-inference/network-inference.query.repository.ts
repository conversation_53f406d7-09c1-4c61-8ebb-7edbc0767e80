import { CursorPagination } from '../../value-object/cursor-pagination';
import { AlloraChainConfig } from '../chain/allora-chain-config';
import { NetworkInferenceContinuationPageOutput } from './network-inference';

export const ALLORA_NETWORK_INFERENCE_QUERY_REPOSITORY = Symbol(
  'NetworkInferenceQueryRepository',
);

export interface NetworkInferenceQueryRepository {
  getNetworkInferencesByTopicId(
    topicId: number,
    alloraChainConfig: AlloraChainConfig,
    pagination: CursorPagination,
    filters: { fromTimestamp?: number },
  ): Promise<NetworkInferenceContinuationPageOutput>;
}
