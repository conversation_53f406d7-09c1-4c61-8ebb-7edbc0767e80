import { ALLORA_NETWORK_INFERENCE_QUERY_REPOSITORY } from '@app/core/domain/allora/network-inference/network-inference.query.repository';
import { SequelizeNetworkInferenceQueryRepository } from '@app/core/infra/repository/allora/network-inference/query/sequelize.network-inference.query.repository';
import { Modu<PERSON> } from '@nestjs/common';

import { ContinuationTokenRepositoryModule } from '../../continuation-token/continuation-token.repository.module';

@Module({
  imports: [ContinuationTokenRepositoryModule],
  providers: [
    {
      useClass: SequelizeNetworkInferenceQueryRepository,
      provide: ALLORA_NETWORK_INFERENCE_QUERY_REPOSITORY,
    },
  ],
  exports: [ALLORA_NETWORK_INFERENCE_QUERY_REPOSITORY],
})
export class NetworkInferenceQueryRepositoryModule {}
