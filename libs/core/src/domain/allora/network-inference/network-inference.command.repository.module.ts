import { SequelizeNetworkInferenceCommandRepository } from '@app/core/infra/repository/allora/network-inference/command/sequelize.network-inference.command.repository';
import { Module } from '@nestjs/common';

import { ALLORA_NETWORK_INFERENCE_COMMAND_REPOSITORY } from './network-inference.command.repository';

@Module({
  imports: [],
  providers: [
    {
      useClass: SequelizeNetworkInferenceCommandRepository,
      provide: ALLORA_NETWORK_INFERENCE_COMMAND_REPOSITORY,
    },
  ],
  exports: [ALLORA_NETWORK_INFERENCE_COMMAND_REPOSITORY],
})
export class NetworkInferenceCommandRepositoryModule {}
