import { ContinuationToken } from '../../continuation-token/continuation-token';

export interface NetworkInferenceProps {
  readonly topicId: number;
  readonly combinedValue: string;
  readonly naiveValue: string;
  readonly timestamp: number;
}

export class NetworkInferenceData {
  readonly topicId: number;
  readonly combinedValue: string;
  readonly naiveValue: string;
  readonly timestamp: number;

  constructor(props: NetworkInferenceProps) {
    this.topicId = props.topicId;
    this.combinedValue = props.combinedValue;
    this.naiveValue = props.naiveValue;
    this.timestamp = props.timestamp;
  }
}

export interface NetworkInferenceContinuationPageOutput {
  data: NetworkInferenceData[];
  continuationToken?: ContinuationToken;
}
