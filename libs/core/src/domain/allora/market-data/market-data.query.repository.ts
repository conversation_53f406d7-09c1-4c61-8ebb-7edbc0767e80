import { CursorPagination } from '../../value-object/cursor-pagination';
import { MarketData, MarketDataContinuationPageOutput } from './market-data';

export const MARKET_DATA_QUERY_REPOSITORY = Symbol('MarketDataQueryRepository');

export interface MarketDataQueryRepository {
  getLatestCandle(): Promise<MarketData | null>;
  getLatestMarketData(
    tickers: string[],
    fromDate: Date,
    pagination: CursorPagination,
  ): Promise<MarketDataContinuationPageOutput>;
}
