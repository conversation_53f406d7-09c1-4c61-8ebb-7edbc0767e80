export interface TickerConfig {
  symbol: string;
  conversion?: {
    ticker: string;
    from: string;
    to: string;
  };
}

export const ENABLED_TICKERS: Readonly<TickerConfig[]> = [
  // USD pairs
  { symbol: 'ETHUSD' },
  { symbol: 'BTCUSD' },
  { symbol: 'ARBUSD' },
  { symbol: 'BNBUSD' },
  { symbol: 'SOLUSD' },
  { symbol: 'BERAUSD' },
  { symbol: 'frxETHWETH' },
  { symbol: 'mETHUSDT' },
  { symbol: 'VIRTUALUSDT' },
  { symbol: 'AIXBTUSDT' },
  { symbol: 'LUNAUSDT' },
  { symbol: 'PAXGUSD' },
  // ETH specific pairs (for these we manually convert to USDT)
  {
    symbol: 'VADERETH',
    conversion: { ticker: 'ETHUSD', from: 'ETH', to: 'USDT' },
  },
  {
    symbol: 'GAMEVETH',
    conversion: { ticker: 'ETHUSD', from: 'ETH', to: 'USDT' },
  },
  {
    symbol: 'SEKOIAETH',
    conversion: { ticker: 'ETHUSD', from: 'ETH', to: 'USDT' },
  },
];

export const ENABLED_TICKER_SYMBOLS: Readonly<string[]> = ENABLED_TICKERS.map(
  (t) => t.symbol,
);

export const ENABLED_TICKER_SYMBOLS_LOWERCASE: Readonly<string[]> =
  ENABLED_TICKER_SYMBOLS.map((t) => t.toLowerCase());
