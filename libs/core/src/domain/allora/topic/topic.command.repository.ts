import { AlloraChainConfig } from '../chain/allora-chain-config';
import { ETopicStatsType, TopicEmaScore, TopicMetadata } from './topic-details';

export const TOPIC_COMMAND_REPOSITORY = Symbol('TopicCommandRepository');

export interface TopicCommandRepository {
  insertTopicRewards(
    topicId: number,
    actorType: string,
    data: { address: string; value: number }[],
    blockNumber: number,
    timestamp: number,
    alloraChainConfig: AlloraChainConfig,
  ): Promise<void>;
  updateAllTopicsTotalStake(
    timestamp: number,
    alloraChainConfig: AlloraChainConfig,
  ): Promise<void>;
  updateTopicEmaScores(
    type: 'worker' | 'reputer',
    topicId: number,
    scores: TopicEmaScore[],
    alloraChainConfig: AlloraChainConfig,
  ): Promise<void>;
  deleteTimeseriesData(tableName: string, timestamp: number): Promise<void>;
  insertTopicTotalStaked(
    topicId: number,
    timestamp: number,
    value: string,
    alloraChainConfig: AlloraChainConfig,
  ): Promise<void>;
  insertTopicWorkerCount(
    topicId: number,
    timestamp: number,
    value: number,
    alloraChainConfig: AlloraChainConfig,
  ): Promise<void>;
  insertTopicEmissions(
    topicId: number,
    timestamp: number,
    value: number,
    alloraChainConfig: AlloraChainConfig,
  ): Promise<void>;
  incrementTopicReputerStats(
    topicId: number,
    type: ETopicStatsType,
    data: { address: string; value: number }[],
    alloraChainConfig: AlloraChainConfig,
  ): Promise<void>;
  incrementTopicWorkerStats(
    topicId: number,
    type: ETopicStatsType,
    data: { address: string; value: number }[],
    alloraChainConfig: AlloraChainConfig,
  ): Promise<void>;
  updateTopicWorkerStats(
    topicId: number,
    type: ETopicStatsType,
    data: { address: string; value: number }[],
    alloraChainConfig: AlloraChainConfig,
  ): Promise<void>;
  updateTopicReputerStats(
    topicId: number,
    type: ETopicStatsType,
    data: { address: string; value: number }[],
    alloraChainConfig: AlloraChainConfig,
  ): Promise<void>;
  insertTopicWorkerLosses(
    topicId: number,
    timestamp: number,
    losses: { address: string; value: string }[],
    alloraChainConfig: AlloraChainConfig,
  ): Promise<void>;
  insertTopicReputerLosses(
    topicId: number,
    timestamp: number,
    losses: { address: string; value: string }[],
    alloraChainConfig: AlloraChainConfig,
  ): Promise<void>;
  updateTopicMetadata(
    topicMetadata: TopicMetadata,
    alloraChainConfig: AlloraChainConfig,
  ): Promise<void>;
}
