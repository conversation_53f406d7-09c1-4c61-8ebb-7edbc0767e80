import { ContinuationToken } from '../../continuation-token/continuation-token';

export enum ETopicStatsType {
  WEIGHT = 'weight',
  LOSS = 'loss',
  TOTAL_EARNED = 'total_earned',
}

export enum TopicSortingOptions {
  TOTAL_STAKED = 'total_staked',
  TOTAL_FUNDING = 'total_funding',
  WORKER_COUNT = 'worker_count',
  REPUTER_COUNT = 'reputer_count',
  EPOCH_LENGTH = 'epoch_length',
}

export enum ETopicReputerStakeType {
  ADD_STAKE = 'ADD_STAKE',
  REMOVE_STAKE = 'REMOVE_STAKE',
  CANCEL_REMOVE_STAKE = 'CANCEL_REMOVE_STAKE',
  DELEGATE_STAKE = 'DELEGATE_STAKE',
  REMOVE_DELEGATE_STAKE = 'REMOVE_DELEGATE_STAKE',
  CANCEL_REMOVE_DELEGATE_STAKE = 'CANCEL_REMOVE_DELEGATE_STAKE',
}

export interface TopicReputerStake {
  type: ETopicReputerStakeType;
  topicId: number;
  is_delegate: boolean;
  sender: string;
  amount: number | null;
  reputerAddress: string;
  targetBlock: number | null;
  blockTimestamp: string | null;
  blockNumber: number;
}

export interface TopicEmaScore {
  address: string;
  score: string;
  is_active: boolean;
  nonce: number;
}

export interface TopicWorkerStats {
  address: string;
  score: number;
  loss: number;
  totalEarned: string;
  alloEarned1d: string;
  alloEarned3d: string;
  alloEarned7d: string;
}

export interface TopicWorkerReputerCount {
  topic_id: number;
  reputer_count: number;
  worker_count: number;
}

export interface TopicMetadata {
  id: number;
  creator: string | null; // creator can be null if not provided
  name: string | null; // name can be null
  lossMethod: string | null; // lossMethod can be null
  epochLength: string | null; // epochLength can be null
  groundTruthLag: string | null; // groundTruthLag can be null
  pNorm: string | null; // pNorm can be null
  alphaRegret: string | null; // alphaRegret can be null
  allowNegative: boolean; // boolean for true/false values
  epsilon: string | null; // epsilon can be null
  initialRegret: string | null; // initialRegret can be null
  workerSubmissionWindow: string | null; // workerSubmissionWindow can be null
  meritSortitionAlpha: string | null; // meritSortitionAlpha can be null
  activeInfererQuantile: string | null; // activeInfererQuantile can be null
  activeForecastorQuantile: string | null; // activeForecastorQuantile can be null
  activeReputerQuantile: string | null; // activeReputerQuantile can be null
  isActive: boolean | null; // isActive can be null
}

class TopicDetailsProps {
  rank: number;
  topicId: number;
  topicName: string;
  description?: string | null;
  epochLength: number;
  groundTruthLag: number;
  lossMethod: string;
  workerSubmissionWindow: number;
  workerCount: number;
  reputerCount: number;
  totalStakedAllo: string;
  totalEmissionsAllo: string;
  isActive: boolean | null;
  isEndorsed: boolean;
  forgeCompetitionId: number | null;
  forgeCompetitionStartDate: string | null;
  forgeCompetitionEndDate: string | null;
  updatedAt: string;
}

export class TopicDetails extends TopicDetailsProps {
  constructor(props: TopicDetailsProps) {
    super();
    Object.assign(this, props);
  }
}

export interface TopicsContinuationPageOutput {
  topics: TopicDetails[];
  continuationToken?: ContinuationToken;
}
