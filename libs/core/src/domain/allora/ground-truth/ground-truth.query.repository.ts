import { CursorPagination } from '@app/core/domain/value-object/cursor-pagination';

import { AlloraChainConfig } from '../chain/allora-chain-config';
import {
  GroundTruthContinuationPageOutput,
  GroundTruthData,
} from './ground-truth';

export const ALLORA_GROUND_TRUTH_QUERY_REPOSITORY = Symbol(
  'GroundTruthQueryRepository',
);

export interface GroundTruthQueryRepository {
  getLatestGroundTruthByTopicId(
    topicId: number,
    alloraChainConfig: AlloraChainConfig,
  ): Promise<GroundTruthData | null>;

  getGroundTruthsByTopicId(
    topicId: number,
    alloraChainConfig: AlloraChainConfig,
    pagination: CursorPagination,
    filters: { fromTimestamp?: number },
  ): Promise<GroundTruthContinuationPageOutput>;
}
