import { SequelizeGroundTruthCommandRepository } from '@app/core/infra/repository/allora/ground-truth/command/sequelize.ground-truth.command.repository';
import { Module } from '@nestjs/common';

import { ALLORA_GROUND_TRUTH_COMMAND_REPOSITORY } from './ground-truth.command.repository';

@Module({
  providers: [
    {
      useClass: SequelizeGroundTruthCommandRepository,
      provide: ALLORA_GROUND_TRUTH_COMMAND_REPOSITORY,
    },
  ],
  exports: [ALLORA_GROUND_TRUTH_COMMAND_REPOSITORY],
})
export class GroundTruthCommandRepositoryModule {}
