import { ContinuationToken } from '../../continuation-token/continuation-token';

export interface GroundTruthData {
  topicId: number;
  gtValue: string;
  timestamp: number;
  epochLastEndedHeight: number;
  reputationTimestamp: number;
}

import { EAlloraChainId } from '../chain/allora-chain-config';

export enum ETopicTargetValueType {
  PRICE = 'price',
  VOLATILITY = 'volatility',
  VOLUME = 'volume',
  ELECTION = 'election',
  RETURN = 'return',
}

export interface TopicConfig {
  topicId: number;
  type: ETopicTargetValueType;
  ticker: string;
  timeInterval: string;
}

// Topic configs for each allora chain
// Note: The config is currently used for the ground truth updater cron job
export const TOPIC_CONFIGS: { [key: string]: TopicConfig[] } = {
  [EAlloraChainId.ALLORA_TESTNET_1]: [
    // Price Predictions - 5min
    {
      topicId: 13,
      type: ETopicTargetValueType.PRICE,
      ticker: 'ethusd',
      timeInterval: '5m',
    },
    {
      topicId: 14,
      type: ETopicTargetValueType.PRICE,
      ticker: 'btcusd',
      timeInterval: '5m',
    },
    {
      topicId: 30,
      type: ETopicTargetValueType.PRICE,
      ticker: 'ethusd',
      timeInterval: '5m',
    },
    {
      topicId: 37,
      type: ETopicTargetValueType.PRICE,
      ticker: 'solusd',
      timeInterval: '5m',
    },
    {
      topicId: 22,
      type: ETopicTargetValueType.PRICE,
      ticker: 'virtualusdt',
      timeInterval: '5m',
    },
    {
      topicId: 23,
      type: ETopicTargetValueType.PRICE,
      ticker: 'aixbtusdt',
      timeInterval: '5m',
    },
    {
      topicId: 24,
      type: ETopicTargetValueType.PRICE,
      ticker: 'lunausdt',
      timeInterval: '5m',
    },
    {
      topicId: 25,
      type: ETopicTargetValueType.PRICE,
      ticker: 'vaderusdt',
      timeInterval: '5m',
    },
    {
      topicId: 26,
      type: ETopicTargetValueType.PRICE,
      ticker: 'gamevusdt',
      timeInterval: '5m',
    },
    {
      topicId: 27,
      type: ETopicTargetValueType.PRICE,
      ticker: 'sekoiausdt',
      timeInterval: '5m',
    },
    {
      topicId: 47,
      type: ETopicTargetValueType.PRICE,
      ticker: 'btcusd',
      timeInterval: '5m',
    },
    // Price Predictions - 10min
    {
      topicId: 1,
      type: ETopicTargetValueType.PRICE,
      ticker: 'ethusd',
      timeInterval: '10m',
    },
    {
      topicId: 3,
      type: ETopicTargetValueType.PRICE,
      ticker: 'btcusd',
      timeInterval: '10m',
    },
    {
      topicId: 5,
      type: ETopicTargetValueType.PRICE,
      ticker: 'solusd',
      timeInterval: '10m',
    },
    // Price Predictions - 20min
    {
      topicId: 7,
      type: ETopicTargetValueType.PRICE,
      ticker: 'ethusd',
      timeInterval: '20m',
    },
    {
      topicId: 8,
      type: ETopicTargetValueType.PRICE,
      ticker: 'bnbusd',
      timeInterval: '20m',
    },
    {
      topicId: 9,
      type: ETopicTargetValueType.PRICE,
      ticker: 'arbusd',
      timeInterval: '20m',
    },
    // Price Predictions - 1h
    {
      topicId: 56,
      type: ETopicTargetValueType.PRICE,
      ticker: 'berausd',
      timeInterval: '1h',
    },
    // Price Predictions - 6h
    {
      topicId: 46,
      type: ETopicTargetValueType.PRICE,
      ticker: 'ethusdc',
      timeInterval: '6h',
    }, // Inactive
    // Price Predictions - 8h
    {
      topicId: 17,
      type: ETopicTargetValueType.PRICE,
      ticker: 'ethusd',
      timeInterval: '8h',
    },
    {
      topicId: 18,
      type: ETopicTargetValueType.PRICE,
      ticker: 'btcusd',
      timeInterval: '8h',
    },
    {
      topicId: 21,
      type: ETopicTargetValueType.PRICE,
      ticker: 'ethusd',
      timeInterval: '8h',
    }, // Inactive
    {
      topicId: 31,
      type: ETopicTargetValueType.PRICE,
      ticker: 'virtualusdt',
      timeInterval: '8h',
    },
    {
      topicId: 32,
      type: ETopicTargetValueType.PRICE,
      ticker: 'aixbtusdt',
      timeInterval: '8h',
    },
    {
      topicId: 33,
      type: ETopicTargetValueType.PRICE,
      ticker: 'lunausdt',
      timeInterval: '8h',
    },
    {
      topicId: 34,
      type: ETopicTargetValueType.PRICE,
      ticker: 'vaderusdt',
      timeInterval: '8h',
    },
    {
      topicId: 35,
      type: ETopicTargetValueType.PRICE,
      ticker: 'gamevusdt',
      timeInterval: '8h',
    },
    {
      topicId: 36,
      type: ETopicTargetValueType.PRICE,
      ticker: 'sekoiausdt',
      timeInterval: '8h',
    },
    {
      topicId: 38,
      type: ETopicTargetValueType.PRICE,
      ticker: 'solusd',
      timeInterval: '8h',
    },
    {
      topicId: 41,
      type: ETopicTargetValueType.PRICE,
      ticker: 'ethusd',
      timeInterval: '8h',
    },
    {
      topicId: 42,
      type: ETopicTargetValueType.PRICE,
      ticker: 'btcusd',
      timeInterval: '8h',
    },
    // Price Predictions - 12h
    {
      topicId: 28,
      type: ETopicTargetValueType.PRICE,
      ticker: 'ethusd',
      timeInterval: '12h',
    },
    {
      topicId: 29,
      type: ETopicTargetValueType.VOLUME,
      ticker: 'ethusdc',
      timeInterval: '12h',
    },
    // Price Predictions - 24h
    {
      topicId: 2,
      type: ETopicTargetValueType.PRICE,
      ticker: 'ethusd',
      timeInterval: '24h',
    },
    {
      topicId: 4,
      type: ETopicTargetValueType.PRICE,
      ticker: 'btcusd',
      timeInterval: '24h',
    },
    {
      topicId: 6,
      type: ETopicTargetValueType.PRICE,
      ticker: 'solusd',
      timeInterval: '24h',
    },
    {
      topicId: 12,
      type: ETopicTargetValueType.PRICE,
      ticker: 'ethusd',
      timeInterval: '24h',
    }, // Inactive
    // Volatility Predictions - 5min
    {
      topicId: 15,
      type: ETopicTargetValueType.VOLATILITY,
      ticker: 'ethusd',
      timeInterval: '5m',
    },
    {
      topicId: 16,
      type: ETopicTargetValueType.VOLATILITY,
      ticker: 'btcusd',
      timeInterval: '5m',
    },
    {
      topicId: 39,
      type: ETopicTargetValueType.VOLATILITY,
      ticker: 'solusd',
      timeInterval: '5m',
    }, // Inactive
    // Volatility Predictions - 8h
    {
      topicId: 19,
      type: ETopicTargetValueType.VOLATILITY,
      ticker: 'ethusd',
      timeInterval: '8h',
    },
    {
      topicId: 20,
      type: ETopicTargetValueType.VOLATILITY,
      ticker: 'btcusd',
      timeInterval: '8h',
    },
    {
      topicId: 40,
      type: ETopicTargetValueType.VOLATILITY,
      ticker: 'solusd',
      timeInterval: '8h',
    }, // Inactive
    {
      topicId: 43,
      type: ETopicTargetValueType.VOLATILITY,
      ticker: 'ethusd',
      timeInterval: '8h',
    }, // Inactive
    {
      topicId: 44,
      type: ETopicTargetValueType.VOLATILITY,
      ticker: 'btcusd',
      timeInterval: '8h',
    }, // Inactive
    // Other Predictions
    {
      topicId: 10,
      type: ETopicTargetValueType.PRICE,
      ticker: 'memecoinusd',
      timeInterval: '1h',
    },
    {
      topicId: 11,
      type: ETopicTargetValueType.ELECTION,
      ticker: 'uspresidential2024',
      timeInterval: '24h',
    },
    {
      topicId: 45,
      type: ETopicTargetValueType.RETURN,
      ticker: 'suiusdt',
      timeInterval: '30m',
    }, // Inactive
    // Price Predictions - 30min
    {
      topicId: 48,
      type: ETopicTargetValueType.PRICE,
      ticker: 'suiusdt',
      timeInterval: '30m',
    },
    // Volatility Predictions - 6h
    {
      topicId: 50,
      type: ETopicTargetValueType.VOLATILITY,
      ticker: 'btcusd',
      timeInterval: '6h',
    },
    // Log-Return Predictions - 24h
    {
      topicId: 60,
      type: ETopicTargetValueType.RETURN,
      ticker: 'paxgusd',
      timeInterval: '24h',
    },
    {
      topicId: 58,
      type: ETopicTargetValueType.RETURN,
      ticker: 'solusd',
      timeInterval: '8h',
    },
  ],
  [EAlloraChainId.ALLORA_MAINNET_1]: [],
  [EAlloraChainId.ALLORA_DEVNET_1]: [],
};

export interface GroundTruthContinuationPageOutput {
  data: GroundTruthData[];
  continuationToken?: ContinuationToken;
}
