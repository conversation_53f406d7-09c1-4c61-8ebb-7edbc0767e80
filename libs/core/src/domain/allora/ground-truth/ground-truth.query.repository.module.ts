import { SequelizeGroundTruthQueryRepository } from '@app/core/infra/repository/allora/ground-truth/query/sequelize.ground-truth.query.repository';
import { Module } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';

import { ContinuationTokenRepositoryModule } from '../../continuation-token/continuation-token.repository.module';
import { ALLORA_GROUND_TRUTH_QUERY_REPOSITORY } from './ground-truth.query.repository';

@Module({
  imports: [SequelizeModule, ContinuationTokenRepositoryModule],
  providers: [
    {
      provide: ALLORA_GROUND_TRUTH_QUERY_REPOSITORY,
      useClass: SequelizeGroundTruthQueryRepository,
    },
  ],
  exports: [ALLORA_GROUND_TRUTH_QUERY_REPOSITORY],
})
export class GroundTruthQueryRepositoryModule {}
