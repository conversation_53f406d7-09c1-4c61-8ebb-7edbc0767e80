export const isNumericString = (value: string) => {
  return /^\d+$/.test(value);
};

/**
 * @param date
 * @returns inputted date value in UNIX seconds
 */
export const date2Timestamp = (date: Date): number => {
  return Math.floor(date.getTime() / 1000);
};

/**
 * Parse time interval string like '5m', '1h', '24h' into milliseconds
 */
export function parseTimeInterval(interval: string): number {
  const match = interval.match(/^(\d+)([a-zA-Z]+)$/);

  if (!match) {
    throw new Error(`Invalid time interval: ${interval}`);
  }

  const value = parseInt(match[1], 10);
  const unit = match[2].toLowerCase();

  switch (unit) {
    case 'm':
      return value * 60 * 1000; // minutes to milliseconds
    case 'h':
      return value * 60 * 60 * 1000; // hours to milliseconds
    case 'd':
      return value * 24 * 60 * 60 * 1000; // days to milliseconds
    default:
      throw new Error(`Invalid time interval: ${interval}`);
  }
}
