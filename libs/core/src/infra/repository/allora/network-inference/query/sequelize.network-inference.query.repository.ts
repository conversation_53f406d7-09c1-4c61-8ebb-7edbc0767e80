import { AlloraChainConfig } from '@app/core/domain/allora/chain/allora-chain-config';
import {
  NetworkInferenceContinuationPageOutput,
  NetworkInferenceData,
} from '@app/core/domain/allora/network-inference/network-inference';
import { NetworkInferenceQueryRepository } from '@app/core/domain/allora/network-inference/network-inference.query.repository';
import {
  CONTINUATION_TOKEN_REPOSITORY,
  ContinuationTokenRepository,
} from '@app/core/domain/continuation-token/continuation-token.repository';
import { CursorPagination } from '@app/core/domain/value-object/cursor-pagination';
import { makeScalarCursorGetAndSetMethods } from '@app/core/infra/utils/cursor.handler';
import { Inject, Injectable } from '@nestjs/common';
import { QueryTypes } from 'sequelize';
import { Sequelize } from 'sequelize-typescript';

@Injectable()
export class SequelizeNetworkInferenceQueryRepository
  implements NetworkInferenceQueryRepository
{
  constructor(
    private readonly sequelize: Sequelize,
    @Inject(CONTINUATION_TOKEN_REPOSITORY)
    private readonly continuationTokenRepository: ContinuationTokenRepository,
  ) {}

  async getNetworkInferencesByTopicId(
    topicId: number,
    alloraChainConfig: AlloraChainConfig,
    pagination: CursorPagination,
    filters: { fromTimestamp?: number },
  ): Promise<NetworkInferenceContinuationPageOutput> {
    const maxPageSize = 10000;
    const { getCursor, setCursor } = makeScalarCursorGetAndSetMethods<
      'timestamp',
      { timestamp: number }
    >(this.continuationTokenRepository, 'timestamp');

    const cursor = (await getCursor(pagination)) || 0;
    const limit = maxPageSize + 1;

    const tableName = alloraChainConfig.networkSpecificTableName(
      'allora_network_inferences',
    );

    const fromTimestamp = filters.fromTimestamp || 0;
    const results = await this.sequelize.query<NetworkInferenceData>(
      `
      SELECT * FROM ${tableName} 
      WHERE 
        topic_id = :topicId AND 
        timestamp >= :fromTimestamp AND
        timestamp > :cursor
      ORDER BY timestamp ASC
      LIMIT :limit
      `,
      {
        type: QueryTypes.SELECT,
        replacements: {
          topicId,
          fromTimestamp,
          cursor,
          limit,
        },
      },
    );

    const hydratedResults = results.map(this.hydrateNetworkInferenceData);

    return setCursor(
      hydratedResults,
      (item) => item.timestamp,
      maxPageSize,
      'data',
    );
  }

  private hydrateNetworkInferenceData = (data: any): NetworkInferenceData => {
    return {
      topicId: Number(data.topic_id),
      combinedValue: String(data.combined_value),
      naiveValue: String(data.naive_value),
      timestamp: Number(data.timestamp),
    };
  };
}
