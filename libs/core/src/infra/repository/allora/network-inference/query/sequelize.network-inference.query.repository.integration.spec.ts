import {
  AlloraChainConfig,
  EAlloraChainId,
} from '@app/core/domain/allora/chain/allora-chain-config';
import { NetworkInferenceQueryRepository } from '@app/core/domain/allora/network-inference/network-inference.query.repository';
import { ContinuationTokenRepository } from '@app/core/domain/continuation-token/continuation-token.repository';
import { Sequelize } from 'sequelize-typescript';

import { sequelizeOptions } from '../../../../../../../../apps/rest-api/src/config';
import { reloadFixtures } from '../../../../../../../../test-integration/fixtures';
import { SequelizeNetworkInferenceQueryRepository } from './sequelize.network-inference.query.repository';

interface NetworkInferenceDbEntry {
  topic_id: number;
  combined_value: string;
  naive_value: string;
  timestamp: number;
}

describe('SequelizeNetworkInferenceQueryRepository', () => {
  let sequelize: Sequelize;
  let continuationTokenRepository: ContinuationTokenRepository;
  let sequelizeNetworkInferenceQueryRepository: NetworkInferenceQueryRepository;
  let alloraChainConfig: AlloraChainConfig;

  beforeEach(async () => {
    sequelize = new Sequelize({
      ...sequelizeOptions,
      dialectOptions: {
        multipleStatements: true,
      },
    });

    continuationTokenRepository = {
      get: jest.fn().mockResolvedValue(null),
      set: jest.fn().mockResolvedValue({ value: 'mocked-token' }),
    } as unknown as ContinuationTokenRepository;

    sequelizeNetworkInferenceQueryRepository =
      new SequelizeNetworkInferenceQueryRepository(
        sequelize,
        continuationTokenRepository,
      );

    alloraChainConfig = new AlloraChainConfig(EAlloraChainId.ALLORA_TESTNET_1);
    await reloadFixtures(sequelize, ['allora-network-inferences']);
  });

  afterEach(async () => {
    await sequelize.close();
  });

  describe('getNetworkInferencesByTopicId', () => {
    it('should return paginated network inference data', async () => {
      const limit = 2;
      const queryResult = await sequelize.query(
        `SELECT * FROM allora_network_inferences_allora_testnet_1 WHERE topic_id = 1 ORDER BY timestamp ASC LIMIT ${limit}`,
      );
      const expectedResult = queryResult[0] as NetworkInferenceDbEntry[];

      const result =
        await sequelizeNetworkInferenceQueryRepository.getNetworkInferencesByTopicId(
          1,
          alloraChainConfig,
          {
            continuationToken: undefined,
          },
          {},
        );

      expect(result.data).toBeDefined();
      expect(result.data.length).toBeGreaterThanOrEqual(limit);

      for (let i = 0; i < limit; i++) {
        expect(result.data[i].combinedValue).toEqual(
          expectedResult[i].combined_value,
        );
        expect(result.data[i].naiveValue).toEqual(
          expectedResult[i].naive_value,
        );
        expect(result.data[i].topicId).toEqual(expectedResult[i].topic_id);
        expect(result.data[i].timestamp).toEqual(expectedResult[i].timestamp);
      }
    });

    it('should filter results by fromTimestamp', async () => {
      const fromTimestamp = 1734360948;
      const queryResult = await sequelize.query(
        `SELECT * FROM allora_network_inferences_allora_testnet_1 WHERE topic_id = 1 AND timestamp >= ${fromTimestamp} ORDER BY timestamp ASC`,
      );
      const expectedResult = queryResult[0] as NetworkInferenceDbEntry[];

      const result =
        await sequelizeNetworkInferenceQueryRepository.getNetworkInferencesByTopicId(
          1,
          alloraChainConfig,
          {},
          { fromTimestamp },
        );

      expect(result.data).toBeDefined();
      expect(result.data.length).toBe(expectedResult.length);

      for (let i = 0; i < result.data.length; i++) {
        expect(result.data[i].timestamp).toBeGreaterThanOrEqual(fromTimestamp);
        expect(result.data[i].combinedValue).toEqual(
          expectedResult[i].combined_value,
        );
        expect(result.data[i].naiveValue).toEqual(
          expectedResult[i].naive_value,
        );
        expect(result.data[i].topicId).toEqual(expectedResult[i].topic_id);
        expect(result.data[i].timestamp).toEqual(expectedResult[i].timestamp);
      }
    });

    it('should return an empty array if no data matches the filter', async () => {
      const result =
        await sequelizeNetworkInferenceQueryRepository.getNetworkInferencesByTopicId(
          1,
          alloraChainConfig,
          {},
          { fromTimestamp: 9999999999 },
        );

      expect(result.data).toBeDefined();
      expect(result.data.length).toBe(0);
    });
  });
});
