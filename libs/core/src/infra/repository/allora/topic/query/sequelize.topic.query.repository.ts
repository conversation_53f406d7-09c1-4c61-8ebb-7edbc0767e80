import { EActorTypes } from '@app/core/app/use-case/allora/topic/topic-rewards-settled/process-topic-rewards-settled.use-case';
import { ALLO_ZERO_ADDRESS } from '@app/core/app/use-case/allora/topic/topic-worker-loss/process-topic-worker-loss.use-case';
import { AlloraChainConfig } from '@app/core/domain/allora/chain/allora-chain-config';
import {
  Topic,
  TopicTokenEndpointIdentifier,
} from '@app/core/domain/allora/topic/topic';
import { TopicLocationFilters } from '@app/core/domain/allora/topic/topic.filter';
import { TopicQueryRepository } from '@app/core/domain/allora/topic/topic.query.repository';
import {
  TopicDetails,
  TopicMetadata,
  TopicsContinuationPageOutput,
  TopicSortingOptions,
  TopicWorkerStats,
} from '@app/core/domain/allora/topic/topic-details';
import { TopicId } from '@app/core/domain/allora/topic/topic-id';
import {
  CONTINUATION_TOKEN_REPOSITORY,
  ContinuationTokenRepository,
} from '@app/core/domain/continuation-token/continuation-token.repository';
import { CursorPagination } from '@app/core/domain/value-object/cursor-pagination';
import { AlloraTopicsPostgresType } from '@app/core/infra/db/postgres-types';
import { makeScalarCursorGetAndSetMethods } from '@app/core/infra/utils/cursor.handler';
import { Inject, Injectable } from '@nestjs/common';
import { QueryTypes } from 'sequelize';
import { Sequelize } from 'sequelize-typescript';

import {
  DEFAULT_ALLORA_NETWORK_ID,
  EAlloraNetworkId,
} from '../../../../../../../../apps/rest-api/src/common/utils';
import { TraceClass } from '../../../../utils/tracing.decorator';

interface SqlTopicTokenEndpointIdentifier {
  topic_id: string;
  use_worker_not_network: boolean;
  worker_address: string;
  worker_inference_endpoint: string;
}

const toISO8601Timestamp = (value: Date | string | null): string | null => {
  if (value === null) {
    return null;
  }

  if (value instanceof Date) {
    return value.toISOString();
  }

  return value;
};

@TraceClass
@Injectable()
export class SequelizeTopicQueryRepository implements TopicQueryRepository {
  readonly TOPICS_LIMIT = 500;

  constructor(
    private readonly sequelize: Sequelize,
    @Inject(CONTINUATION_TOKEN_REPOSITORY)
    private readonly continuationTokenRepository: ContinuationTokenRepository,
  ) {}

  async _getTopicIdAndOverrideByTokenAndWindow(
    tableName: string,
    token: string,
    duration: string,
  ): Promise<TopicTokenEndpointIdentifier> {
    const query = `
    SELECT topic_id, use_worker_not_network, worker_address, worker_inference_endpoint
    FROM ${tableName}
    WHERE token = :token AND iso_duration = :duration;
    `;

    const result = await this.sequelize.query<SqlTopicTokenEndpointIdentifier>(
      query,
      {
        type: QueryTypes.SELECT,
        replacements: { token, duration },
      },
    );

    return {
      topicId: new TopicId(result[0].topic_id),
      useWorkerNotNetwork: result[0].use_worker_not_network,
      workerAddress: result[0].worker_address,
      workerInferenceEndpoint: result[0].worker_inference_endpoint,
    };
  }

  async getPricePredictionTopicIdAndOverrideByTokenAndWindow(
    token: string,
    duration: string,
  ): Promise<TopicTokenEndpointIdentifier> {
    return this._getTopicIdAndOverrideByTokenAndWindow(
      'allora_topic_token_endpoint_identifiers',
      token,
      duration,
    );
  }

  async getVolatilityPredictionTopicIdAndOverrideByTokenAndWindow(
    token: string,
    duration: string,
  ): Promise<TopicTokenEndpointIdentifier> {
    return this._getTopicIdAndOverrideByTokenAndWindow(
      'allora_topic_volatility_endpoint_identifiers',
      token,
      duration,
    );
  }

  async getReputerWorkerTopicsWithStatsByAddress(
    cosmosAddress: string,
    alloraChainConfig: AlloraChainConfig,
    sortOption: TopicSortingOptions,
  ): Promise<{ workerTopics: TopicDetails[]; reputerTopics: TopicDetails[] }> {
    const query = `
      WITH worker_participating_topics AS (
          SELECT DISTINCT topic_id, address
          FROM allora_topic_worker_ema_scores_${alloraChainConfig.networkSuffix}
          WHERE address = '${cosmosAddress}'

          UNION

          SELECT DISTINCT topic_id, address
          FROM allora_topic_worker_stats_${alloraChainConfig.networkSuffix}
          WHERE address = '${cosmosAddress}'
      ),
      reputer_participating_topics AS (
          SELECT DISTINCT topic_id, address
          FROM allora_topic_reputer_ema_scores_${alloraChainConfig.networkSuffix}
          WHERE address = '${cosmosAddress}'

          UNION

          SELECT DISTINCT topic_id, address
          FROM allora_topic_reputer_stats_${alloraChainConfig.networkSuffix}
          WHERE address = '${cosmosAddress}'
      ),
      user_participating_topics AS (
        SELECT
            wt.topic_id,
            wt.address,
            'worker' AS source
        FROM
            worker_participating_topics wt
        
        UNION ALL
        
        SELECT
            rt.topic_id,
            rt.address,
            'reputer' AS source
        FROM
            reputer_participating_topics rt
      ),
      reputer_counts AS (
        SELECT
          topic_id,
          COUNT(*) AS reputer_count
        FROM
          allora_topic_reputer_stats_${alloraChainConfig.networkSuffix}
        GROUP BY
          topic_id
      ),
      worker_counts AS (
        SELECT DISTINCT ON (topic_id)
          topic_id,
          value AS worker_count
        FROM
          allora_topic_worker_count_${alloraChainConfig.networkSuffix}
        ORDER BY
          topic_id,
          timestamp DESC NULLS LAST
      ),
      total_staked AS (
        SELECT DISTINCT ON (topic_id)
          topic_id,
          value AS total_staked
        FROM
          allora_topic_total_staked_${alloraChainConfig.networkSuffix}
        ORDER BY
          topic_id,
          timestamp DESC NULLS LAST
      ),
      total_funding AS (
        SELECT DISTINCT ON (topic_id)
          topic_id,
          value AS total_funding
        FROM
          allora_topic_emissions_${alloraChainConfig.networkSuffix}
        ORDER BY
          topic_id,
          timestamp DESC NULLS LAST
      ),
      allora_forge_competitions AS (
        SELECT
          id AS forge_competition_id,
          topic_id AS forge_competition_topic_id,
          start_date AS forge_competition_start_date,
          end_date AS forge_competition_end_date
        FROM allora_forge_competitions
      )
      SELECT
        a.source,
        a.topic_id,
        b.name,
        b.epoch_length,
        b.ground_truth_lag,
        b.loss_method,
        b.worker_submission_window,
        b.is_active,
        b.is_endorsed,
        g.forge_competition_id,
        g.forge_competition_start_date,
        g.forge_competition_end_date,
        c.reputer_count,
        d.worker_count,
        e.total_staked,
        f.total_funding,
        (NOW() - (b.epoch_length::numeric * 5 * INTERVAL '1 second')) AS updated_at
      FROM
        user_participating_topics AS a
        LEFT JOIN allora_topics_metadata_${alloraChainConfig.networkSuffix} AS b ON a.topic_id = b.id
        LEFT JOIN reputer_counts AS c ON a.topic_id = c.topic_id
        LEFT JOIN worker_counts AS d ON a.topic_id = d.topic_id
        LEFT JOIN total_staked AS e ON a.topic_id = e.topic_id
        LEFT JOIN total_funding AS f ON a.topic_id = f.topic_id
        LEFT JOIN allora_forge_competitions AS g ON a.topic_id = g.forge_competition_topic_id
      WHERE b.is_blacklisted = false
      ORDER BY ${sortOption}::numeric ASC NULLS LAST;
    `;
    const [res]: any[] = await this.sequelize.query(query);
    let workerTopics = res.filter((topic: any) => topic.source === 'worker');
    let reputerTopics = res.filter((topic: any) => topic.source === 'reputer');

    workerTopics = this.hydrateTopicsWithStats(workerTopics);
    reputerTopics = this.hydrateTopicsWithStats(reputerTopics);

    const workerReputerTopics = {
      workerTopics: workerTopics,
      reputerTopics: reputerTopics,
    };

    return workerReputerTopics;
  }

  async getUserParticipatingTopicsWithStats(
    cosmosAddress: string,
    alloraChainConfig: AlloraChainConfig,
    sortOption: TopicSortingOptions,
    pagination: CursorPagination,
  ): Promise<TopicsContinuationPageOutput> {
    const { getCursor, setCursor } = makeScalarCursorGetAndSetMethods<
      'rank',
      { rank: number }
    >(this.continuationTokenRepository, 'rank');
    const cursor = (await getCursor(pagination)) || 0;
    const limit = this.TOPICS_LIMIT + 1;
    const query = `
    WITH user_participating_topics AS (
      SELECT
        topic_id,
        address
      FROM
        allora_topic_worker_stats_${alloraChainConfig.networkSuffix}
      WHERE
        address = '${cosmosAddress}'
      GROUP BY
        topic_id,
        address
    ),
    reputer_counts AS (
      SELECT
        topic_id,
        COUNT(*) AS reputer_count
      FROM
        allora_topic_reputer_stats_${alloraChainConfig.networkSuffix}
      GROUP BY
        topic_id
    ),
    worker_counts AS (
      SELECT DISTINCT ON (topic_id)
        topic_id,
        value AS worker_count
      FROM
        allora_topic_worker_count_${alloraChainConfig.networkSuffix}
      ORDER BY
        topic_id,
        timestamp DESC NULLS LAST
    ),
    total_staked AS (
      SELECT DISTINCT ON (topic_id)
        topic_id,
        value AS total_staked
      FROM
        allora_topic_total_staked_${alloraChainConfig.networkSuffix}
      ORDER BY
        topic_id,
        timestamp DESC NULLS LAST
    ),
    total_funding AS (
      SELECT DISTINCT ON (topic_id)
        topic_id,
        value AS total_funding
      FROM
          allora_topic_emissions_${alloraChainConfig.networkSuffix}
      ORDER BY
        topic_id,
        timestamp DESC NULLS LAST
    ),
    allora_forge_competitions AS (
      SELECT
        id AS forge_competition_id,
        topic_id AS forge_competition_topic_id,
        start_date AS forge_competition_start_date,
        end_date AS forge_competition_end_date
      FROM allora_forge_competitions
    )
    SELECT *
    FROM (
      SELECT
        ROW_NUMBER() OVER (ORDER BY ${sortOption} DESC NULLS LAST) AS rank,
        a.topic_id,
        b.name,
        b.epoch_length,
        b.ground_truth_lag,
        b.loss_method,
        b.worker_submission_window,
        b.is_active,
        c.reputer_count,
        d.worker_count,
        e.total_staked,
        f.total_funding,
        b.is_endorsed,
        g.forge_competition_id,
        g.forge_competition_start_date,
        g.forge_competition_end_date,
        (NOW() - (b.epoch_length::numeric * 5 * INTERVAL '1 second')) AS updated_at
      FROM
        user_participating_topics AS a
        LEFT JOIN allora_topics_metadata_${alloraChainConfig.networkSuffix} AS b ON a.topic_id = b.id
        LEFT JOIN reputer_counts AS c ON a.topic_id = c.topic_id
        LEFT JOIN worker_counts AS d ON a.topic_id = d.topic_id
        LEFT JOIN total_staked AS e ON a.topic_id = e.topic_id
        LEFT JOIN total_funding AS f ON a.topic_id = f.topic_id
        LEFT JOIN allora_forge_competitions AS g ON a.topic_id = g.forge_competition_topic_id
      WHERE b.is_blacklisted = false
    ) AS ranked_data
    WHERE rank > ${cursor}
    ORDER BY rank ASC
    LIMIT ${limit}
    `;
    const [res]: any[] = await this.sequelize.query(query);

    const topics = this.hydrateTopicsWithStats(res);

    return setCursor<'topics', TopicDetails>(
      topics,
      (topic) => topic.rank,
      this.TOPICS_LIMIT,
      'topics',
    );
  }

  async getAllTopicsWithStats(
    alloraChainConfig: AlloraChainConfig,
    sortOption: TopicSortingOptions,
    pagination: CursorPagination,
  ): Promise<TopicsContinuationPageOutput> {
    const { getCursor, setCursor } = makeScalarCursorGetAndSetMethods<
      'rank',
      { rank: number }
    >(this.continuationTokenRepository, 'rank');
    const cursor = (await getCursor(pagination)) || 0;
    const limit = this.TOPICS_LIMIT + 1;
    const query = `
    WITH reputer_counts AS (
      SELECT
        topic_id,
        COUNT(*) AS reputer_count
      FROM
        allora_topic_reputer_stats_${alloraChainConfig.networkSuffix}
      GROUP BY
        topic_id
    ),
    worker_counts AS (
      SELECT DISTINCT ON (topic_id)
        topic_id,
        value AS worker_count
      FROM
        allora_topic_worker_count_${alloraChainConfig.networkSuffix}
      ORDER BY
        topic_id,
        timestamp DESC NULLS LAST
    ),
    total_staked AS (
      SELECT DISTINCT ON (topic_id)
        topic_id,
        value AS total_staked
      FROM
        allora_topic_total_staked_${alloraChainConfig.networkSuffix}
      ORDER BY
        topic_id,
        timestamp DESC NULLS LAST
    ),
    total_funding AS (
      SELECT DISTINCT ON (topic_id)
        topic_id,
        value AS total_funding
      FROM
        allora_topic_emissions_${alloraChainConfig.networkSuffix}
      ORDER BY
        topic_id,
        timestamp DESC NULLS LAST
    ),
    allora_forge_competitions AS (
      SELECT
        id AS forge_competition_id,
        topic_id AS forge_competition_topic_id,
        start_date AS forge_competition_start_date,
        end_date AS forge_competition_end_date
      FROM allora_forge_competitions
    )
    SELECT *
    FROM (
        SELECT
          ROW_NUMBER() OVER (ORDER BY ${sortOption}::numeric ASC NULLS LAST) as rank,
          a.id AS topic_id,
          a.name,
          a.epoch_length,
          a.ground_truth_lag,
          a.loss_method,
          a.worker_submission_window,
          a.is_active,
          b.reputer_count,
          c.worker_count,
          d.total_staked,
          e.total_funding,
          a.is_endorsed,
          f.forge_competition_id,
          f.forge_competition_start_date,
          f.forge_competition_end_date,
          (NOW() - (a.epoch_length::numeric * 5 * INTERVAL '1 second')) AS updated_at
        FROM
          allora_topics_metadata_${alloraChainConfig.networkSuffix} AS a
        LEFT JOIN
          reputer_counts AS b ON a.id = b.topic_id
        LEFT JOIN
          worker_counts AS c ON a.id = c.topic_id
        LEFT JOIN
          total_staked AS d ON a.id = d.topic_id
        LEFT JOIN
          total_funding AS e ON a.id = e.topic_id
        LEFT JOIN
          allora_forge_competitions AS f ON a.id = f.forge_competition_topic_id
        WHERE a.is_blacklisted = false
    ) ranked_data
    WHERE rank > ${cursor}
    ORDER BY rank ASC
    LIMIT ${limit}
    `;
    const [res]: any[] = await this.sequelize.query(query);

    const topics = this.hydrateTopicsWithStats(res);

    return setCursor<'topics', TopicDetails>(
      topics,
      (topic) => topic.rank,
      this.TOPICS_LIMIT,
      'topics',
    );
  }

  async getTopicWithStatsById(
    topicId: number,
    alloraChainConfig: AlloraChainConfig,
  ): Promise<TopicDetails> {
    const query = `
    WITH reputer_counts AS (
      SELECT
        topic_id,
        COUNT(*) AS reputer_count
      FROM
        allora_topic_reputer_stats_${alloraChainConfig.networkSuffix}
      WHERE
        topic_id = :topicId
      GROUP BY
        topic_id
    ),
    worker_counts AS (
      SELECT DISTINCT ON (topic_id)
        topic_id,
        value AS worker_count
      FROM
        allora_topic_worker_count_${alloraChainConfig.networkSuffix}
      WHERE
        topic_id = :topicId
      ORDER BY
        topic_id,
        timestamp DESC NULLS LAST
    ),
    total_staked AS (
      SELECT DISTINCT ON (topic_id)
        topic_id,
        value AS total_staked
      FROM
        allora_topic_total_staked_${alloraChainConfig.networkSuffix}
      WHERE
        topic_id = :topicId
      ORDER BY
        topic_id,
        timestamp DESC NULLS LAST
    ),
    total_funding AS (
      SELECT DISTINCT ON (topic_id)
        topic_id,
        value AS total_funding
      FROM
        allora_topic_emissions_${alloraChainConfig.networkSuffix}
      WHERE
        topic_id = :topicId
      ORDER BY
        topic_id,
        timestamp DESC NULLS LAST
    ),
    forge_competitions AS (
    	SELECT
          id AS forge_competition_id,
          topic_id AS forge_competition_topic_id,
          start_date AS forge_competition_start_date,
          end_date AS forge_competition_end_date
		  FROM allora_forge_competitions
		  WHERE topic_id = :topicId
    )
    SELECT
      a.id AS topic_id,
      a.name,
      a.epoch_length,
      a.ground_truth_lag,
      a.loss_method,
      a.worker_submission_window,
      a.is_active,
      a.is_endorsed,
      b.reputer_count,
      c.worker_count,
      d.total_staked,
      e.total_funding,
      f.forge_competition_id,
      f.forge_competition_start_date,
      f.forge_competition_end_date,
      (NOW() - (a.epoch_length::numeric * 5 * INTERVAL '1 second')) AS updated_at
    FROM
      allora_topics_metadata_${alloraChainConfig.networkSuffix} AS a
    LEFT JOIN
      reputer_counts AS b ON a.id = b.topic_id
    LEFT JOIN
      worker_counts AS c ON a.id = c.topic_id
    LEFT JOIN
      total_staked AS d ON a.id = d.topic_id
    LEFT JOIN
      total_funding AS e ON a.id = e.topic_id
    LEFT JOIN
      forge_competitions AS f ON a.id = f.forge_competition_topic_id
    WHERE
      a.id = :topicId
      AND a.is_blacklisted = false
    `;

    const [res]: any[] = await this.sequelize.query(query, {
      replacements: { topicId },
      type: QueryTypes.SELECT,
    });

    if (!res) {
      throw new Error(`Topic with id ${topicId} not found`);
    }

    return new TopicDetails({
      rank: 1,
      topicId: res.topic_id,
      topicName: res.name || '',
      description: null, // not provided in the query result
      epochLength: Number(res.epoch_length) || 0,
      groundTruthLag: Number(res.ground_truth_lag) || 0,
      lossMethod: String(res.loss_method),
      workerSubmissionWindow: Number(res.worker_submission_window) || 0,
      workerCount: Number(res.worker_count) || 0,
      reputerCount: Number(res.reputer_count) || 0,
      totalStakedAllo: res.total_staked || '0',
      totalEmissionsAllo: res.total_funding || '0',
      isActive: Boolean(res.is_active),
      isEndorsed: Boolean(res.is_endorsed),
      forgeCompetitionId: res.forge_competition_id || null,
      forgeCompetitionStartDate: toISO8601Timestamp(
        res.forge_competition_start_date,
      ),
      forgeCompetitionEndDate: toISO8601Timestamp(
        res.forge_competition_end_date,
      ),
      updatedAt: res.updated_at,
    });
  }

  async getAllTopics(
    alloraChainConfig: AlloraChainConfig,
  ): Promise<TopicMetadata[]> {
    const query = `
      SELECT * FROM allora_topics_metadata_${alloraChainConfig.networkSuffix} ORDER BY id ASC NULLS LAST;
    `;
    const [res]: any[] = await this.sequelize.query(query);
    const topicsMetadata = res.map((item: any) => {
      return {
        id: Number(item.id),
        creator: item.creator ? String(item.creator) : null,
        name: item.name ? String(item.name) : null,
        lossMethod: item.loss_method ? String(item.loss_method) : null,
        epochLength: item.epoch_length ? String(item.epoch_length) : null,
        groundTruthLag: item.ground_truth_lag
          ? String(item.ground_truth_lag)
          : null,
        pNorm: item.p_norm ? String(item.p_norm) : null,
        alphaRegret: item.alpha_regret ? String(item.alpha_regret) : null,
        allowNegative: Boolean(item.allow_negative),
        epsilon: item.epsilon ? String(item.epsilon) : null,
        initialRegret: item.initial_regret ? String(item.initial_regret) : null,
        workerSubmissionWindow: item.worker_submission_window
          ? String(item.worker_submission_window)
          : null,
        meritSortitionAlpha: item.merit_sortition_alpha
          ? String(item.merit_sortition_alpha)
          : null,
        activeInfererQuantile: item.active_inferer_quantile
          ? String(item.active_inferer_quantile)
          : null,
        activeForecastorQuantile: item.active_forecaster_quantile
          ? String(item.active_forecaster_quantile)
          : null,
        activeReputerQuantile: item.active_reputer_quantile
          ? String(item.active_reputer_quantile)
          : null,
        isActive: item.is_active,
        createdAt: new Date(item.created_at),
      };
    });
    return topicsMetadata;
  }

  async getCumulativeWorkerParticipation(
    topicId: number,
    alloraChainConfig: AlloraChainConfig,
  ): Promise<number> {
    const query = `
    WITH total_rewarded_workers AS (
        SELECT
            COUNT(*) AS total
        FROM
            allora_topic_worker_stats_${alloraChainConfig.networkSuffix}
        WHERE
            topic_id = ${topicId} AND total_earned IS NOT NULL
    ),
    active_workers AS (
        SELECT
            COUNT(*) AS total
        FROM
            allora_topic_worker_ema_scores_${alloraChainConfig.networkSuffix}
        WHERE
            topic_id = ${topicId} AND is_active = TRUE
    )
    SELECT
        (SELECT total FROM total_rewarded_workers) + 
        (SELECT total FROM active_workers) AS total_count;
    `;
    const res = await this.sequelize.query(query);
    return Number((res[0] as Record<string, string>[])[0].total_count);
  }

  async getLatestTopicEmissions(alloraChainConfig: AlloraChainConfig): Promise<
    {
      topicId: number;
      totalEmissionsAllo: string;
    }[]
  > {
    const query = `
    WITH ranked_rows AS (
      SELECT
        ROW_NUMBER() OVER (PARTITION BY topic_id ORDER BY timestamp DESC NULLS LAST) AS row_number,
        topic_id, 
        value
      FROM
        allora_topic_emissions_${alloraChainConfig.networkSuffix}
    )
    SELECT
      topic_id,
      value as total_emissions_allo
    FROM ranked_rows
    WHERE row_number = 1
    GROUP BY topic_id, total_emissions_allo;
    `;
    const res = await this.sequelize.query(query);
    return res[0].map((row: any) => ({
      topicId: Number(row.topic_id),
      totalEmissionsAllo: String(row.total_emissions_allo),
    }));
  }

  async getWorkerCountForAllTopics(
    alloraChainConfig: AlloraChainConfig,
  ): Promise<{ topicId: number; workerCount: number }[]> {
    const query = `
    WITH ranked_rows AS (
      SELECT
        ROW_NUMBER() OVER (PARTITION BY topic_id ORDER BY timestamp DESC NULLS LAST) AS row_number,
        topic_id, 
        value
      FROM
        allora_topic_worker_count_${alloraChainConfig.networkSuffix}
    )
    SELECT
      topic_id,
      value as worker_count
    FROM
      ranked_rows
    WHERE row_number = 1
    GROUP BY topic_id, worker_count;
    `;
    const res = await this.sequelize.query(query);
    return res[0].map((row: any) => ({
      topicId: Number(row.topic_id),
      workerCount: Number(row.worker_count),
    }));
  }

  /**
   * NOTE: we apply a 10^loss transformation to avoid negative values
   * as it is originally calculated as log(mse) on the network
   */

  async getTopicWorkerStats(
    topicId: number,
    alloraChainConfig: AlloraChainConfig,
    limit: number,
  ): Promise<TopicWorkerStats[]> {
    const oneDaySeconds = 86400;
    const threeDaysSeconds = oneDaySeconds * 3;
    const sevenDaysSeconds = oneDaySeconds * 7;
    const query = `
      WITH max_nonce_cte AS (
          SELECT 
            MAX(nonce) AS max_nonce
          FROM allora_topic_worker_ema_scores_${alloraChainConfig.networkSuffix}
          WHERE is_active = TRUE
          AND topic_id = ${topicId}
      ),
      filtered_scores AS (
          SELECT
              a.address,
              a.score,
              POWER(10, b.loss) AS loss,
              b.total_earned
          FROM
              allora_topic_worker_ema_scores_${
                alloraChainConfig.networkSuffix
              } AS a
          LEFT JOIN
              allora_topic_worker_stats_${alloraChainConfig.networkSuffix} AS b
              ON a.address = b.address AND a.topic_id = b.topic_id
          CROSS JOIN
              max_nonce_cte
          WHERE
              a.is_active = TRUE
              AND a.topic_id = ${topicId}
              AND a.nonce = max_nonce_cte.max_nonce
      ),
      worker_rewards_by_period AS (
        SELECT
          address,
          SUM(
            CASE 
              WHEN timestamp >= EXTRACT(EPOCH FROM NOW()) - ${oneDaySeconds} 
              THEN rewards 
              ELSE 0 
            END
          ) AS allo_earned_1d,
          SUM(
            CASE 
              WHEN timestamp >= EXTRACT(EPOCH FROM NOW()) - ${threeDaysSeconds} 
              THEN rewards 
              ELSE 0 
            END
          ) AS allo_earned_3d,
          SUM(
            CASE 
              WHEN timestamp >= EXTRACT(EPOCH FROM NOW()) - ${sevenDaysSeconds} 
              THEN rewards 
              ELSE 0 
            END
          ) AS allo_earned_7d
        FROM
          allora_rewards_${alloraChainConfig.networkSuffix}
        WHERE
          topic_id = ${topicId}
          AND actor_type = '${EActorTypes.INFERER.toUpperCase()}'
        GROUP BY
          address
      )
      SELECT
          fs.*,
          COALESCE(wrp.allo_earned_1d, 0) AS allo_earned_1d,
          COALESCE(wrp.allo_earned_3d, 0) AS allo_earned_3d,
          COALESCE(wrp.allo_earned_7d, 0) AS allo_earned_7d
      FROM
          filtered_scores fs
      LEFT JOIN
          worker_rewards_by_period wrp ON fs.address = wrp.address
      ORDER BY
          fs.score DESC NULLS LAST
      LIMIT ${limit};
    `;
    const res = await this.sequelize.query(query);
    return res[0].map((row: any) => ({
      address: String(row.address),
      score: row.score ? Number(row.score) : 0,
      loss: row.loss ? Number(row.loss) : 0,
      totalEarned: row.total_earned ? String(row.total_earned) : '0',
      alloEarned1d: row.allo_earned_1d ? String(row.allo_earned_1d) : '0',
      alloEarned3d: row.allo_earned_3d ? String(row.allo_earned_3d) : '0',
      alloEarned7d: row.allo_earned_7d ? String(row.allo_earned_7d) : '0',
    }));
  }

  async getTopicReputerStats(
    topicId: number,
    alloraChainConfig: AlloraChainConfig,
    limit: number,
  ): Promise<TopicWorkerStats[]> {
    const oneDaySeconds = 86400;
    const threeDaysSeconds = oneDaySeconds * 3;
    const sevenDaysSeconds = oneDaySeconds * 7;
    const query = `
      WITH max_nonce_cte AS (
          SELECT 
            MAX(nonce) AS max_nonce
          FROM allora_topic_reputer_ema_scores_${
            alloraChainConfig.networkSuffix
          }
          WHERE is_active = TRUE
          AND topic_id = ${topicId}
      ),
      filtered_scores AS (
          SELECT
              a.address,
              a.score,
              POWER(10, b.loss) AS loss,
              b.total_earned
          FROM
              allora_topic_reputer_ema_scores_${
                alloraChainConfig.networkSuffix
              } AS a
          LEFT JOIN
              allora_topic_reputer_stats_${alloraChainConfig.networkSuffix} AS b
              ON a.address = b.address AND a.topic_id = b.topic_id
          CROSS JOIN
              max_nonce_cte
          WHERE
              a.is_active = TRUE
              AND a.topic_id = ${topicId}
              AND a.nonce = max_nonce_cte.max_nonce
      ),
      reputer_rewards_by_period AS (
        SELECT
          address,
          SUM(
            CASE 
              WHEN timestamp >= EXTRACT(EPOCH FROM NOW()) - ${oneDaySeconds} 
              THEN rewards 
              ELSE 0 
            END
          ) AS allo_earned_1d,
          SUM(
            CASE 
              WHEN timestamp >= EXTRACT(EPOCH FROM NOW()) - ${threeDaysSeconds} 
              THEN rewards 
              ELSE 0 
            END
          ) AS allo_earned_3d,
          SUM(
            CASE 
              WHEN timestamp >= EXTRACT(EPOCH FROM NOW()) - ${sevenDaysSeconds} 
              THEN rewards 
              ELSE 0 
            END
          ) AS allo_earned_7d
        FROM
          allora_rewards_${alloraChainConfig.networkSuffix}
        WHERE
          topic_id = ${topicId}
          AND actor_type = '${EActorTypes.REPUTER.toUpperCase()}'
        GROUP BY
          address
      )
      SELECT
          fs.*,
          COALESCE(rrp.allo_earned_1d, 0) AS allo_earned_1d,
          COALESCE(rrp.allo_earned_3d, 0) AS allo_earned_3d,
          COALESCE(rrp.allo_earned_7d, 0) AS allo_earned_7d
      FROM
          filtered_scores fs
      LEFT JOIN
          reputer_rewards_by_period rrp ON fs.address = rrp.address
      ORDER BY
          fs.score DESC NULLS LAST
      LIMIT ${limit};
    `;
    const res = await this.sequelize.query(query);
    return res[0].map((row: any) => ({
      address: String(row.address),
      score: row.score ? Number(row.score) : 0,
      loss: row.loss ? Number(row.loss) : 0,
      totalEarned: row.total_earned ? String(row.total_earned) : '0',
      alloEarned1d: row.allo_earned_1d ? String(row.allo_earned_1d) : '0',
      alloEarned3d: row.allo_earned_3d ? String(row.allo_earned_3d) : '0',
      alloEarned7d: row.allo_earned_7d ? String(row.allo_earned_7d) : '0',
    }));
  }

  async getNetworkLossesByTopicId(
    topicId: number,
    alloraChainConfig: AlloraChainConfig,
  ): Promise<{ timestamp: string; value: string }[]> {
    const query = `
      SELECT
          timestamp,
          POWER(10, value::numeric) as value
      FROM
          allora_topic_worker_losses_${alloraChainConfig.networkSuffix}
      WHERE
          topic_id = ${topicId}
          AND address = '${ALLO_ZERO_ADDRESS}'
          AND to_timestamp(timestamp) > NOW() - INTERVAL '7 days'
      ORDER BY timestamp ASC;
    `;
    const res = await this.sequelize.query(query);
    return res[0] as { timestamp: string; value: string }[];
  }

  async getEmissionsByTopicId(
    topicId: number,
    alloraChainConfig: AlloraChainConfig,
  ): Promise<{ timestamp: string; value: string }[]> {
    const query = `
      SELECT
          timestamp,
          value
      FROM
          allora_topic_emissions_${alloraChainConfig.networkSuffix}
      WHERE
          topic_id = ${topicId}
          AND to_timestamp(timestamp) > NOW() - INTERVAL '7 days'
      ORDER BY timestamp ASC;
    `;
    const res = await this.sequelize.query(query);
    return res[0] as { timestamp: string; value: string }[];
  }

  async getTotalStakedByTopicId(
    topicId: number,
    alloraChainConfig: AlloraChainConfig,
  ): Promise<{ timestamp: string; value: string }[]> {
    const query = `
      SELECT
          timestamp,
          value
      FROM
          allora_topic_total_staked_${alloraChainConfig.networkSuffix}
      WHERE
          topic_id = ${topicId}
          AND to_timestamp(timestamp) > NOW() - INTERVAL '7 days'
      ORDER BY timestamp ASC;
    `;
    const res = await this.sequelize.query(query);
    return res[0] as { timestamp: string; value: string }[];
  }

  async getWorkerCountsByTopicId(
    topicId: number,
    alloraChainConfig: AlloraChainConfig,
  ): Promise<{ timestamp: string; value: string }[]> {
    const query = `
      SELECT
          timestamp,
          value
      FROM
          allora_topic_worker_count_${alloraChainConfig.networkSuffix}
      WHERE
          topic_id = ${topicId}
          AND to_timestamp(timestamp) > NOW() - INTERVAL '7 days'
      ORDER BY timestamp ASC;
    `;
    const res = await this.sequelize.query(query);
    return res[0] as {
      timestamp: string;
      value: string;
    }[];
  }

  async getReputerLossesByTopicId(
    topicId: number,
    addresses: string[],
    alloraChainConfig: AlloraChainConfig,
  ): Promise<
    {
      address: string;
      timestamp: string;
      value: string;
    }[]
  > {
    const addressesSqlString = addresses
      .map((address) => `'${address.toLowerCase()}'`)
      .join(',');
    const query = `
      SELECT
          address,
          timestamp,
          POWER(10, value::numeric) as value
      FROM
          allora_topic_reputer_losses_${alloraChainConfig.networkSuffix}
      WHERE
          topic_id = ${topicId}
          AND address in (${addressesSqlString})
          AND to_timestamp(timestamp) > NOW() - INTERVAL '7 days'
      ORDER BY timestamp ASC;
    `;
    const res = await this.sequelize.query(query);
    return res[0] as {
      address: string;
      timestamp: string;
      value: string;
    }[];
  }

  async getWorkerLossesByTopicId(
    topicId: number,
    addresses: string[],
    alloraChainConfig: AlloraChainConfig,
  ): Promise<
    {
      address: string;
      timestamp: string;
      value: string;
    }[]
  > {
    const addressesSqlString = addresses
      .map((address) => `'${address.toLowerCase()}'`)
      .join(',');
    const query = `
      SELECT
          address,
          timestamp,
          POWER(10, value::numeric) as value
      FROM
          allora_topic_worker_losses_${alloraChainConfig.networkSuffix}
      WHERE
          topic_id = ${topicId}
          AND address in (${addressesSqlString})
          AND to_timestamp(timestamp) > NOW() - INTERVAL '7 days'
      ORDER BY timestamp ASC;
    `;
    const res = await this.sequelize.query(query);
    return res[0] as {
      address: string;
      timestamp: string;
      value: string;
    }[];
  }

  /**
   * @param chainSlug
   * @param adapterTopicId id of topic on what is assumed to be the upshot-endorsed adapter for the given chain
   * @returns corresponding topicId on the Allora cosmos appchain
   */
  async getByAdapterChainAndTopic(
    chainSlug: string,
    adapterTopicId: TopicId,
    filters?: TopicLocationFilters,
  ): Promise<Topic | null> {
    const alloraNetworkFilter =
      filters?.alloraNetworkId || DEFAULT_ALLORA_NETWORK_ID;
    const topic = await this.sequelize.query<AlloraTopicsPostgresType>(
      `
      SELECT a.*
      FROM allora_topic_to_endorsed_adapter_topics AS t
      INNER JOIN allora_topics AS a
        ON a.id = t.allora_topic_id
      WHERE t.chain_slug = :chainSlug
        AND t.adapter_topic_id = :adapterTopicId
        AND a.allora_network_id = :alloraNetworkId
      LIMIT 1
      `,
      {
        replacements: {
          chainSlug,
          adapterTopicId: adapterTopicId.value.toString(),
          alloraNetworkId: alloraNetworkFilter,
        },
        type: QueryTypes.SELECT,
      },
    );

    if (topic.length < 1) {
      return null;
    }

    return new Topic({
      id: new TopicId(topic[0].id),
      b7sFunctionId: topic[0].b7s_function_id,
      b7sMethod: topic[0].b7s_method,
      b7sEnvVars: topic[0].b7s_env_vars,
      alloraNetworkId: topic[0].allora_network_id as EAlloraNetworkId,
    });
  }

  async getByAlloraTopicId(
    alloraTopicId: TopicId,
    filters?: TopicLocationFilters,
  ): Promise<Topic | null> {
    const alloraNetworkFilter =
      filters?.alloraNetworkId || DEFAULT_ALLORA_NETWORK_ID;
    const topic = await this.sequelize.query<AlloraTopicsPostgresType>(
      `
      SELECT a.*
      FROM allora_topics AS a
      WHERE a.id = :alloraTopicId AND a.allora_network_id = :alloraNetworkId
      LIMIT 1
      `,
      {
        replacements: {
          alloraTopicId: alloraTopicId.value.toString(),
          alloraNetworkId: alloraNetworkFilter,
        },
        type: QueryTypes.SELECT,
      },
    );

    if (topic.length < 1) {
      return null;
    }

    return new Topic({
      id: new TopicId(topic[0].id),
      b7sFunctionId: topic[0].b7s_function_id,
      b7sMethod: topic[0].b7s_method,
      b7sEnvVars: topic[0].b7s_env_vars,
      alloraNetworkId: topic[0].allora_network_id as EAlloraNetworkId,
    });
  }

  hydrateTopicsWithStats(topics: any[]): TopicDetails[] {
    return topics.map(
      (topic) =>
        new TopicDetails({
          rank: topic.rank,
          topicId: topic.topic_id,
          topicName: topic.name || '',
          description: null, // not provided in the query result
          epochLength: Number(topic.epoch_length) || 0,
          groundTruthLag: Number(topic.ground_truth_lag) || 0,
          lossMethod: String(topic.loss_method),
          workerSubmissionWindow: Number(topic.worker_submission_window) || 0,
          workerCount: Number(topic.worker_count) || 0,
          reputerCount: Number(topic.reputer_count) || 0,
          totalStakedAllo: topic.total_staked || '0',
          totalEmissionsAllo: topic.total_funding || '0',
          isActive: Boolean(topic.is_active),
          isEndorsed: Boolean(topic.is_endorsed),
          forgeCompetitionId: topic.forge_competition_id || null,
          forgeCompetitionStartDate: toISO8601Timestamp(
            topic.forge_competition_start_date,
          ),
          forgeCompetitionEndDate: toISO8601Timestamp(
            topic.forge_competition_end_date,
          ),
          updatedAt:
            toISO8601Timestamp(topic.updated_at) || new Date().toISOString(),
        }),
    );
  }
}
