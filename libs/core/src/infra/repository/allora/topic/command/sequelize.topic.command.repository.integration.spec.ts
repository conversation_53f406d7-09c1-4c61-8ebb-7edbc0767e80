import {
  AlloraChainConfig,
  EAlloraChainId,
} from '@app/core/domain/allora/chain/allora-chain-config';
import {
  ETopicReputerStakeType,
  ETopicStatsType,
} from '@app/core/domain/allora/topic/topic-details';
import { TopicReputerStake } from '@app/core/domain/allora/topic/topic-details';
import { Test, TestingModule } from '@nestjs/testing';
import { Sequelize } from 'sequelize-typescript';

import { sequelizeOptions } from '../../../../../../../../apps/rest-api/src/config';
import { SequelizeTopicCommandRepository } from './sequelize.topic.command.repository';

describe('Sequelize->AlloraTopicCommandRepository', () => {
  let alloraTopicCommandRepository: SequelizeTopicCommandRepository;
  let sequelize: Sequelize;
  let module: TestingModule;
  let alloraChainConfig: AlloraChainConfig;

  beforeAll(async () => {
    sequelize = new Sequelize({
      ...sequelizeOptions,
      dialectOptions: {
        multipleStatements: true,
      },
    });

    alloraTopicCommandRepository = new SequelizeTopicCommandRepository(
      sequelize,
    );

    alloraChainConfig = new AlloraChainConfig(EAlloraChainId.ALLORA_TESTNET_1);

    module = await Test.createTestingModule({
      providers: [],
    }).compile();
    await module.init();
  });

  beforeEach(async () => {
    // Clear the tables before each test
    await sequelize.query(
      'TRUNCATE TABLE allora_topic_reputer_stake_allora_testnet_1 RESTART IDENTITY CASCADE;',
    );
    await sequelize.query(
      'TRUNCATE TABLE allora_topic_total_staked_allora_testnet_1 RESTART IDENTITY CASCADE;',
    );
    await sequelize.query(
      `TRUNCATE TABLE allora_topic_worker_count_${alloraChainConfig.networkSuffix} RESTART IDENTITY CASCADE;`,
    );
    await sequelize.query(
      `TRUNCATE TABLE allora_rewards_${alloraChainConfig.networkSuffix} RESTART IDENTITY CASCADE;`,
    );
  });

  describe('insertTopicWorkerCount', () => {
    it('should insert topic worker count', async () => {
      const topicId = 1;
      const timestamp = **********;
      const value = 100;
      await alloraTopicCommandRepository.insertTopicWorkerCount(
        topicId,
        timestamp,
        value,
        alloraChainConfig,
      );
      const rows = await sequelize.query(`
        select * from allora_topic_worker_count_${alloraChainConfig.networkSuffix};
      `);
      expect(rows[0].length).toBeGreaterThan(0);
    });
  });

  describe('insertTopicRewards', () => {
    it('should insert topic rewards', async () => {
      const topicId = 1;
      const actorType = 'worker';
      const data = [
        { address: 'allo1abc123', value: 0.5 },
        { address: 'allo1def456', value: 1.2 },
      ];
      const blockNumber = 1000000;
      const timestamp = **********;

      await alloraTopicCommandRepository.insertTopicRewards(
        topicId,
        actorType,
        data,
        blockNumber,
        timestamp,
        alloraChainConfig,
      );

      const rows = await sequelize.query(`
        select * from allora_rewards_${alloraChainConfig.networkSuffix};
      `);
      expect(rows[0].length).toBe(2);
    });
  });

  describe('insertTopicEmissions', () => {
    it('should insert topic emissions', async () => {
      const topicId = 1;
      const timestamp = **********;
      const value = 0.999;
      await alloraTopicCommandRepository.insertTopicEmissions(
        topicId,
        timestamp,
        value,
        alloraChainConfig,
      );
      const rows = await sequelize.query(`
        select * from allora_topic_emissions_${alloraChainConfig.networkSuffix};
      `);
      expect(rows[0].length).toBeGreaterThan(0);
    });
  });

  describe('incrementTopicReputerStats', () => {
    it('should increment reputer stats', async () => {
      const topicId = 10;
      const type: ETopicStatsType = ETopicStatsType.TOTAL_EARNED;
      const data = [
        {
          address: 'allo13qvawtp0g8a2awv0z57dksl4s3jja3g60a5u39',
          value: 4.492074797e-9,
        },
        {
          address: 'allo1qnxmyayst6upmmgxrrswu4xtyccljaxvlwhp2y',
          value: 0.11320113517518637,
        },
      ];
      await alloraTopicCommandRepository.incrementTopicReputerStats(
        topicId,
        type,
        data,
        alloraChainConfig,
      );
      const rows = await sequelize.query(`
        select * from allora_topic_reputer_stats_${alloraChainConfig.networkSuffix};
      `);
      expect(rows[0].length).toBeGreaterThan(0);
    });
  });

  describe('incrementTopicWorkerStats', () => {
    it('should increment worker stats', async () => {
      const topicId = 1;
      const type: ETopicStatsType = ETopicStatsType.TOTAL_EARNED;
      const data = [
        { address: '0x1', value: 0.999 },
        { address: '0x2', value: 0.889 },
      ];
      await alloraTopicCommandRepository.incrementTopicWorkerStats(
        topicId,
        type,
        data,
        alloraChainConfig,
      );
      const rows = await sequelize.query(`
        select * from allora_topic_worker_stats_${alloraChainConfig.networkSuffix};
      `);
      expect(rows[0].length).toBeGreaterThan(0);
    });
  });

  describe('updateTopicWorkerStats', () => {
    it('should update worker stats', async () => {
      const topicId = 1;
      const type: ETopicStatsType = ETopicStatsType.LOSS;
      const data = [
        { address: '0x1', value: 0.999 },
        { address: '0x2', value: 0.889 },
      ];
      await alloraTopicCommandRepository.updateTopicWorkerStats(
        topicId,
        type,
        data,
        alloraChainConfig,
      );
      const rows = await sequelize.query(`
        select * from allora_topic_worker_stats_${alloraChainConfig.networkSuffix};
      `);
      expect(rows[0].length).toBeGreaterThan(0);
    });
  });

  describe('insertTopicWorkerLoss', () => {
    it('should insert worker losses', async () => {
      const topicId = 1;
      const timestamp = **********;
      const losses = [
        { address: '0x1', value: '0.1' },
        { address: '0x2', value: '0.2' },
      ];
      await alloraTopicCommandRepository.insertTopicWorkerLosses(
        topicId,
        timestamp,
        losses,
        alloraChainConfig,
      );
      const rows = await sequelize.query(`
        select * from allora_topic_worker_losses_${alloraChainConfig.networkSuffix};
      `);
      expect(rows[0].length).toBeGreaterThan(0);
    });
  });

  describe('insertTopicReputerLoss', () => {
    it('should insert worker losses', async () => {
      const topicId = 1;
      const timestamp = 1880000000;
      const losses = [
        { address: '0x1', value: '0.1' },
        { address: '0x2', value: '0.2' },
      ];
      await alloraTopicCommandRepository.insertTopicReputerLosses(
        topicId,
        timestamp,
        losses,
        alloraChainConfig,
      );
      const rows = await sequelize.query(`
        select * from allora_topic_reputer_losses_${alloraChainConfig.networkSuffix};
      `);
      expect(rows[0].length).toBeGreaterThan(0);
    });
  });

  describe('resetTopicEmaScores', () => {
    it('should insert worker ema scores', async () => {
      const topicId = 1;
      const scores = [
        {
          address: 'allo1k4as9a4j8z34l2a62c374pg3ax2zkfcemn7scn',
          score: '0.1',
          is_active: true,
          nonce: 1,
        },
      ];
      await alloraTopicCommandRepository.updateTopicEmaScores(
        'worker',
        topicId,
        scores,
        alloraChainConfig,
      );
      const rows = await sequelize.query(`
        select * from allora_topic_worker_ema_scores_${alloraChainConfig.networkSuffix} where topic_id = ${topicId};
      `);
      expect(rows[0].length).toBe(1);
    });
  });

  describe('updateTopicMetadata', () => {
    it('should insert topic metadata', async () => {
      const topicMetadata = {
        id: 1,
        creator: 'allo1creator',
        name: 'Test Topic',
        lossMethod: 'mse',
        epochLength: '3600',
        groundTruthLag: '86400',
        pNorm: '2',
        alphaRegret: '0.1',
        allowNegative: false,
        epsilon: '0.01',
        initialRegret: '1',
        workerSubmissionWindow: '1800',
        meritSortitionAlpha: '0.5',
        activeInfererQuantile: '0.9',
        activeForecastorQuantile: '0.8',
        activeReputerQuantile: '0.7',
        isActive: true,
      };

      await alloraTopicCommandRepository.updateTopicMetadata(
        topicMetadata,
        alloraChainConfig,
      );

      const rows = await sequelize.query(`
        SELECT * FROM allora_topics_metadata_${alloraChainConfig.networkSuffix} WHERE id = ${topicMetadata.id};
      `);

      expect(rows[0].length).toBe(1);
      const insertedMetadata = rows[0][0] as any;
      expect(insertedMetadata.id).toBe(topicMetadata.id);
      expect(insertedMetadata.creator).toBe(topicMetadata.creator);
      expect(insertedMetadata.name).toBe(topicMetadata.name);
      expect(insertedMetadata.loss_method).toBe(topicMetadata.lossMethod);
      expect(insertedMetadata.epoch_length).toBe(topicMetadata.epochLength);
      expect(insertedMetadata.ground_truth_lag).toBe(
        topicMetadata.groundTruthLag,
      );
      expect(insertedMetadata.p_norm).toBe(topicMetadata.pNorm);
      expect(insertedMetadata.alpha_regret).toBe(topicMetadata.alphaRegret);
      expect(insertedMetadata.allow_negative).toBe(topicMetadata.allowNegative);
      expect(insertedMetadata.epsilon).toBe(topicMetadata.epsilon);
      expect(insertedMetadata.initial_regret).toBe(topicMetadata.initialRegret);
      expect(insertedMetadata.worker_submission_window).toBe(
        topicMetadata.workerSubmissionWindow,
      );
      expect(insertedMetadata.merit_sortition_alpha).toBe(
        topicMetadata.meritSortitionAlpha,
      );
      expect(insertedMetadata.active_inferer_quantile).toBe(
        topicMetadata.activeInfererQuantile,
      );
      expect(insertedMetadata.active_forecaster_quantile).toBe(
        topicMetadata.activeForecastorQuantile,
      );
      expect(insertedMetadata.active_reputer_quantile).toBe(
        topicMetadata.activeReputerQuantile,
      );
    });
  });

  describe('insertReputerStakeUpdate', () => {
    it('should insert reputer stake data correctly', async () => {
      const chainConfig = new AlloraChainConfig(
        EAlloraChainId.ALLORA_TESTNET_1,
      );
      const reputerStake: TopicReputerStake = {
        type: ETopicReputerStakeType.ADD_STAKE,
        topicId: 1,
        is_delegate: false,
        sender: '0x1234567890abcdef',
        amount: 1000000000,
        reputerAddress: '0xabcdef1234567890',
        targetBlock: 12345,
        blockTimestamp: '2024-01-01T00:00:00Z',
        blockNumber: 1000000,
      };

      await alloraTopicCommandRepository.insertReputerStakeUpdate(
        reputerStake,
        chainConfig,
      );

      const [results] = await sequelize.query(`
        SELECT * FROM allora_topic_reputer_stake_allora_testnet_1
        WHERE topic_id = 1
      `);
      expect(results).toHaveLength(1);
      expect(results[0]).toMatchObject({
        type: reputerStake.type,
        topic_id: reputerStake.topicId,
        is_delegation: reputerStake.is_delegate,
        sender: reputerStake.sender,
        amount: String(reputerStake.amount),
        reputer_address: reputerStake.reputerAddress,
        target_block_height: reputerStake.targetBlock,
        block_number: reputerStake.blockNumber,
      });
    });
  });

  describe('updateTopicTotalStake', () => {
    it('should update total stake based on reputer stake records', async () => {
      // First insert some reputer stake records
      const reputerStakes: TopicReputerStake[] = [
        {
          type: ETopicReputerStakeType.ADD_STAKE,
          topicId: 1,
          is_delegate: false,
          sender: '0x1234',
          amount: 100,
          reputerAddress: '0xabcd',
          targetBlock: 12345,
          blockTimestamp: '2024-01-01T00:00:00Z',
          blockNumber: 1000000,
        },
        {
          type: ETopicReputerStakeType.REMOVE_STAKE,
          topicId: 1,
          is_delegate: false,
          sender: '0x1234',
          amount: 30,
          reputerAddress: '0xabcd',
          targetBlock: 12346,
          blockTimestamp: '2024-01-01T00:00:00Z',
          blockNumber: 1000001,
        },
      ];

      // Insert the test stake records
      for (const stake of reputerStakes) {
        await alloraTopicCommandRepository.insertReputerStakeUpdate(
          stake,
          alloraChainConfig,
        );
      }

      const timestamp = **********;
      await alloraTopicCommandRepository.updateTopicTotalStake(
        1,
        timestamp,
        alloraChainConfig,
      );

      // Verify the result
      const rows = await sequelize.query(`
        select * from allora_topic_total_staked_${alloraChainConfig.networkSuffix}
        where topic_id = 1;
      `);

      expect(rows[0].length).toBe(1);
      const result = rows[0][0] as any;
      // Net stake should be 70 (100 added - 30 removed)
      expect(result.value).toBe('70');
      expect(result.topic_id).toBe(1);
      expect(result.timestamp).toBe(timestamp);
    });
  });

  describe('deleteTimeseriesData', () => {
    it('should delete old timeseries data while preserving recent records', async () => {
      // Setup test data - insert some timeseries records
      const tableName = `allora_topic_worker_count_${alloraChainConfig.networkSuffix}`;
      const testData = [
        { topic_id: 1, timestamp: 1000, value: 10 },
        { topic_id: 2, timestamp: 1500, value: 15 },
        { topic_id: 1, timestamp: 2000, value: 20 },
        { topic_id: 2, timestamp: 2500, value: 25 },
        { topic_id: 1, timestamp: 3000, value: 30 },
      ];

      // Insert test data
      const insertQuery = `
        INSERT INTO ${tableName} (topic_id, timestamp, value)
        VALUES ${testData
          .map((d) => `(${d.topic_id}, ${d.timestamp}, ${d.value})`)
          .join(', ')};
      `;
      await sequelize.query(insertQuery);

      // Execute deleteTimeseriesData with threshold timestamp
      const thresholdTimestamp = 2000;
      await alloraTopicCommandRepository.deleteTimeseriesData(
        tableName,
        thresholdTimestamp,
      );

      // Verify results
      const [results] = await sequelize.query(`
        SELECT * FROM ${tableName}
        ORDER BY topic_id, timestamp;
      `);

      // Should keep:
      // - For topic_id 1: timestamps 2000 and 3000 (>= threshold)
      // - For topic_id 2: timestamp 2500 (>= threshold)
      expect(results).toHaveLength(3);
    });

    it('should retain the latest record when no recent data exists', async () => {
      const tableName = `allora_topic_worker_count_${alloraChainConfig.networkSuffix}`;
      const testData = [
        { topic_id: 1, timestamp: 1000, value: 10 },
        { topic_id: 1, timestamp: 2000, value: 20 },
        { topic_id: 1, timestamp: 3000, value: 30 },
      ];

      // Insert test data
      const insertQuery = `
        INSERT INTO ${tableName} (topic_id, timestamp, value)
        VALUES ${testData
          .map((d) => `(${d.topic_id}, ${d.timestamp}, ${d.value})`)
          .join(', ')};
      `;
      await sequelize.query(insertQuery);

      // Set threshold timestamp higher than all existing records
      const thresholdTimestamp = 4000;
      await alloraTopicCommandRepository.deleteTimeseriesData(
        tableName,
        thresholdTimestamp,
      );

      // Verify results
      const [results] = await sequelize.query(`
        SELECT * FROM ${tableName}
        ORDER BY topic_id, timestamp;
      `);

      // Should only keep the latest record per topic
      expect(results).toHaveLength(1);
    });
  });

  describe('updateAllTopicsTotalStake', () => {
    it('should update total stake for all topics in a single operation', async () => {
      // First insert some reputer stake records for multiple topics
      const reputerStakes: TopicReputerStake[] = [
        // Topic 1
        {
          type: ETopicReputerStakeType.ADD_STAKE,
          topicId: 1,
          is_delegate: false,
          sender: '0x1234',
          amount: 100,
          reputerAddress: '0xabcd',
          targetBlock: 12345,
          blockTimestamp: '2024-01-01T00:00:00Z',
          blockNumber: 1000000,
        },
        {
          type: ETopicReputerStakeType.REMOVE_STAKE,
          topicId: 1,
          is_delegate: false,
          sender: '0x1234',
          amount: 30,
          reputerAddress: '0xabcd',
          targetBlock: 12346,
          blockTimestamp: '2024-01-01T00:00:00Z',
          blockNumber: 1000001,
        },
        // Topic 2
        {
          type: ETopicReputerStakeType.ADD_STAKE,
          topicId: 2,
          is_delegate: false,
          sender: '0x5678',
          amount: 200,
          reputerAddress: '0xefgh',
          targetBlock: 12345,
          blockTimestamp: '2024-01-01T00:00:00Z',
          blockNumber: 1000000,
        },
        {
          type: ETopicReputerStakeType.REMOVE_STAKE,
          topicId: 2,
          is_delegate: false,
          sender: '0x5678',
          amount: 50,
          reputerAddress: '0xefgh',
          targetBlock: 12346,
          blockTimestamp: '2024-01-01T00:00:00Z',
          blockNumber: 1000001,
        },
      ];

      // Insert the test stake records
      for (const stake of reputerStakes) {
        await alloraTopicCommandRepository.insertReputerStakeUpdate(
          stake,
          alloraChainConfig,
        );
      }

      const timestamp = **********;
      await alloraTopicCommandRepository.updateAllTopicsTotalStake(
        timestamp,
        alloraChainConfig,
      );

      // Verify the results
      const rows = await sequelize.query(`
        SELECT * FROM allora_topic_total_staked_${alloraChainConfig.networkSuffix}
        ORDER BY topic_id;
      `);

      expect(rows[0].length).toBe(2); // Should have entries for both topics

      const [topic1Result, topic2Result] = rows[0] as any[];

      // Check Topic 1 result (100 added - 30 removed = 70)
      expect(topic1Result.topic_id).toBe(1);
      expect(topic1Result.timestamp).toBe(timestamp);
      expect(topic1Result.value).toBe('70');

      // Check Topic 2 result (200 added - 50 removed = 150)
      expect(topic2Result.topic_id).toBe(2);
      expect(topic2Result.timestamp).toBe(timestamp);
      expect(topic2Result.value).toBe('150');
    });
  });
});
