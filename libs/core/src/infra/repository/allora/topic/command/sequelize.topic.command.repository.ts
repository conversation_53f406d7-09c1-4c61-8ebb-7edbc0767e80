import { AlloraChainConfig } from '@app/core/domain/allora/chain/allora-chain-config';
import { TopicCommandRepository } from '@app/core/domain/allora/topic/topic.command.repository';
import {
  ETopicStatsType,
  TopicEmaScore,
  TopicMetadata,
  TopicReputerStake,
} from '@app/core/domain/allora/topic/topic-details';
import { Injectable, Logger } from '@nestjs/common';
import { QueryTypes } from 'sequelize';
import { Sequelize } from 'sequelize-typescript';

import { TraceClass } from '../../../../utils/tracing.decorator';

@TraceClass
@Injectable()
export class SequelizeTopicCommandRepository implements TopicCommandRepository {
  private readonly logger = new Logger(SequelizeTopicCommandRepository.name);

  constructor(private readonly sequelize: Sequelize) {}

  async insertTopicRewards(
    topicId: number,
    actorType: string,
    data: { address: string; value: number }[],
    blockNumber: number,
    timestamp: number,
    alloraChainConfig: AlloraChainConfig,
  ): Promise<void> {
    const tableName = `allora_rewards_${alloraChainConfig.networkSuffix}`;
    const insertString = data
      .map(
        ({ address, value }) =>
          `(${topicId}, '${address}', '${actorType}', ${value}, ${blockNumber}, ${timestamp})`,
      )
      .join(', ');
    const query = `
      INSERT INTO ${tableName} (topic_id, address, actor_type, rewards, block_number, timestamp)
      VALUES ${insertString}
    `;
    await this.sequelize.query(query, { type: QueryTypes.INSERT });
  }

  async updateAllTopicsTotalStake(
    timestamp: number,
    alloraChainConfig: AlloraChainConfig,
  ): Promise<void> {
    const reputerStakeTableName = `allora_topic_reputer_stake_${alloraChainConfig.networkSuffix}`;
    const totalStakedTableName = `allora_topic_total_staked_${alloraChainConfig.networkSuffix}`;
    const query = `
      INSERT INTO ${totalStakedTableName} (topic_id, timestamp, value)
      SELECT
          topic_id,
          ${timestamp} AS timestamp,
          SUM(
              CASE
                  WHEN type IN (
                      'ADD_STAKE',
                      'DELEGATE_STAKE'
                  ) THEN amount
                  WHEN type IN (
                      'REMOVE_STAKE',
                      'REMOVE_DELEGATE_STAKE'
                  ) THEN -amount
                  ELSE 0
              END
          )::TEXT AS value
      FROM
        ${reputerStakeTableName}
      GROUP BY
          topic_id;
    `;
    await this.sequelize.query(query, { type: QueryTypes.INSERT });
  }

  async updateTopicTotalStake(
    topicId: number,
    timestamp: number,
    alloraChainConfig: AlloraChainConfig,
  ): Promise<void> {
    const reputerStakeTableName = `allora_topic_reputer_stake_${alloraChainConfig.networkSuffix}`;
    const totalStakedTableName = `allora_topic_total_staked_${alloraChainConfig.networkSuffix}`;
    const query = `
    INSERT INTO ${totalStakedTableName} (topic_id, timestamp, value)
    SELECT
        topic_id,
        ${timestamp} AS timestamp,
        SUM(
            CASE
                WHEN type IN (
                    'ADD_STAKE',
                    'DELEGATE_STAKE'
                ) THEN amount
                WHEN type IN (
                    'REMOVE_STAKE',
                    'REMOVE_DELEGATE_STAKE'
                ) THEN -amount
                ELSE 0
            END
        )::TEXT AS value
    FROM
      ${reputerStakeTableName}
    WHERE
        topic_id = ${topicId}
    GROUP BY
        topic_id;
    `;
    await this.sequelize.query(query, { type: QueryTypes.INSERT });
  }

  async insertReputerStakeUpdate(
    reputerStake: TopicReputerStake,
    alloraChainConfig: AlloraChainConfig,
  ): Promise<void> {
    const tableName = `allora_topic_reputer_stake_${alloraChainConfig.networkSuffix}`;
    const query = `
    INSERT INTO ${tableName} (type, topic_id, is_delegation, sender, amount, reputer_address, target_block_height, block_timestamp, block_number)
    VALUES (
      '${reputerStake.type}',
      ${reputerStake.topicId},
      ${reputerStake.is_delegate},
      '${reputerStake.sender}',
      ${reputerStake.amount},
      '${reputerStake.reputerAddress}',
      ${reputerStake.targetBlock},
      '${reputerStake.blockTimestamp}',
      ${reputerStake.blockNumber}
    )
    `;
    await this.sequelize.query(query, { type: QueryTypes.INSERT });
  }

  async updateTopicEmaScores(
    type: 'worker' | 'reputer',
    topicId: number,
    scores: TopicEmaScore[],
    alloraChainConfig: AlloraChainConfig,
  ): Promise<void> {
    const tableName =
      type === 'worker'
        ? `allora_topic_worker_ema_scores_${alloraChainConfig.networkSuffix}`
        : `allora_topic_reputer_ema_scores_${alloraChainConfig.networkSuffix}`;
    const transaction = await this.sequelize.transaction();
    // insert new scores
    const rows = scores
      .map(
        ({ address, score, is_active, nonce }) =>
          `('${address}', ${topicId}, ${score}, ${is_active}, ${nonce})`,
      )
      .join(', ');
    const query = `
      INSERT INTO ${tableName} (address, topic_id, score, is_active, nonce)
      VALUES ${rows}
      ON CONFLICT (address, topic_id)
      DO UPDATE
      SET
          score = EXCLUDED.score,
          is_active = EXCLUDED.is_active,
          nonce = EXCLUDED.nonce,
          "updatedAt" = NOW();
    `;
    await this.sequelize.query(query, { type: QueryTypes.INSERT, transaction });
    await transaction.commit();
  }

  async deleteTimeseriesData(
    tableName: string,
    timestamp: number,
  ): Promise<void> {
    const query = `
    DELETE FROM ${tableName} t
    USING (
        SELECT
            topic_id,
            timestamp,
            MAX(timestamp) OVER (PARTITION BY topic_id) AS max_timestamp,
            COUNT(CASE WHEN timestamp >= ${timestamp} THEN 1 END) OVER (PARTITION BY topic_id) AS recent_count
        FROM
            ${tableName}
    ) sub
    WHERE
        t.topic_id = sub.topic_id
        AND t.timestamp = sub.timestamp
        AND (
            -- Delete old records ONLY IF there are recent records
            (sub.recent_count > 0 AND t.timestamp < ${timestamp})
            OR
            -- If no recent records exist, delete all except the latest
            (sub.recent_count = 0 AND t.timestamp != sub.max_timestamp)
        );
    `;
    await this.sequelize.query(query, { type: QueryTypes.DELETE });
  }

  async insertTopicTotalStaked(
    topicId: number,
    timestamp: number,
    value: string,
    alloraChainConfig: AlloraChainConfig,
  ): Promise<void> {
    const query = `
      INSERT INTO allora_topic_total_staked_${alloraChainConfig.networkSuffix} (topic_id, timestamp, value)
      VALUES (${topicId}, ${timestamp}, '${value}');
    `;
    await this.sequelize.query(query, { type: QueryTypes.INSERT });
  }

  async insertTopicWorkerCount(
    topicId: number,
    timestamp: number,
    value: number,
    alloraChainConfig: AlloraChainConfig,
  ): Promise<void> {
    const query = `
      INSERT INTO allora_topic_worker_count_${alloraChainConfig.networkSuffix} (topic_id, timestamp, value)
      VALUES (${topicId}, ${timestamp}, ${value});
    `;
    await this.sequelize.query(query, { type: QueryTypes.INSERT });
  }

  async insertTopicEmissions(
    topicId: number,
    timestamp: number,
    value: number,
    alloraChainConfig: AlloraChainConfig,
  ): Promise<void> {
    const tableName = `allora_topic_emissions_${alloraChainConfig.networkSuffix}`;
    const query = `
      INSERT INTO ${tableName} (topic_id, timestamp, value)
      VALUES (${topicId}, ${timestamp}, ${value})
      ON CONFLICT (topic_id, timestamp)
      DO UPDATE
      SET
        value = ${tableName}.value::numeric + EXCLUDED.value::numeric;
    `;
    await this.sequelize.query(query, {
      type: QueryTypes.INSERT,
    });
  }

  async incrementTopicReputerStats(
    topicId: number,
    type: ETopicStatsType,
    data: { address: string; value: number }[],
    alloraChainConfig: AlloraChainConfig,
  ): Promise<void> {
    // ensure only total_earned is used here
    if (type !== ETopicStatsType.TOTAL_EARNED) {
      throw new Error('Invalid stats type');
    }
    const tableName = `allora_topic_reputer_stats_${alloraChainConfig.networkSuffix}`;
    const insertString = data
      .map(({ address, value }) => `('${address}', ${topicId}, ${value})`)
      .join(', ');
    const query = `
      INSERT INTO ${tableName} (address, topic_id, ${type})
      VALUES ${insertString}
      ON CONFLICT (topic_id, address)
      DO UPDATE
      SET 
          ${type} = COALESCE(${tableName}.${type}::numeric, 0) + EXCLUDED.${type}::numeric,
          updated_at = NOW();
    `;
    await this.sequelize.query(query, {
      type: QueryTypes.UPDATE,
    });
  }

  async incrementTopicWorkerStats(
    topicId: number,
    type: ETopicStatsType,
    data: { address: string; value: number }[],
    alloraChainConfig: AlloraChainConfig,
  ): Promise<void> {
    // ensure only total_earned is used here
    if (type !== ETopicStatsType.TOTAL_EARNED) {
      throw new Error('Invalid stats type');
    }

    const tableName = `allora_topic_worker_stats_${alloraChainConfig.networkSuffix}`;
    const insertString = data
      .map(({ address, value }) => `('${address}', ${topicId}, ${value})`)
      .join(', ');
    const query = `
      INSERT INTO ${tableName} (address, topic_id, ${type})
      VALUES ${insertString}
      ON CONFLICT (topic_id, address)
      DO UPDATE
      SET 
          ${type} = COALESCE(${tableName}.${type}::numeric, 0) + EXCLUDED.${type}::numeric,
          updated_at = NOW();
    `;
    await this.sequelize.query(query, {
      type: QueryTypes.UPDATE,
    });
  }

  async updateTopicReputerStats(
    topicId: number,
    type: ETopicStatsType,
    data: { address: string; value: number }[],
    alloraChainConfig: AlloraChainConfig,
  ): Promise<void> {
    const insertString = data
      .map(({ address, value }) => `('${address}', ${topicId}, ${value})`)
      .join(', ');
    const query = `
      INSERT INTO allora_topic_reputer_stats_${alloraChainConfig.networkSuffix} (address, topic_id, ${type})
      VALUES ${insertString}
      ON CONFLICT (topic_id, address)
      DO UPDATE
      SET 
          ${type} = EXCLUDED.${type},
          updated_at = NOW();
    `;
    await this.sequelize.query(query, {
      type: QueryTypes.INSERT,
    });
  }

  async updateTopicWorkerStats(
    topicId: number,
    type: ETopicStatsType,
    data: { address: string; value: number }[],
    alloraChainConfig: AlloraChainConfig,
  ): Promise<void> {
    const insertString = data
      .map(({ address, value }) => `('${address}', ${topicId}, ${value})`)
      .join(', ');
    const query = `
      INSERT INTO allora_topic_worker_stats_${alloraChainConfig.networkSuffix} (address, topic_id, ${type})
      VALUES ${insertString}
      ON CONFLICT (topic_id, address)
      DO UPDATE
      SET 
          ${type} = EXCLUDED.${type},
          updated_at = NOW();
    `;
    await this.sequelize.query(query, {
      type: QueryTypes.UPDATE,
    });
  }

  async insertTopicWorkerLosses(
    topicId: number,
    timestamp: number,
    losses: { address: string; value: string }[],
    alloraChainConfig: AlloraChainConfig,
  ): Promise<void> {
    // insert batch all losses as separate rows for timeseries
    const rows = losses
      .map(
        ({ address, value }) =>
          `(${topicId}, '${address}', ${timestamp}, '${value}')`,
      )
      .join(', ');
    const query = `
      INSERT INTO allora_topic_worker_losses_${alloraChainConfig.networkSuffix} (topic_id, address, timestamp, value)
      VALUES ${rows}
    `;
    await this.sequelize.query(query, {
      type: QueryTypes.INSERT,
    });
  }

  async insertTopicReputerLosses(
    topicId: number,
    timestamp: number,
    losses: { address: string; value: string }[],
    alloraChainConfig: AlloraChainConfig,
  ): Promise<void> {
    // insert batch all losses as separate rows for timeseries
    const rows = losses
      .map(
        ({ address, value }) =>
          `(${topicId}, '${address}', ${timestamp}, '${value}')`,
      )
      .join(', ');
    const query = `
      INSERT INTO allora_topic_reputer_losses_${alloraChainConfig.networkSuffix} (topic_id, address, timestamp, value)
      VALUES ${rows}
    `;
    await this.sequelize.query(query, {
      type: QueryTypes.INSERT,
    });
  }

  async updateTopicMetadata(
    topicMetadata: TopicMetadata,
    alloraChainConfig: AlloraChainConfig,
  ): Promise<void> {
    const query = `
      INSERT INTO allora_topics_metadata_${alloraChainConfig.networkSuffix} (
        id, 
        creator, 
        name, 
        loss_method, 
        epoch_length, 
        ground_truth_lag, 
        p_norm, 
        alpha_regret, 
        allow_negative, 
        epsilon, 
        initial_regret, 
        worker_submission_window, 
        merit_sortition_alpha, 
        active_inferer_quantile, 
        active_forecaster_quantile, 
        active_reputer_quantile,
        is_active
      )
      VALUES (
        :id, 
        :creator, 
        :name, 
        :lossMethod, 
        :epochLength, 
        :groundTruthLag, 
        :pNorm, 
        :alphaRegret, 
        :allowNegative, 
        :epsilon, 
        :initialRegret, 
        :workerSubmissionWindow, 
        :meritSortitionAlpha, 
        :activeInfererQuantile, 
        :activeForecastorQuantile,
        :activeReputerQuantile,
        :isActive
      )
      ON CONFLICT (id) DO UPDATE SET
        creator = EXCLUDED.creator,
        name = EXCLUDED.name,
        loss_method = EXCLUDED.loss_method,
        epoch_length = EXCLUDED.epoch_length,
        ground_truth_lag = EXCLUDED.ground_truth_lag,
        p_norm = EXCLUDED.p_norm,
        alpha_regret = EXCLUDED.alpha_regret,
        allow_negative = EXCLUDED.allow_negative,
        epsilon = EXCLUDED.epsilon,
        initial_regret = EXCLUDED.initial_regret,
        worker_submission_window = EXCLUDED.worker_submission_window,
        merit_sortition_alpha = EXCLUDED.merit_sortition_alpha,
        active_inferer_quantile = EXCLUDED.active_inferer_quantile,
        active_forecaster_quantile = EXCLUDED.active_forecaster_quantile,
        active_reputer_quantile = EXCLUDED.active_reputer_quantile,
        is_active = EXCLUDED.is_active;
    `;
    await this.sequelize.query(query, {
      replacements: {
        id: topicMetadata.id,
        creator: topicMetadata.creator,
        name: topicMetadata.name,
        lossMethod: topicMetadata.lossMethod,
        epochLength: topicMetadata.epochLength,
        groundTruthLag: topicMetadata.groundTruthLag,
        pNorm: topicMetadata.pNorm,
        alphaRegret: topicMetadata.alphaRegret,
        allowNegative: topicMetadata.allowNegative,
        epsilon: topicMetadata.epsilon,
        initialRegret: topicMetadata.initialRegret,
        workerSubmissionWindow: topicMetadata.workerSubmissionWindow,
        meritSortitionAlpha: topicMetadata.meritSortitionAlpha,
        activeInfererQuantile: topicMetadata.activeInfererQuantile,
        activeForecastorQuantile: topicMetadata.activeForecastorQuantile,
        activeReputerQuantile: topicMetadata.activeReputerQuantile,
        isActive: topicMetadata.isActive,
      },
      type: QueryTypes.UPSERT,
    });
  }
}
