import { AlloraChainConfig } from '@app/core/domain/allora/chain/allora-chain-config';
import { GroundTruthData } from '@app/core/domain/allora/ground-truth/ground-truth';
import { GroundTruthCommandRepository } from '@app/core/domain/allora/ground-truth/ground-truth.command.repository';
import { Injectable, Logger } from '@nestjs/common';
import { QueryTypes } from 'sequelize';
import { Sequelize } from 'sequelize-typescript';

import { TraceClass } from '../../../../utils/tracing.decorator';

@TraceClass
@Injectable()
export class SequelizeGroundTruthCommandRepository
  implements GroundTruthCommandRepository
{
  private readonly logger = new Logger(
    SequelizeGroundTruthCommandRepository.name,
  );

  constructor(private readonly sequelize: Sequelize) {}

  async insertGroundTruth(
    groundTruth: GroundTruthData,
    alloraChainConfig: AlloraChainConfig,
  ): Promise<void> {
    const transaction = await this.sequelize.transaction();
    try {
      const tableName = alloraChainConfig.networkSpecificTableName(
        'allora_network_ground_truth',
      );
      await this.sequelize.query(
        `
        INSERT INTO ${tableName} (
          topic_id,
          ground_truth_value,
          timestamp,
          epoch_last_ended_height,
          reputation_timestamp
        ) VALUES (
          :topicId,
          :groundTruthValue,
          :timestamp,
          :epochLastEndedHeight,
          :reputationTimestamp
        )
        `,
        {
          type: QueryTypes.INSERT,
          replacements: {
            topicId: groundTruth.topicId,
            groundTruthValue: groundTruth.gtValue,
            timestamp: groundTruth.timestamp,
            epochLastEndedHeight: groundTruth.epochLastEndedHeight,
            reputationTimestamp: groundTruth.reputationTimestamp,
          },
          transaction,
        },
      );

      await transaction.commit();
    } catch (error) {
      await transaction.rollback();
      this.logger.error('Error inserting ground truth:', error.message);
      throw error;
    }
  }
}
