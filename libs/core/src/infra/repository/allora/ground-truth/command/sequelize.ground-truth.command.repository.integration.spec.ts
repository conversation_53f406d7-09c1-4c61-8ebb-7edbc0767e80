import {
  AlloraChainConfig,
  EAlloraChainId,
} from '@app/core/domain/allora/chain/allora-chain-config';
import { GroundTruthData } from '@app/core/domain/allora/ground-truth/ground-truth';
import { Test, TestingModule } from '@nestjs/testing';
import { QueryTypes } from 'sequelize';
import { Sequelize } from 'sequelize-typescript';

import { sequelizeOptions } from '../../../../../../../../apps/rest-api/src/config';
import { SequelizeGroundTruthCommandRepository } from './sequelize.ground-truth.command.repository';

describe('SequelizeGroundTruthCommandRepository', () => {
  let repository: SequelizeGroundTruthCommandRepository;
  let sequelize: Sequelize;
  let module: TestingModule;
  let alloraChainConfig: AlloraChainConfig;

  beforeAll(async () => {
    sequelize = new Sequelize({
      ...sequelizeOptions,
      dialectOptions: {
        multipleStatements: true,
      },
    });

    repository = new SequelizeGroundTruthCommandRepository(sequelize);

    alloraChainConfig = new AlloraChainConfig(EAlloraChainId.ALLORA_TESTNET_1);

    module = await Test.createTestingModule({
      providers: [],
    }).compile();
    await module.init();
  });

  beforeEach(async () => {
    // Clear the test table before each test
    await sequelize.query(
      'TRUNCATE TABLE allora_network_ground_truth_allora_testnet_1 RESTART IDENTITY CASCADE;',
    );
  });

  afterAll(async () => {
    await sequelize.close();
  });

  describe('insertGroundTruth', () => {
    it('should insert a ground truth successfully', async () => {
      const groundTruthData: GroundTruthData = {
        topicId: 47,
        gtValue: JSON.stringify({
          lastPrice: 107891.86,
          timestamp: '2024-12-16T17:09:08.000Z',
        }),
        timestamp: **********,
        epochLastEndedHeight: 1000000,
        reputationTimestamp: **********,
      };

      await repository.insertGroundTruth(groundTruthData, alloraChainConfig);

      // Verify the record was inserted
      const result = await sequelize.query(
        'SELECT * FROM allora_network_ground_truth_allora_testnet_1 WHERE topic_id = :topicId',
        {
          type: QueryTypes.SELECT,
          replacements: { topicId: 47 },
        },
      );

      expect(result).toHaveLength(1);
      expect(result[0]).toMatchObject({
        topic_id: 47,
        ground_truth_value: JSON.stringify({
          lastPrice: 107891.86,
          timestamp: '2024-12-16T17:09:08.000Z',
        }),
        timestamp: **********,
      });
    });
    it('should handle multiple ground truth entries for the same topic', async () => {
      const groundTruthData1: GroundTruthData = {
        topicId: 47,
        gtValue: JSON.stringify({
          lastPrice: 107891.86,
          timestamp: '2024-12-16T17:09:08.000Z',
        }),
        timestamp: **********,
        epochLastEndedHeight: 1000000,
        reputationTimestamp: **********,
      };

      const groundTruthData2: GroundTruthData = {
        topicId: 47,
        gtValue: JSON.stringify({
          lastPrice: 108000,
          timestamp: '2024-12-16T17:10:08.000Z',
        }),
        timestamp: 1734360949,
        epochLastEndedHeight: 1000000,
        reputationTimestamp: **********,
      };

      await repository.insertGroundTruth(groundTruthData1, alloraChainConfig);
      await repository.insertGroundTruth(groundTruthData2, alloraChainConfig);

      // Verify both records were inserted
      const result = await sequelize.query(
        'SELECT * FROM allora_network_ground_truth_allora_testnet_1 WHERE topic_id = :topicId ORDER BY timestamp',
        {
          type: QueryTypes.SELECT,
          replacements: { topicId: 47 },
        },
      );

      expect(result).toHaveLength(2);
      expect(result[0]).toMatchObject({
        ground_truth_value: JSON.stringify({
          lastPrice: 107891.86,
          timestamp: '2024-12-16T17:09:08.000Z',
        }),
        timestamp: **********,
      });
      expect(result[1]).toMatchObject({
        ground_truth_value: JSON.stringify({
          lastPrice: 108000,
          timestamp: '2024-12-16T17:10:08.000Z',
        }),
        timestamp: 1734360949,
      });
    });

    it('should handle different topic IDs', async () => {
      const groundTruthData1: GroundTruthData = {
        topicId: 1,
        gtValue: JSON.stringify({
          lastPrice: 2000.5,
          timestamp: '2024-12-16T17:09:08.000Z',
        }),
        timestamp: **********,
        epochLastEndedHeight: 1000000,
        reputationTimestamp: **********,
      };

      const groundTruthData2: GroundTruthData = {
        topicId: 3,
        gtValue: JSON.stringify({
          lastPrice: 50000.25,
          timestamp: '2024-12-16T17:09:08.000Z',
        }),
        timestamp: **********,
        epochLastEndedHeight: 1000000,
        reputationTimestamp: **********,
      };

      await repository.insertGroundTruth(groundTruthData1, alloraChainConfig);
      await repository.insertGroundTruth(groundTruthData2, alloraChainConfig);

      // Verify both records were inserted with correct topic IDs
      const result1 = await sequelize.query(
        'SELECT * FROM allora_network_ground_truth_allora_testnet_1 WHERE topic_id = :topicId',
        {
          type: QueryTypes.SELECT,
          replacements: { topicId: 1 },
        },
      );

      const result3 = await sequelize.query(
        'SELECT * FROM allora_network_ground_truth_allora_testnet_1 WHERE topic_id = :topicId',
        {
          type: QueryTypes.SELECT,
          replacements: { topicId: 3 },
        },
      );

      expect(result1).toHaveLength(1);
      expect(result1[0]).toMatchObject({
        topic_id: 1,
        ground_truth_value: JSON.stringify({
          lastPrice: 2000.5,
          timestamp: '2024-12-16T17:09:08.000Z',
        }),
      });

      expect(result3).toHaveLength(1);
      expect(result3[0]).toMatchObject({
        topic_id: 3,
        ground_truth_value: JSON.stringify({
          lastPrice: 50000.25,
          timestamp: '2024-12-16T17:09:08.000Z',
        }),
      });
    });

    it('should rollback on database errors', async () => {
      const groundTruthData: GroundTruthData = {
        topicId: 47,
        gtValue: JSON.stringify({
          lastPrice: 107891.86,
          timestamp: '2024-12-16T17:09:08.000Z',
        }),
        timestamp: **********,
        epochLastEndedHeight: 1000000,
        reputationTimestamp: **********,
      };

      const invalidChainConfig = {
        networkSpecificTableName: (tableName: string) => 'invalid_table_name',
      } as unknown as AlloraChainConfig;

      await expect(
        repository.insertGroundTruth(groundTruthData, invalidChainConfig),
      ).rejects.toThrow();

      // Verify no records were inserted in any table
      const testnetResult = await sequelize.query(
        'SELECT COUNT(*) as count FROM allora_network_ground_truth_allora_testnet_1',
        {
          type: QueryTypes.SELECT,
        },
      );

      expect(testnetResult[0]).toMatchObject({ count: '0' });
    });
  });
});
