import { AlloraChainConfig } from '@app/core/domain/allora/chain/allora-chain-config';
import {
  GroundTruthContinuationPageOutput,
  GroundTruthData,
} from '@app/core/domain/allora/ground-truth/ground-truth';
import { GroundTruthQueryRepository } from '@app/core/domain/allora/ground-truth/ground-truth.query.repository';
import {
  CONTINUATION_TOKEN_REPOSITORY,
  ContinuationTokenRepository,
} from '@app/core/domain/continuation-token/continuation-token.repository';
import { CursorPagination } from '@app/core/domain/value-object/cursor-pagination';
import { makeScalarCursorGetAndSetMethods } from '@app/core/infra/utils/cursor.handler';
import { Inject, Injectable } from '@nestjs/common';
import { QueryTypes } from 'sequelize';
import { Sequelize } from 'sequelize-typescript';

@Injectable()
export class SequelizeGroundTruthQueryRepository
  implements GroundTruthQueryRepository
{
  constructor(
    private readonly sequelize: Sequelize,
    @Inject(CONTINUATION_TOKEN_REPOSITORY)
    private readonly continuationTokenRepository: ContinuationTokenRepository,
  ) {}

  async getLatestGroundTruthByTopicId(
    topicId: number,
    alloraChainConfig: AlloraChainConfig,
  ): Promise<GroundTruthData | null> {
    const tableName = alloraChainConfig.networkSpecificTableName(
      'allora_network_ground_truth',
    );

    const results = await this.sequelize.query(
      `SELECT * FROM ${tableName} WHERE topic_id = :topicId ORDER BY epoch_last_ended_height DESC LIMIT 1`,
      {
        type: QueryTypes.SELECT,
        replacements: { topicId },
      },
    );

    return results.length > 0 ? this.hydrateGroundTruthData(results[0]) : null;
  }

  async getGroundTruthsByTopicId(
    topicId: number,
    alloraChainConfig: AlloraChainConfig,
    pagination: CursorPagination,
    filters: { fromTimestamp?: number },
  ): Promise<GroundTruthContinuationPageOutput> {
    const maxPageSize = 10000;
    const { getCursor, setCursor } = makeScalarCursorGetAndSetMethods<
      'reputationTimestamp',
      { reputationTimestamp: number }
    >(this.continuationTokenRepository, 'reputationTimestamp');

    const cursor = (await getCursor(pagination)) || 0;
    const limit = maxPageSize + 1;

    const tableName = alloraChainConfig.networkSpecificTableName(
      'allora_network_ground_truth',
    );

    const fromTimestamp = filters.fromTimestamp || 0;
    const results = await this.sequelize.query<GroundTruthData>(
      `
      SELECT * FROM ${tableName} 
      WHERE 
        topic_id = :topicId AND 
        reputation_timestamp >= :fromTimestamp AND
        reputation_timestamp > :cursor
      ORDER BY reputation_timestamp ASC
      LIMIT :limit
      `,
      {
        type: QueryTypes.SELECT,
        replacements: {
          topicId,
          fromTimestamp,
          cursor,
          limit,
        },
      },
    );

    const hydratedResults = results.map(this.hydrateGroundTruthData);

    return setCursor(
      hydratedResults,
      (item) => item.reputationTimestamp,
      maxPageSize,
      'data',
    );
  }

  private hydrateGroundTruthData = (data: any): GroundTruthData => {
    return {
      topicId: Number(data.topic_id),
      gtValue: String(data.ground_truth_value),
      timestamp: Number(data.timestamp),
      epochLastEndedHeight: Number(data.epoch_last_ended_height),
      reputationTimestamp: Number(data.reputation_timestamp),
    };
  };
}
