import {
  AlloraChainConfig,
  EAlloraChainId,
} from '@app/core/domain/allora/chain/allora-chain-config';
import { GroundTruthQueryRepository } from '@app/core/domain/allora/ground-truth/ground-truth.query.repository';
import { ContinuationTokenRepository } from '@app/core/domain/continuation-token/continuation-token.repository';
import { Sequelize } from 'sequelize-typescript';

import { sequelizeOptions } from '../../../../../../../../apps/rest-api/src/config';
import { reloadFixtures } from '../../../../../../../../test-integration/fixtures';
import { SequelizeGroundTruthQueryRepository } from './sequelize.ground-truth.query.repository';

interface GroundTruthDbEntry {
  topic_id: number;
  ground_truth_value: string;
  epoch_last_ended_height: number;
  timestamp: number;
  reputation_timestamp: number;
}

describe('SequelizeGroundTruthQueryRepository', () => {
  let sequelize: Sequelize;
  let continuationTokenRepository: ContinuationTokenRepository;
  let sequelizeGroundTruthQueryRepository: GroundTruthQueryRepository;
  let alloraChainConfig: AlloraChainConfig;

  beforeEach(async () => {
    sequelize = new Sequelize({
      ...sequelizeOptions,
      dialectOptions: {
        multipleStatements: true,
      },
    });

    continuationTokenRepository = {
      get: jest.fn().mockResolvedValue(null),
      set: jest.fn().mockResolvedValue({ value: 'mocked-token' }),
    } as unknown as ContinuationTokenRepository;

    sequelizeGroundTruthQueryRepository =
      new SequelizeGroundTruthQueryRepository(
        sequelize,
        continuationTokenRepository,
      );

    alloraChainConfig = new AlloraChainConfig(EAlloraChainId.ALLORA_TESTNET_1);
    await reloadFixtures(sequelize, ['allora-ground-truth']);
  });

  afterEach(async () => {
    await sequelize.close();
  });

  describe('getLatestGroundTruthByTopicId', () => {
    it('should return the latest ground truth record based on epoch_last_ended_height', async () => {
      // get latest ground truth record for topic 1
      const queryResult = await sequelize.query(
        `SELECT * FROM allora_network_ground_truth_allora_testnet_1 WHERE topic_id = 1 ORDER BY epoch_last_ended_height DESC LIMIT 1`,
      );
      const expectedResult = queryResult[0][0] as GroundTruthDbEntry;

      const latest =
        await sequelizeGroundTruthQueryRepository.getLatestGroundTruthByTopicId(
          1,
          alloraChainConfig,
        );

      expect(latest).toBeDefined();
      expect(latest!.topicId).toEqual(expectedResult.topic_id);
      expect(latest!.gtValue).toEqual(expectedResult.ground_truth_value);
      expect(latest!.timestamp).toEqual(expectedResult.timestamp);
      expect(latest!.epochLastEndedHeight).toEqual(
        expectedResult.epoch_last_ended_height,
      );
      expect(latest!.reputationTimestamp).toEqual(
        expectedResult.reputation_timestamp,
      );
    });

    it('should return null if no record is found for the topic', async () => {
      const latest =
        await sequelizeGroundTruthQueryRepository.getLatestGroundTruthByTopicId(
          999,
          alloraChainConfig,
        );
      expect(latest).toBeNull();
    });
  });

  describe('getGroundTruthsByTopicId', () => {
    it('should return paginated ground truth data', async () => {
      const limit = 2;
      const queryResult = await sequelize.query(
        `SELECT * FROM allora_network_ground_truth_allora_testnet_1 WHERE topic_id = 1 ORDER BY reputation_timestamp ASC LIMIT ${limit}`,
      );
      const expectedResult = queryResult[0] as GroundTruthDbEntry[];

      const result =
        await sequelizeGroundTruthQueryRepository.getGroundTruthsByTopicId(
          1,
          alloraChainConfig,
          {
            continuationToken: undefined,
          },
          {},
        );

      expect(result.data).toBeDefined();
      expect(result.data.length).toBeGreaterThanOrEqual(limit);

      for (let i = 0; i < limit; i++) {
        expect(result.data[i].gtValue).toEqual(
          expectedResult[i].ground_truth_value,
        );
        expect(result.data[i].topicId).toEqual(expectedResult[i].topic_id);
        expect(result.data[i].timestamp).toEqual(expectedResult[i].timestamp);
        expect(result.data[i].epochLastEndedHeight).toEqual(
          expectedResult[i].epoch_last_ended_height,
        );
        expect(result.data[i].reputationTimestamp).toEqual(
          expectedResult[i].reputation_timestamp,
        );
      }
    });

    it('should filter results by fromTimestamp', async () => {
      const fromTimestamp = 1734360948;
      const queryResult = await sequelize.query(
        `SELECT * FROM allora_network_ground_truth_allora_testnet_1 WHERE topic_id = 1 AND reputation_timestamp >= ${fromTimestamp} ORDER BY reputation_timestamp ASC`,
      );
      const expectedResult = queryResult[0] as GroundTruthDbEntry[];

      const result =
        await sequelizeGroundTruthQueryRepository.getGroundTruthsByTopicId(
          1,
          alloraChainConfig,
          {},
          { fromTimestamp },
        );

      expect(result.data).toBeDefined();
      expect(result.data.length).toBe(expectedResult.length);

      for (let i = 0; i < result.data.length; i++) {
        expect(result.data[i].reputationTimestamp).toBeGreaterThanOrEqual(
          fromTimestamp,
        );
        expect(result.data[i].gtValue).toEqual(
          expectedResult[i].ground_truth_value,
        );
        expect(result.data[i].topicId).toEqual(expectedResult[i].topic_id);
        expect(result.data[i].timestamp).toEqual(expectedResult[i].timestamp);
        expect(result.data[i].epochLastEndedHeight).toEqual(
          expectedResult[i].epoch_last_ended_height,
        );
        expect(result.data[i].reputationTimestamp).toEqual(
          expectedResult[i].reputation_timestamp,
        );
      }
    });

    it('should return an empty array if no data matches the filter', async () => {
      const result =
        await sequelizeGroundTruthQueryRepository.getGroundTruthsByTopicId(
          1,
          alloraChainConfig,
          {},
          { fromTimestamp: 9999999999 },
        );

      expect(result.data).toBeDefined();
      expect(result.data.length).toBe(0);
    });
  });
});
