import {
  AlloraChainConfig,
  EAlloraChainId,
} from '@app/core/domain/allora/chain/allora-chain-config';
import { Inject, Injectable, Optional } from '@nestjs/common';
import axios, { AxiosRequestConfig } from 'axios';
import * as _ from 'lodash';

import {
  PROMETHEUS_METRICS_LOGGER,
  PrometheusMetricsLogger,
} from '../prometheus/prometheus-metrics.logger';

export const ALLORA_API_CLIENT = Symbol('ALLORA_API_CLIENT');

enum HTTPMethod {
  GET = 'GET',
  POST = 'POST',
  PUT = 'PUT',
  DELETE = 'DELETE',
}

export interface AlloraTopicDetails {
  topic: {
    id: number;
    creator: string;
    metadata: string;
    loss_method: string;
    epoch_last_ended: number;
    epoch_length: number;
    ground_truth_lag: number;
    p_norm: number;
    alpha_regret: number;
    allow_negative: boolean;
    epsilon: number;
    initial_regret: number;
    worker_submission_window: number;
    merit_sortition_alpha: number;
    active_inferer_quantile: number;
    active_forecaster_quantile: number;
    active_reputer_quantile: number;
  };
  weight: number;
  effective_revenue: number;
}

export interface AlloraBlockDetails {
  block: {
    header: {
      height: number;
      time: string;
    };
  };
}

export interface AlloraTopicLastReputerCommitInfo {
  last_commit: {
    block_height: number;
    nonce: {
      block_height: number;
    };
  };
}

const DEFAULT_ALLORA_CHAIN_CONFIG = new AlloraChainConfig(
  EAlloraChainId.ALLORA_TESTNET_1,
);

@Injectable()
export class AlloraApiClient {
  private readonly baseApiUrl: string;

  constructor(
    @Inject(PROMETHEUS_METRICS_LOGGER)
    private readonly prometheusMetricsLogger: PrometheusMetricsLogger,
    @Optional()
    private readonly alloraChainConfig: AlloraChainConfig = DEFAULT_ALLORA_CHAIN_CONFIG,
  ) {
    if (!this.alloraChainConfig.rpcUrl) {
      throw new Error(
        `RPC URL is not set for chain id: ${this.alloraChainConfig.chainId}`,
      );
    }

    this.baseApiUrl = this.alloraChainConfig.rpcUrl;
  }

  async getLatestNetworkInferencesOutlierResistant(
    topicId: string,
    reqConfig?: AxiosRequestConfig<any>,
  ) {
    const data = await this.executeRequest(
      HTTPMethod.GET,
      `/emissions/v9/latest_network_inferences_outlier_resistant/${topicId}`,
      reqConfig,
      ['network_inferences.combined_value'],
    );
    return data;
  }

  async getLatestNetworkInferences(
    topicId: string,
    reqConfig?: AxiosRequestConfig<any>,
  ) {
    const data = await this.executeRequest(
      HTTPMethod.GET,
      `/emissions/v9/latest_network_inferences/${topicId}`,
      reqConfig,
      ['network_inferences.combined_value'],
    );
    return data;
  }

  async getAlloBalanceFromAddress(
    address: string,
    reqConfig?: AxiosRequestConfig<any>,
  ) {
    const data = await this.executeRequest(
      HTTPMethod.GET,
      `/cosmos/bank/v1beta1/balances/${address}`,
      reqConfig,
      ['balances[0].amount'],
    );
    return data;
  }

  async getTopicDetails(
    topicId: number,
    reqConfig?: AxiosRequestConfig<any>,
  ): Promise<AlloraTopicDetails> {
    const data = await this.executeRequest(
      HTTPMethod.GET,
      `/emissions/v9/topics/${topicId}`,
      reqConfig,
    );

    return this.hydrateTopicDetails(data);
  }

  private hydrateTopicDetails(data: any): AlloraTopicDetails {
    return {
      topic: {
        id: Number(data.topic.id),
        creator: data.topic.creator,
        metadata: data.topic.metadata,
        loss_method: data.topic.loss_method,
        epoch_last_ended: Number(data.topic.epoch_last_ended),
        epoch_length: Number(data.topic.epoch_length),
        ground_truth_lag: Number(data.topic.ground_truth_lag),
        p_norm: Number(data.topic.p_norm),
        alpha_regret: Number(data.topic.alpha_regret),
        allow_negative: data.topic.allow_negative,
        epsilon: Number(data.topic.epsilon),
        initial_regret: Number(data.topic.initial_regret),
        worker_submission_window: Number(data.topic.worker_submission_window),
        merit_sortition_alpha: Number(data.topic.merit_sortition_alpha),
        active_inferer_quantile: Number(data.topic.active_inferer_quantile),
        active_forecaster_quantile: Number(
          data.topic.active_forecaster_quantile,
        ),
        active_reputer_quantile: Number(data.topic.active_reputer_quantile),
      },
      weight: Number(data.weight),
      effective_revenue: Number(data.effective_revenue),
    };
  }

  async getBlockByHeight(
    height: number,
    reqConfig?: AxiosRequestConfig<any>,
  ): Promise<AlloraBlockDetails> {
    const data = await this.executeRequest(
      HTTPMethod.GET,
      `/cosmos/base/tendermint/v1beta1/blocks/${height}`,
      reqConfig,
    );
    return this.hydrateBlockDetails(data);
  }

  private hydrateBlockDetails(data: any): AlloraBlockDetails {
    return {
      block: {
        header: {
          height: Number(data.block.header.height),
          time: data.block.header.time,
        },
      },
    };
  }

  async getTopicLastReputerCommitInfo(
    topicId: number,
    reqConfig?: AxiosRequestConfig<any>,
  ): Promise<AlloraTopicLastReputerCommitInfo> {
    const data = await this.executeRequest(
      HTTPMethod.GET,
      `/emissions/v9/topic_last_reputer_commit_info/${topicId}`,
      reqConfig,
    );

    return this.hydrateTopicLastReputerCommitInfo(data);
  }

  private hydrateTopicLastReputerCommitInfo(
    data: any,
  ): AlloraTopicLastReputerCommitInfo {
    return {
      last_commit: {
        block_height: Number(data.last_commit.block_height),
        nonce: {
          block_height: Number(data.last_commit.nonce.block_height),
        },
      },
    };
  }

  async getStatus(reqConfig?: AxiosRequestConfig<any>) {
    const data = await this.executeRequest(
      HTTPMethod.GET,
      `/cosmos/base/node/v1beta1/status`,
      reqConfig,
    );
    return data;
  }

  async executeRequest(
    httpMethod: HTTPMethod,
    requestPath: string,
    reqConfig: AxiosRequestConfig<any> = {},
    requiredResponseFields: string[] = [],
  ) {
    const requestUrl = `${this.baseApiUrl}${requestPath}`;
    const startTime = Date.now();

    try {
      const response = await axios[httpMethod.toLowerCase()](
        requestUrl,
        reqConfig,
      );
      const duration = (Date.now() - startTime) / 1000;

      const missingFields = this.getMissingFields(
        response.data,
        requiredResponseFields,
      );

      this.prometheusMetricsLogger.rpcRequestsTotal.inc({
        endpoint: requestUrl,
        http_method: httpMethod,
        status: response.status,
        error_type: '',
        missing_fields_count: missingFields.length,
      });

      // Record duration
      this.prometheusMetricsLogger.rpcRequestsDuration.observe(
        {
          endpoint: requestUrl,
          http_method: httpMethod,
        },
        duration,
      );
      return response.data;
    } catch (error) {
      const duration = (Date.now() - startTime) / 1000;

      // Determine if it's a connection error or another type
      const isConnectionError =
        error.code === 'ECONNREFUSED' ||
        error.code === 'ECONNABORTED' ||
        error.code === 'ETIMEDOUT' ||
        !error.response;

      const errorType = isConnectionError
        ? 'connection_error'
        : 'request_error';

      // Record failed request
      this.prometheusMetricsLogger.rpcRequestsTotal.inc({
        endpoint: requestUrl,
        http_method: httpMethod,
        status: error.response.status,
        error_type: errorType,
        missing_fields_count: 0,
      });

      // Record duration
      this.prometheusMetricsLogger.rpcRequestsDuration.observe(
        {
          endpoint: requestUrl,
          http_method: httpMethod,
        },
        duration,
      );

      throw error;
    }
  }

  private getMissingFields(response, expectedPaths) {
    const missingFields = expectedPaths.filter((path) => {
      const value = _.get(response, path);
      return value === null || value === undefined;
    });
    return missingFields;
  }
}
