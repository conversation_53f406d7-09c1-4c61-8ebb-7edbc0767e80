name: Build Pipeline for Test and Audit
'on':
  - pull_request
concurrency:
  group: ${{ github.head_ref || github.run_id }}
  cancel-in-progress: true
jobs:
  build:
    name: Run Unit Tests and Test Docker build
    runs-on: ubuntu-latest
    env:
      NODE_ENV: test
      NPM_TOKEN: ${{ secrets.NPM_AUTH_TOKEN }}
      DD_API_KEY: ${{ secrets.DD_API_KEY }}
      DD_ENV: ci
      TEST_DB_HOST: localhost
      TEST_DB_PORT: 15432
      TEST_DB_USER: postgres
      TEST_DB_PASS: postgres
      TEST_DB_NAME: postgres-test
      DB_HOST: upshot_backend_db
      DB_PORT: 5432
      DB_USER: postgres
      DB_PASS: postgres
      DB_NAME: postgres
      KAFKA_BROKER_URLS: localhost:9092
      REDIS_URL: redis://localhost:16379
      REDIS_CACHE_URL: redis://localhost:16379
      REDIS_IDEMPOTENCY_URL: redis://localhost:16379
      EMAIL_FROM: <EMAIL>
      PORT: 3000
      KAFKA_CONSUMERS_HEALTH_PORT: 5001
      JWT_KEY: testing-baby\!
      DYNAMO_DB_REGION: us-east-1
      DYNAMO_DB_ENDPOINT: http://localhost:18000
      AMAZON_ACCESS_KEY: key
      AMAZON_SECRET_KEY: secret
      AMAZON_S3_BUCKET_MARKET_DATA_UPLOADS: allora-dev-ohlc-timeseries
      ACTIVATE_E2E_TESTS: false
      HIDE_REPLICATION_LOGS: true
      JWT_API_PORTAL_SECRET: secret
      JWT_REFRESH_TOKEN_SECRET: secret
      JWT_ONE_TIME_LOGIN_TOKEN_SECRET: secret
      RECAPTCHA_SECRET: secret
      STRIPE_SECRET_KEY: secret
      STRIPE_WEBHOOK_SECRET: secret
      APPRAISAL_SIGNER_PRIVATE_KEY: '0xaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa'
      ALCHEMY_API_KEY: secret
      RESERVOIR_API_BASE_URL: https://api-goerli.reservoir.tools
      RESERVOIR_API_KEY: api-demo-key
      MODULUS_BASE_URL: https://dev---modulus-api-image-kmxx654kma-uw.a.run.app/manager
      MODULUS_MODEL_IDX: 2
      RN_SLACK_BOT_TOKEN: test
      SEPOLIA_ADAPTER_SIGNER_PRIVATE_KEY: ${{ secrets.SEPOLIA_ADAPTER_SIGNER_PRIVATE_KEY }}
      SEPOLIA_ADAPTER_SIGNER_PUBLIC_KEY: '0xa459c3a3b7769e18e702a3b5e2decdd495655791'
      SEPOLIA_RPC_URL: ${{ secrets.SEPOLIA_RPC_URL }}
      VAULT_REPORTER_PK: secret
      ALLORA_DB_HOST: secret
      ALLORA_DB_PORT: 1234
      ALLORA_DB_USER: secret
      ALLORA_DB_PASS: secret
      ALLORA_DB_NAME: secret
      ALLORA_API_RPC_URL: https://allora-api.testnet.allora.network
      ALLORA_HEAD_NODE_URL: https://heads.edgenet.allora.network/api/v1/functions/execute
      BINANCE_PARTNER_PUBLIC_KEY: test-public-key
      TIINGO_API_KEY: secret
    steps:
      - name: Checkout
        uses: actions/checkout@v3
      - name: Print env
        run: |
          pwd
          ls -a
          printenv
      - name: Setup Node.js 16.x
        uses: actions/setup-node@v3
        with:
          node-version: 16
          cache: npm
      - name: Cache NPM dependencies
        uses: actions/cache@v3
        id: cache-npm
        with:
          path: node_modules
          key: ${{ runner.os }}-npm-${{ hashFiles('package-lock.json') }}
      - name: Install Dependencies
        if: steps.cache-npm.outputs.cache-hit != 'true'
        run: npm ci --loglevel error
      - name: Validate Typescript
        run: npm run build:validate-ts
      - name: Lint
        run: npm run lint
      - name: Docker compose pull images
        run: docker compose pull --quiet
      - name: Build Containers (with e2e)
        run: docker compose build
      - name: Migrate Database
        run: |
          docker compose up -d --wait --quiet-pull upshot_backend_db
          npx sequelize-cli db:migrate
      - name: Start Containers
        timeout-minutes: 5
        run: npm run start:containers
      - name: Generate and verify OpenAPI (Swagger) definitions
        run: npm run build:swagger
      - name: Test (Unit)
        run: npm run test
      - name: Test (Integration)
        timeout-minutes: 5
        run: npm run test:integration
      - name: On Failure - App Container logs
        if: failure()
        run: >
          docker compose logs upshot_backend_rest_api
          upshot_backend_kafka_consumers
      - name: On Failure - Service Container logs
        if: failure()
        run: >
          docker compose config --services | egrep -v
          '(upshot_backend_rest_api|upshot_backend_kafka_consumers)' | docker
          compose logs
