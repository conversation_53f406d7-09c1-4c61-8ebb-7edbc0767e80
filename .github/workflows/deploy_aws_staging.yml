# This workflow will build and push a new container image to Amazon ECR,
# and then will deploy a new task definition to Amazon ECS which will be run by Fargate when a release is created
name: Deploy to Staging Amazon ECS

on:
  push:
    branches:
      - staging

concurrency:
  group: ${{ github.ref  }}
  cancel-in-progress: true
jobs:
  deploy-staging:
    name: Deploy to staging
    runs-on: ubuntu-latest
    environment: staging

    steps:
      - name: Checkout
        uses: actions/checkout@v3

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v2
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: us-east-1

      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v1

      - name: Build, tag, and push image to Amazon ECR
        id: build-image
        env:
          ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
          ECR_REPOSITORY: upshot-rest-api-staging
          IMAGE_TAG: ${{ github.sha }}
          NPM_TOKEN: ${{ secrets.NPM_AUTH_TOKEN }}
          MAX_OLD_SPACE_SIZE: 1024
        run: |
          # Build a docker container and push it to ECR so that it can be deployed to ECS.
          docker build -f infra/RestApi.Dockerfile --build-arg NPM_TOKEN=$NPM_TOKEN -t $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG .
          docker push $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG
          echo "image=$ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG" >> "$GITHUB_OUTPUT"

      - name: Fill in the new image ID in the Amazon ECS task definition
        id: task-def
        uses: aws-actions/amazon-ecs-render-task-definition@v1
        with:
          task-definition: infra/staging-task-def.json
          container-name: upshot-rest-api-staging
          image: ${{ steps.build-image.outputs.image }}

      - name: CRON - Fill in the new image ID in the Amazon ECS task definition
        id: cron-task-def
        uses: aws-actions/amazon-ecs-render-task-definition@v1
        with:
          task-definition: infra/staging-cron-task-def.json
          container-name: upshot-rest-api-cron-staging
          image: ${{ steps.build-image.outputs.image }}

      - name: Rest API - Fill in the new image ID in the Amazon ECS Migration task definition
        id: migration-task-def
        uses: aws-actions/amazon-ecs-render-task-definition@v1
        with:
          task-definition: infra/staging-migration-task-def.json
          container-name: upshot-rest-api-staging
          image: ${{ steps.build-image.outputs.image }}

      - name: Rest API - Push task definition to migration family
        working-directory: infra
        run: |
          aws ecs register-task-definition --cli-input-json file://${{ steps.migration-task-def.outputs.task-definition }}
          echo "Done pushing migration task definition to its aws family "

      - name: Rest API - Run migration task on AWS
        working-directory: infra
        run: |
          aws ecs run-task --cluster arn:aws:ecs:us-east-1:696230526504:cluster/upshot-backend-staging --task-definition upshot-rest-api-staging-migration --count 1 --enable-ecs-managed-tags --enable-execute-command --launch-type FARGATE --network-configuration file://staging-network.json --overrides file://staging-migration-override.json
          echo "Run task pushed to process"

      - name: Rest API - Deploy Amazon ECS task definition
        uses: aws-actions/amazon-ecs-deploy-task-definition@v1
        with:
          task-definition: ${{ steps.task-def.outputs.task-definition }}
          service: rest-api
          cluster: upshot-backend-staging
          wait-for-service-stability: true

      - name: CRON - Deploy Amazon ECS task definition
        uses: aws-actions/amazon-ecs-deploy-task-definition@v1
        with:
          task-definition: ${{ steps.cron-task-def.outputs.task-definition }}
          service: cron-job
          cluster: upshot-backend-staging
          wait-for-service-stability: true

  deploy-kafka-staging:
    name: Deploy Kafka to Staging
    runs-on: ubuntu-latest
    environment: staging

    steps:
      - name: Checkout
        uses: actions/checkout@v3

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v2
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: us-east-1

      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v1

      # Build and deploy Kafka service
      - name: Kafka - Build, tag, and push image to Amazon ECR
        id: build-image
        env:
          ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
          ECR_REPOSITORY: upshot-kafka-consumers-staging
          IMAGE_TAG: ${{ github.sha }}
          NPM_TOKEN: ${{ secrets.NPM_AUTH_TOKEN }}
          MAX_OLD_SPACE_SIZE: 30720
        run: |
          # Build a docker container and push it to ECR so that it can be deployed to ECS.
          # docker build -f infra/KafkaConsumers.Dockerfile --build-arg NPM_TOKEN=$NPM_TOKEN -t $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG .
          docker build -f infra/KafkaConsumers.Dockerfile --build-arg NPM_TOKEN=$NPM_TOKEN -t $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG .
          docker push $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG
          echo "image=$ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG" >> "$GITHUB_OUTPUT"

      - name: Kafka - Fill in the new image ID in the Amazon ECS task definition
        id: task-def
        uses: aws-actions/amazon-ecs-render-task-definition@v1
        with:
          task-definition: infra/staging-kafka-consumers-task-def.json
          container-name: upshot-kafka-consumers-staging
          image: ${{ steps.build-image.outputs.image }}

      - name: Kafka - Deploy Amazon ECS task definition
        uses: aws-actions/amazon-ecs-deploy-task-definition@v1
        with:
          task-definition: ${{ steps.task-def.outputs.task-definition }}
          service: kafka-consumer
          cluster: upshot-backend-staging
          wait-for-service-stability: true

  deploy-api-portal-staging:
    name: Deploy to staging api-portal
    runs-on: ubuntu-latest
    environment: staging

    steps:
      - name: Checkout
        uses: actions/checkout@v3

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v2
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: us-east-1

      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v1

      - name: Build, tag, and push image to Amazon ECR
        id: build-image
        env:
          ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
          ECR_REPOSITORY: upshot-api-portal-staging
          IMAGE_TAG: ${{ github.sha }}
          NPM_TOKEN: ${{ secrets.NPM_AUTH_TOKEN }}
          MAX_OLD_SPACE_SIZE: 1024
        run: |
          # Build a docker container and push it to ECR so that it can be deployed to ECS.
          docker build -f infra/APIPortal.Dockerfile --build-arg NPM_TOKEN=$NPM_TOKEN -t $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG .
          docker push $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG
          echo "image=$ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG" >> "$GITHUB_OUTPUT"

      - name: Fill in the new image ID in the Amazon ECS task definition
        id: task-def
        uses: aws-actions/amazon-ecs-render-task-definition@v1
        with:
          task-definition: infra/staging-api-portal-task-def.json
          container-name: upshot-api-portal-staging
          image: ${{ steps.build-image.outputs.image }}

      - name: API Portal - Deploy Amazon ECS task definition
        uses: aws-actions/amazon-ecs-deploy-task-definition@v1
        with:
          task-definition: ${{ steps.task-def.outputs.task-definition }}
          service: api-portal
          cluster: upshot-backend-staging
          wait-for-service-stability: true

  publish-docs-package:
    # Don't run on re-runs until we fix a manual re-deploy Action
    if: github.run_attempt == 1
    needs: deploy-staging
    name: publish new api docs # and new types package
    runs-on: ubuntu-latest
    environment: staging
    env:
      GITHUB_TOKEN: ${{ secrets.UPSHOT_PROTECTED_PUSH }}
      NPM_TOKEN: ${{ secrets.NPM_AUTH_TOKEN }}

    steps:
      - name: Checkout
        uses: actions/checkout@v3
      - run: |
          git config --global user.email "<EMAIL>"
          git config --global user.name "Github Action"

      - name: Setup Node.js 16.x
        uses: actions/setup-node@v3
        with:
          node-version: 16
          cache: 'npm'

      # - name: Create New Types Version
      #   run: npm version patch -m "%s [skip actions]"

      - name: Generate types definition
        run: npx --yes swagger-typescript-api -p <(curl -s https://upshot:${{ secrets.STAGE_DOCS_PASSWORD }}@stage.api.upshot.xyz/v2/docs-json) -n stage.swagger.d.ts

      # - name: Publish Types Package
      #   run: npm publish

      - name: Resync Swagger API Definition for https://docs.stage.upshot.xyz/reference/
        uses: readmeio/rdme@v8
        with:
          rdme: openapi https://upshot:${{ secrets.STAGE_DOCS_PASSWORD }}@stage.api.upshot.xyz/v2/docs-json --key=${{ secrets.README_STAGE_API_KEY }} --id=${{ secrets.README_STAGE_API_DEF_ID }}
