# This workflow will build and push a new container image to Amazon ECR,
# and then will deploy a new task definition to Amazon ECS which will be run by Fargate when a release is created
name: Deploy to Production Amazon ECS

on:
  push:
    branches:
      - main

concurrency:
  group: ${{ github.ref }}
  cancel-in-progress: true

jobs:
  deploy-prod:
    if: github.ref == 'refs/heads/main'
    name: Deploy Rest API to production
    runs-on: ubuntu-latest
    environment: production

    steps:
    - name: Checkout
      uses: actions/checkout@v3

    - name: Configure AWS credentials
      uses: aws-actions/configure-aws-credentials@v2
      with:
        aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
        aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        aws-region: us-east-1

    - name: Login to Amazon ECR
      id: login-ecr
      uses: aws-actions/amazon-ecr-login@v1

    # Build and deploy Rest API
    - name: Rest API - Build, tag, and push image to Amazon ECR
      id: build-image
      env:
        ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
        ECR_REPOSITORY: minimalist-api
        IMAGE_TAG: ${{ github.sha }}
        NPM_TOKEN: ${{ secrets.NPM_AUTH_TOKEN }}
        MAX_OLD_SPACE_SIZE: 30720
      run: |
        # Build a docker container and push it to ECR so that it can be deployed to ECS.
        docker build -f infra/RestApi.Dockerfile --build-arg NPM_TOKEN=$NPM_TOKEN -t $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG .
        docker push $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG
        echo "image=$ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG" >> "$GITHUB_OUTPUT"

    - name: Rest API - Fill in the new image ID in the Amazon ECS task definition
      id: task-def
      uses: aws-actions/amazon-ecs-render-task-definition@v1
      with:
        task-definition: infra/prod-task-def.json
        container-name: upshot-rest-api-production
        image: ${{ steps.build-image.outputs.image }}

    - name: CRON - Fill in the new image ID in the Amazon ECS task definition
      id: cron-task-def
      uses: aws-actions/amazon-ecs-render-task-definition@v1
      with:
        task-definition: infra/prod-cron-task-def.json
        container-name: upshot-rest-api-production-cron
        image: ${{ steps.build-image.outputs.image }}

    - name: Rest API - Fill in the new image ID in the Amazon ECS Migration task definition
      id: migration-task-def
      uses: aws-actions/amazon-ecs-render-task-definition@v1
      with:
        task-definition: infra/prod-migration-task-def.json
        container-name: upshot-rest-api-production
        image: ${{ steps.build-image.outputs.image }}

    - name: Rest API - Push task definition to migration family
      working-directory: infra
      run: |
        aws ecs register-task-definition --cli-input-json file://${{ steps.migration-task-def.outputs.task-definition }}
        echo "Done pushing migration task definition to its aws family "

    - name: Rest API - Run migration task on AWS
      working-directory: infra
      run: |
        aws ecs run-task --cluster arn:aws:ecs:us-east-1:696230526504:cluster/upshot-backend-production --task-definition minimalist-migration-prod --count 1 --enable-ecs-managed-tags --enable-execute-command --launch-type FARGATE --network-configuration file://network.json --overrides file://migration-override.json
        echo "Run task pushed to process"

    - name: Rest API - Deploy Amazon ECS task definition
      uses: aws-actions/amazon-ecs-deploy-task-definition@v1
      with:
        task-definition: ${{ steps.task-def.outputs.task-definition }}
        service: rest-api
        cluster: upshot-backend-production
        wait-for-service-stability: true

    - name: CRON - Deploy Amazon ECS task definition
      uses: aws-actions/amazon-ecs-deploy-task-definition@v1
      with:
        task-definition: ${{ steps.cron-task-def.outputs.task-definition }}
        service: cron-job
        cluster: upshot-backend-production
        wait-for-service-stability: true

  deploy-kafka-prod:
    if: github.ref == 'refs/heads/main'
    name: Deploy Kafka to production
    runs-on: ubuntu-latest
    environment: production

    steps:
    - name: Checkout
      uses: actions/checkout@v3

    - name: Configure AWS credentials
      uses: aws-actions/configure-aws-credentials@v2
      with:
        aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
        aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        aws-region: us-east-1

    - name: Login to Amazon ECR
      id: login-ecr
      uses: aws-actions/amazon-ecr-login@v1

    # Build and deploy Kafka service
    - name: Kafka - Build, tag, and push image to Amazon ECR
      id: build-image
      env:
        ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
        ECR_REPOSITORY: kafka
        IMAGE_TAG: ${{ github.sha }}
        NPM_TOKEN: ${{ secrets.NPM_AUTH_TOKEN }}
        MAX_OLD_SPACE_SIZE: 30720
      run: |
        # Build a docker container and push it to ECR so that it can be deployed to ECS.
        # docker build -f infra/KafkaConsumers.Dockerfile --build-arg NPM_TOKEN=$NPM_TOKEN -t $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG .
        docker build -f infra/KafkaConsumers.Dockerfile --build-arg NPM_TOKEN=$NPM_TOKEN -t $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG .
        docker push $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG
        echo "image=$ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG" >> "$GITHUB_OUTPUT"

    - name: Kafka - Fill in the new image ID in the Amazon ECS task definition
      id: task-def
      uses: aws-actions/amazon-ecs-render-task-definition@v1
      with:
        task-definition: infra/prod-kafka-consumers-task-def.json
        container-name: upshot-kafka-consumers-api-container
        image: ${{ steps.build-image.outputs.image }}

    - name: Kafka - Deploy Amazon ECS task definition
      uses: aws-actions/amazon-ecs-deploy-task-definition@v1
      with:
        task-definition: ${{ steps.task-def.outputs.task-definition }}
        service: kafka-consumer
        cluster: upshot-backend-production
        wait-for-service-stability: true

  deploy-api-portal-prod:
    name: Deploy to production api-portal
    runs-on: ubuntu-latest
    environment: production

    steps:
    - name: Checkout
      uses: actions/checkout@v3

    - name: Configure AWS credentials
      uses: aws-actions/configure-aws-credentials@v2
      with:
        aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
        aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        aws-region: us-east-1

    - name: Login to Amazon ECR
      id: login-ecr
      uses: aws-actions/amazon-ecr-login@v1

    - name: Build, tag, and push image to Amazon ECR
      id: build-image
      env:
        ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
        ECR_REPOSITORY: upshot-api-portal-prod
        IMAGE_TAG: ${{ github.sha }}
        NPM_TOKEN: ${{ secrets.NPM_AUTH_TOKEN }}
        MAX_OLD_SPACE_SIZE: 1024
      run: |
        # Build a docker container and push it to ECR so that it can be deployed to ECS.
        docker build -f infra/APIPortal.Dockerfile --build-arg NPM_TOKEN=$NPM_TOKEN -t $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG .
        docker push $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG
        echo "image=$ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG" >> "$GITHUB_OUTPUT"

    - name: Fill in the new image ID in the Amazon ECS task definition
      id: task-def
      uses: aws-actions/amazon-ecs-render-task-definition@v1
      with:
        task-definition: infra/prod-api-portal-task-def.json
        container-name: upshot-api-portal-prod
        image: ${{ steps.build-image.outputs.image }}

    - name: API Portal - Deploy Amazon ECS task definition
      uses: aws-actions/amazon-ecs-deploy-task-definition@v1
      with:
        task-definition: ${{ steps.task-def.outputs.task-definition }}
        service: api-portal
        cluster: upshot-backend-production
        wait-for-service-stability: true

  publish-docs:
    needs: deploy-prod
    runs-on: ubuntu-latest
    steps:
    - name: Resync Swagger API Definition for https://docs.upshot.xyz/reference/
      uses: readmeio/rdme@v8
      with:
        rdme: openapi https://api.upshot.xyz/v2/docs-json --key=${{ secrets.README_API_KEY }} --id=${{ secrets.README_API_DEF_ID }}

  notify:
    needs: [deploy-prod, deploy-kafka-prod, deploy-api-portal-prod]
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v3
        with:
          fetch-depth: 0
      - name: Set Envs
        run: |
          echo "GIT_TAG=$(git describe --tags --abbrev=0)" >> $GITHUB_ENV
          echo "PR_NUMBER=$(echo ${{ github.ref }} | cut -d / -f 3)" >> $GITHUB_ENV
      - name: Notify Datadog
        run: |
          curl -X POST -H "Content-type: application/json" -H "DD-API-KEY: ${{ secrets.DD_API_KEY }}" \
          -d '{
            "title": "New Production Release",
            "text": "A new production release has been deployed.\nVersion: ${{env.GIT_TAG}}\nPR: https://github.com/upshot-tech/upshot-backend/pull/${{env.PR_NUMBER}}",
            "tags": ["success:true", "component:backend_api", "service":backend_api", "environment:production", "env":"production", "version:${{env.GIT_TAG}}"]
          }' \
          "https://api.datadoghq.com/api/v1/events"
