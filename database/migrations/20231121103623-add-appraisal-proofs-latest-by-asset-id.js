'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      /**
       * appraisal_proofs_latest_by_asset_id is an updatable table.
       * appraisal_proofs is insert-only tables.
       */
      await queryInterface.createTable(
        'appraisal_proofs_latest_by_asset',
        {
          asset_id: {
            type: Sequelize.TEXT,
            primaryKey: true,
            allowNull: false,
          },
          inference_id: {
            type: Sequelize.INTEGER,
            allowNull: false,
          },
          fulfilled_at: {
            // when we received the proof from modulus
            type: Sequelize.DATE,
            allowNull: false,
          },
        },
        { transaction },
      );

      await transaction.commit();
    } catch (error) {
      await transaction.rollback();
      // Log all errors, but don't throw to prevent migration failure
      console.log(error);
    }
  },

  async down(queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      await queryInterface.dropTable('appraisal_proofs_latest_by_asset', {
        transaction,
      });

      await transaction.commit();
    } catch (error) {
      await transaction.rollback();
      console.log(error);
    }
  },
};
