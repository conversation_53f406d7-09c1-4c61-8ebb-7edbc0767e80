'use strict';

const TICKERS = [
  'ethusd',
  'btcusd',
  'arbusd',
  'bnbusd',
  'solusd',
  'frxethweth',
  'methusdt',
  'virtualusdt',
  'aixbtusdt',
  'lunausdt',
  'vadereth',
  'gameveth',
  'sekoiaeth',
];

const toISODate = (date) => {
  const year = date.getUTCFullYear();
  const month = String(date.getUTCMonth() + 1).padStart(2, '0');
  const day = String(date.getUTCDate()).padStart(2, '0');
  return `${year}-${month}-${day}`;
};

const buildMonth = (ticker, year, month) => {
  const monthIndex = month - 1;

  const start = new Date(Date.UTC(year, monthIndex, 1));
  const end = new Date(Date.UTC(year, monthIndex + 1, 0));

  return {
    ticker,
    periodicity: 'month',
    state: 'pending',
    start: toISODate(start),
    end: toISODate(end),
  };
};

const buildYear = (ticker, year, fromMonth = 1, toMonth = 12) => {
  const rows = [];
  for (let month = fromMonth; month <= toMonth; month++) {
    rows.push(buildMonth(ticker, year, month));
  }
  return rows;
};

module.exports = {
  async up(queryInterface, Sequelize) {
    for (const ticker of TICKERS) {
      const rows = [];
      rows.push(...buildYear(ticker, 2020));
      rows.push(...buildYear(ticker, 2021));
      rows.push(...buildYear(ticker, 2022));
      rows.push(...buildYear(ticker, 2023));
      rows.push(...buildYear(ticker, 2024));
      rows.push(...buildYear(ticker, 2025, 1, 2));
      console.log(
        `allora_ohlc_buckets: inserting ${rows.length} rows for ticker ${ticker}`,
      );
      await queryInterface.bulkInsert('allora_ohlc_buckets', rows);
    }
  },

  async down(queryInterface, Sequelize) {
    console.error(`this migration cannot be reversed`);
  },
};
