'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      await queryInterface.createTable('allora_forge_user_competitions', {
        id: {
          type: Sequelize.INTEGER,
          autoIncrement: true,
          primaryKey: true,
        },
        allora_user_id: {
          type: Sequelize.UUID,
          allowNull: false,
          references: {
            model: 'allora_users',
            key: 'id',
          },
        },
        allora_forge_competition_id: {
          type: Sequelize.INTEGER,
          allowNull: false,
          references: {
            model: 'allora_forge_competitions',
            key: 'id',
          },
        },
        submitted_registration: {
          type: Sequelize.BOOLEAN,
          allowNull: false,
          defaultValue: false,
        },
        approved_registration: {
          type: Sequelize.BOOLEAN,
          allowNull: false,
          defaultValue: false,
        },
        updated_at: {
          type: Sequelize.DATE,
          allowNull: false,
          defaultValue: Sequelize.fn('NOW'),
        },
        created_at: {
          type: Sequelize.DATE,
          allowNull: false,
          defaultValue: Sequelize.fn('NOW'),
        },
      });

      // create unique constraint on allora_user_id and allora_forge_competition_id
      await queryInterface.addConstraint(
        'allora_forge_user_competitions',
        {
          name: 'unique_allora_user_id_and_allora_forge_competition_id',
          type: 'unique',
          fields: ['allora_user_id', 'allora_forge_competition_id'],
        },
        { transaction },
      );
      await transaction.commit();
    } catch (e) {
      await transaction.rollback();
      throw e;
    }
  },

  async down(queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      // drop unique constraint on allora_user_id and allora_forge_competition_id
      await queryInterface.removeConstraint(
        'allora_forge_user_competitions',
        'unique_allora_user_id_and_allora_forge_competition_id',
        { transaction },
      );
      await queryInterface.dropTable('allora_forge_user_competitions', {
        transaction,
      });
      await transaction.commit();
    } catch (e) {
      await transaction.rollback();
      throw e;
    }
  },
};
