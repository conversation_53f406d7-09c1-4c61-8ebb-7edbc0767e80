'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      await queryInterface.createTable(
        'binance_wallet_campaign',
        {
          id: {
            type: Sequelize.INTEGER,
            primaryKey: true,
            autoIncrement: true,
          },
          wallet_id: {
            type: Sequelize.TEXT,
            allowNull: false,
          },
          evm_address: {
            type: Sequelize.TEXT,
            allowNull: false,
            primaryKey: true,
          },
          metadata: {
            type: Sequelize.JSONB,
            allowNull: true,
          },
          updated_at: {
            type: Sequelize.DATE,
            defaultValue: Sequelize.fn('now'),
          },
          created_at: {
            type: Sequelize.DATE,
            defaultValue: Sequelize.fn('now'),
          },
        },
        { transaction },
      );
      await transaction.commit();
    } catch (e) {
      await transaction.rollback();
      throw e;
    }
  },

  down: async (queryInterface, Sequelize) => {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      await queryInterface.dropTable('binance_wallet_campaign');

      await transaction.commit();
    } catch (e) {
      await transaction.rollback();
      throw e;
    }
  },
};
