'use strict';

/*
This migration is to creates the table `allora_topic_token_endpoint_identifiers`
*/

module.exports = {
  up: async (queryInterface, Sequelize) => {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      // Create tables for token prices
      await queryInterface.sequelize.query(
        `
      CREATE TABLE allora_topic_token_endpoint_identifiers (
          topic_id TEXT NOT NULL,
          token TEXT NOT NULL,
          iso_duration TEXT NOT NULL,
          use_worker_not_network BOOLEAN NOT NULL DEFAULT FALSE,
          worker_address TEXT,
          worker_inference_endpoint TEXT,
          worker_internal_id TEXT,
          created_at timestamp with time zone NOT NULL DEFAULT now(),
          updated_at timestamp with time zone NOT NULL DEFAULT now()
      );
      `,
        { transaction },
      );

      // Create tables for volatility
      await queryInterface.sequelize.query(
        `
      CREATE TABLE allora_topic_volatility_endpoint_identifiers (
          topic_id TEXT NOT NULL,
          token TEXT NOT NULL,
          iso_duration TEXT NOT NULL,
          use_worker_not_network BOOLEAN NOT NULL DEFAULT FALSE,
          worker_address TEXT,
          worker_inference_endpoint TEXT,
          worker_internal_id TEXT,
          created_at timestamp with time zone NOT NULL DEFAULT now(),
          updated_at timestamp with time zone NOT NULL DEFAULT now()
      );
      `,
        { transaction },
      );

      await transaction.commit();
    } catch (e) {
      await transaction.rollback();
      throw e;
    }
  },

  down: async (queryInterface, Sequelize) => {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      // Drop tables and indexes for devnet_1
      await queryInterface.sequelize.query(
        `
        DROP TABLE IF EXISTS allora_topic_token_endpoint_identifiers;
        DROP TABLE IF EXISTS allora_topic_volatility_endpoint_identifiers;
      `,
        { transaction },
      );

      await transaction.commit();
    } catch (e) {
      await transaction.rollback();
      throw e;
    }
  },
};

// Token prices
// 13	ETH	5m		allo10zs9ngtjfqgq2xm49pvar2zug5w7eta2z6lu8n	http://offchain.privy.behindthecurtain.xyz:8096/inference/ETH	testnet--13--worker-1
// 14	BTC	5m		allo1s980q3mhw5wkv7q3pskp9yvdzuqx059vkwt8n5	http://offchain.privy.behindthecurtain.xyz:8097/inference/BTC	testnet--14--worker-1		
// 17	ETH	8h		allo12qlzjv9eeuevzrtutc5hkcdjupmxl6r7hztpw9	http://offchain.privy.behindthecurtain.xyz:8100/inference/ETH	testnet--17--worker-1		
// 18	BTC	8h		allo1a7nxgnu0knuzssqlgw5yclmr2dmqyrnqhq2war	http://offchain.privy.behindthecurtain.xyz:8101/inference/BTC	testnet--18--worker-1		

// Volatility
// 15	ETH	5m		allo1jpn4y7mwntw5655svgjtytudl60ulyf5k2c0dv	http://offchain.privy.behindthecurtain.xyz:8098/inference/ETH	testnet--15--worker-1		
// 16	BTC	5m		allo1h322pw8td4vff92m30t52fd8fq3g3xq3hud6yl	http://offchain.privy.behindthecurtain.xyz:8099/inference/BTC	testnet--16--worker-1		
// 19	BTC	8h		allo190703c6llh2h5jwjd9yrutqxtug05r9luf70ar	http://offchain.privy.behindthecurtain.xyz:8102/inference/BTC	testnet--19--worker-1		
// 20	BTC	8h		allo1mwv56e4uvrpjnen3wx0kxtcaqrv0elfxrka7nz	http://offchain.privy.behindthecurtain.xyz:8103/inference/BTC	testnet--20--worker-1		

// api_access_control_rules
// <distinct_uuidv4>	100000	/v2/allora/consumer/price/:chain_slug/:token/:duration	2024-11-14 17:44:38.615016+00	
// <distinct_uuidv4>	0	/v2/allora/consumer/price/:chain_slug/:token/:duration	2024-11-14 17:44:38.615016+00	
// <distinct_uuidv4>	100	/v2/allora/consumer/price/:chain_slug/:token/:duration	2024-11-14 17:44:38.615016+00	
// <distinct_uuidv4>	100000	/v2/allora/consumer/volatility/:chain_slug/:token/:duration	2024-11-14 17:44:38.615016+00	
// <distinct_uuidv4>	0	/v2/allora/consumer/volatility/:chain_slug/:token/:duration	2024-11-14 17:44:38.615016+00	
// <distinct_uuidv4>	100	/v2/allora/consumer/volatility/:chain_slug/:token/:duration	2024-11-14 17:44:38.615016+00	

