'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      // note: there are existing rows in this table so we must allow null
      await queryInterface.addColumn(
        'allora_users',
        'username',
        {
          type: Sequelize.TEXT,
          allowNull: true,
          unique: true,
        },
        { transaction },
      );
      await queryInterface.addColumn(
        'allora_users',
        'email',
        {
          type: Sequelize.TEXT,
          allowNull: true,
          unique: true,
        },
        { transaction },
      );
      await queryInterface.addColumn(
        'allora_users',
        'first_name',
        {
          type: Sequelize.TEXT,
          allowNull: true,
        },
        { transaction },
      );
      await queryInterface.addColumn(
        'allora_users',
        'last_name',
        {
          type: Sequelize.TEXT,
          allowNull: true,
        },
        { transaction },
      );
      await transaction.commit();
    } catch (error) {
      console.error(error);
      await transaction.rollback();
      throw error;
    }
  },

  async down(queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      await queryInterface.removeColumn('allora_users', 'last_name', {
        transaction,
      });
      await queryInterface.removeColumn('allora_users', 'first_name', {
        transaction,
      });
      await queryInterface.removeColumn('allora_users', 'email', {
        transaction,
      });
      await queryInterface.removeColumn('allora_users', 'username', {
        transaction,
      });

      await transaction.commit();
    } catch (error) {
      console.error(error);
      await transaction.rollback();
      throw error;
    }
  },
};
