'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      await queryInterface.createTable(
        'tokens_oracle_supported_tokens',
        {
          id: {
            type: Sequelize.STRING,
            primaryKey: true,
          },
          name: {
            type: Sequelize.STRING,
            allowNull: true,
          },
          symbol: {
            type: Sequelize.STRING,
            allowNull: false,
          },
          created_at: {
            type: Sequelize.DATE,
            defaultValue: Sequelize.fn('now'),
          },
          updated_at: {
            type: Sequelize.DATE,
            defaultValue: Sequelize.fn('now'),
          },
        },
        { transaction },
      );

      // tokens feed table
      await queryInterface.createTable(
        'tokens_oracle_block_data',
        {
          block_height: {
            type: Sequelize.INTEGER,
            primaryKey: true,
          },
          token_id: {
            type: Sequelize.STRING,
          },
          token_symbol: {
            type: Sequelize.STRING,
          },
          created_at: {
            type: Sequelize.DATE,
            defaultValue: Sequelize.fn('now'),
          },
          updated_at: {
            type: Sequelize.DATE,
            defaultValue: Sequelize.fn('now'),
          },
        },
        { transaction },
      );

      await transaction.commit();
    } catch (error) {
      console.error(error);
      await transaction.rollback();
      throw error;
    }
  },

  async down(queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();
    // seasons
    await queryInterface.dropTable(
      'tokens_oracle_supported_tokens',
      transaction,
    );
    // season points
    await queryInterface.dropTable('tokens_oracle_block_data', transaction);
    await transaction.commit();
  },
};
