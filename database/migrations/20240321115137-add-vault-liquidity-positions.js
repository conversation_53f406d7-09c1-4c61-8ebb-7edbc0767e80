'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      await queryInterface.dropTable('vault_liquidity_positions');
      await queryInterface.createTable(
        'vault_user_aggregated_liquidity_positions',
        {
          vault_address: {
            type: Sequelize.TEXT,
            allowNull: false,
          },
          chain_id: {
            type: Sequelize.INTEGER,
            allowNull: false,
          },
          user_address: {
            type: Sequelize.STRING,
            allowNull: false,
          },
          erc20_amount: {
            type: Sequelize.STRING,
            allowNull: false,
          },
          shares_amount: {
            type: Sequelize.STRING,
            allowNull: false,
          },
          created_at: {
            type: Sequelize.DATE,
            allowNull: false,
          },
        },
      );

      await queryInterface.addConstraint(
        'vault_user_aggregated_liquidity_positions',
        {
          fields: ['vault_address', 'chain_id', 'user_address'],
          type: 'primary key',
          name: 'vault_user_aggregated_liquidity_positions_pkey',
          transaction,
        },
      );
      await transaction.commit();
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('vault_user_aggregated_liquidity_positions');
  },
};
