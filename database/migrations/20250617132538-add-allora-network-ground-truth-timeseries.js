'use strict';

const networkSuffixes = [
  'allora_devnet_1',
  'allora_testnet_1',
  'allora_mainnet_1',
];

module.exports = {
  async up(queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();

    try {
      //  NETWORK GROUND TRUTH
      for (const networkSuffix of networkSuffixes) {
        const tableName = `allora_network_ground_truth_${networkSuffix}`;

        await queryInterface.createTable(
          tableName,
          {
            id: {
              type: Sequelize.BIGINT,
              primaryKey: true,
              autoIncrement: true,
            },
            topic_id: {
              type: Sequelize.INTEGER,
              allowNull: false,
            },
            ground_truth_value: {
              type: Sequelize.TEXT,
              allowNull: false,
            },
            timestamp: {
              type: Sequelize.INTEGER,
              allowNull: false,
            },
            epoch_last_ended_height: {
              type: Sequelize.INTEGER,
              allowNull: false,
            },
            reputation_timestamp: {
              type: Sequelize.INTEGER,
              allowNull: false,
            },
            created_at: {
              type: Sequelize.DATE,
              allowNull: false,
              defaultValue: Sequelize.fn('NOW'),
            },
          },
          { transaction },
        );

        await queryInterface.addIndex(tableName, ['topic_id', 'timestamp'], {
          name: `idx_network_ground_truth_${networkSuffix}`,
          transaction,
        });
      }
      await transaction.commit();
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  },

  async down(queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      for (const networkSuffix of networkSuffixes) {
        const tableName = `allora_network_ground_truth_${networkSuffix}`;
        await queryInterface.dropTable(tableName, { transaction });
        await queryInterface.removeIndex(
          tableName,
          `idx_network_ground_truth_${networkSuffix}`,
          { transaction },
        );
      }
      await transaction.commit();
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  },
};
