module.exports = {
  async up(queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      await queryInterface.createTable(
        'ditto_leaderboard',
        {
          address: {
            type: Sequelize.TEXT,
            allowNull: false,
          },
          liquidity_points: {
            type: Sequelize.INTEGER,
            allowNull: false,
            defaultValue: 0,
          },
          referral_points: {
            type: Sequelize.INTEGER,
            allowNull: false,
            defaultValue: 0,
          },
          total: {
            type: Sequelize.INTEGER,
            allowNull: false,
            defaultValue: 0,
          },
          index: {
            type: Sequelize.INTEGER,
            allowNull: false,
          },
          season_id: {
            type: Sequelize.INTEGER,
            allowNull: false,
          },
          created_at: {
            type: Sequelize.DATE,
            allowNull: false,
            defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
          },
          updated_at: {
            type: Sequelize.DATE,
            allowNull: false,
            defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
          },
        },
        { transaction },
      );

      await queryInterface.addConstraint('ditto_leaderboard', {
        fields: ['address', 'season_id'],
        type: 'primary key',
        name: 'ditto_leaderboard_pkey',
        transaction,
      });

      await queryInterface.addIndex(
        'ditto_leaderboard',
        ['season_id', 'total'],
        { transaction },
      );
      await queryInterface.addIndex(
        'ditto_leaderboard',
        ['season_id', 'address'],
        { transaction },
      );
      await queryInterface.addIndex('ditto_leaderboard', ['total'], {
        transaction,
      });

      await transaction.commit();
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  },

  async down(queryInterface) {
    // drop ditto_leaderboard table
    await queryInterface.dropTable('ditto_leaderboard');
  },
};
