'use strict';

/*
This migration is to rename existing allora tables with a network name suffix to support multiple chains.
The existing tables are for allora_testnet_1, so the following tables should be renamed with that suffix:
- allora_topics_metadata
- allora_topic_worker_stats
- allora_topic_worker_losses
- allora_topic_worker_ema_scores
- allora_topic_worker_count
- allora_topic_total_staked
- allora_topic_reputer_stats
- allora_topic_reputer_losses
- allora_topic_reputer_ema_scores
- allora_topic_emissions
- allora_wallet_balance
*/

module.exports = {
  up: async (queryInterface, Sequelize) => {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      // Rename tables with _allora_testnet_1 suffix
      await queryInterface.renameTable(
        'allora_topics_metadata',
        'allora_topics_metadata_allora_testnet_1',
        { transaction },
      );
      await queryInterface.renameTable(
        'allora_topic_worker_stats',
        'allora_topic_worker_stats_allora_testnet_1',
        { transaction },
      );
      await queryInterface.renameTable(
        'allora_topic_worker_losses',
        'allora_topic_worker_losses_allora_testnet_1',
        { transaction },
      );
      await queryInterface.renameTable(
        'allora_topic_worker_ema_scores',
        'allora_topic_worker_ema_scores_allora_testnet_1',
        { transaction },
      );
      await queryInterface.renameTable(
        'allora_topic_worker_count',
        'allora_topic_worker_count_allora_testnet_1',
        { transaction },
      );
      await queryInterface.renameTable(
        'allora_topic_total_staked',
        'allora_topic_total_staked_allora_testnet_1',
        { transaction },
      );
      await queryInterface.renameTable(
        'allora_topic_reputer_stats',
        'allora_topic_reputer_stats_allora_testnet_1',
        { transaction },
      );
      await queryInterface.renameTable(
        'allora_topic_reputer_losses',
        'allora_topic_reputer_losses_allora_testnet_1',
        { transaction },
      );
      await queryInterface.renameTable(
        'allora_topic_reputer_ema_scores',
        'allora_topic_reputer_ema_scores_allora_testnet_1',
        { transaction },
      );
      await queryInterface.renameTable(
        'allora_topic_emissions',
        'allora_topic_emissions_allora_testnet_1',
        { transaction },
      );
      await queryInterface.renameTable(
        'allora_wallet_balance',
        'allora_wallet_balance_allora_testnet_1',
        { transaction },
      );

      await transaction.commit();
    } catch (e) {
      await transaction.rollback();
      throw e;
    }
  },

  down: async (queryInterface, Sequelize) => {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      // Revert table names to original
      await queryInterface.renameTable(
        'allora_topics_metadata_allora_testnet_1',
        'allora_topics_metadata',
        { transaction },
      );
      await queryInterface.renameTable(
        'allora_topic_worker_stats_allora_testnet_1',
        'allora_topic_worker_stats',
        { transaction },
      );
      await queryInterface.renameTable(
        'allora_topic_worker_losses_allora_testnet_1',
        'allora_topic_worker_losses',
        { transaction },
      );
      await queryInterface.renameTable(
        'allora_topic_worker_ema_scores_allora_testnet_1',
        'allora_topic_worker_ema_scores',
        { transaction },
      );
      await queryInterface.renameTable(
        'allora_topic_worker_count_allora_testnet_1',
        'allora_topic_worker_count',
        { transaction },
      );
      await queryInterface.renameTable(
        'allora_topic_total_staked_allora_testnet_1',
        'allora_topic_total_staked',
        { transaction },
      );
      await queryInterface.renameTable(
        'allora_topic_reputer_stats_allora_testnet_1',
        'allora_topic_reputer_stats',
        { transaction },
      );
      await queryInterface.renameTable(
        'allora_topic_reputer_losses_allora_testnet_1',
        'allora_topic_reputer_losses',
        { transaction },
      );
      await queryInterface.renameTable(
        'allora_topic_reputer_ema_scores_allora_testnet_1',
        'allora_topic_reputer_ema_scores',
        { transaction },
      );
      await queryInterface.renameTable(
        'allora_topic_emissions_allora_testnet_1',
        'allora_topic_emissions',
        { transaction },
      );
      await queryInterface.renameTable(
        'allora_wallet_balance_allora_testnet_1',
        'allora_wallet_balance',
        { transaction },
      );

      await transaction.commit();
    } catch (e) {
      await transaction.rollback();
      throw e;
    }
  },
};
