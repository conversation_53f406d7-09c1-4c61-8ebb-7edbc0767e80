'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      // Create table of timeseries of inferences for allora models per use case
      await queryInterface.createTable(
        'allora_model_inferences_timeseries',
        {
          token: {
            type: Sequelize.TEXT,
            allowNull: false,
          },
          iso_duration: {
            type: Sequelize.TEXT,
            allowNull: false,
          },
          worker_internal_id: {
            type: Sequelize.TEXT,
            allowNull: false,
          },
          inference_value: {
            type: Sequelize.TEXT,
            allowNull: false,
          },
          timestamp: {
            type: Sequelize.INTEGER,
            allowNull: false,
          },
          created_at: {
            type: Sequelize.DATE,
            allowNull: false,
            defaultValue: Sequelize.fn('NOW'),
          },
        },
        {
          transaction,
        },
      );

      // Create index separately
      await queryInterface.addIndex(
        'allora_model_inferences_timeseries',
        [
          { name: 'token', order: 'ASC' },
          { name: 'iso_duration', order: 'ASC' },
          { name: 'timestamp', order: 'DESC' }
        ],
        {
          name: 'allora_model_inferences_timeseries_token_duration_timestamp_idx',
          transaction,
        }
      );

      // Create table of latest inferences per allora model per use case
      await queryInterface.createTable(
        'allora_model_inferences_latest',
        {
          token: {
            type: Sequelize.TEXT,
            allowNull: false,
          },
          iso_duration: {
            type: Sequelize.TEXT,
            allowNull: false,
          },
          worker_internal_id: {
            type: Sequelize.TEXT,
            allowNull: false,
          },
          inference_value: {
            type: Sequelize.TEXT,
            allowNull: false,
          },
          timestamp: {
            type: Sequelize.INTEGER,
            allowNull: false,
          },
          created_at: {
            type: Sequelize.DATE,
            defaultValue: Sequelize.fn('now'),
          },
          updated_at: {
            type: Sequelize.DATE,
            defaultValue: Sequelize.fn('now'),
          },
        },
        {
          transaction,
        },
      );

      // Create unique index separately
      await queryInterface.addIndex(
        'allora_model_inferences_latest',
        ['token', 'iso_duration', 'worker_internal_id'],
        {
          unique: true,
          transaction,
        }
      );

      await queryInterface.createTable(
        'tokens',
        {
          token: {
            type: Sequelize.TEXT,
            allowNull: false,
          },
          decimals: {
            type: Sequelize.TEXT,
            allowNull: false,
          },
          native_chain_slug: {
            type: Sequelize.TEXT,
            allowNull: false,
          },
          name: {
            type: Sequelize.TEXT,
            allowNull: false,
          },
          created_at: {
            type: Sequelize.DATE,
            defaultValue: Sequelize.fn('now'),
          },
        },
        {
          transaction,
        },
      );

      await transaction.commit();
    } catch (e) {
      await transaction.rollback();
      throw e;
    }
  },

  async down(queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      // Drop tables in reverse order of creation
      await queryInterface.dropTable('tokens', {
        transaction,
      });
      await queryInterface.dropTable('allora_model_inferences_latest', {
        transaction,
      });
      await queryInterface.dropTable('allora_model_inferences_timeseries', {
        transaction,
      });

      await transaction.commit();
    } catch (e) {
      await transaction.rollback();
      throw e;
    }
  },
};
