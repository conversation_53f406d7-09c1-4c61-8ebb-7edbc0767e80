'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      /**
       * appraisal_proof_pending_asset_ids is an updatable table with all assets
       *  currently with a pending request to modulus. This is used to prevent
       *  duplicate requests to modulus.
       * appraisal_proof_requests & appraisal_proofs are insert-only tables
       */
      await queryInterface.createTable(
        'appraisal_proof_pending_requests',
        {
          asset_id: {
            type: Sequelize.TEXT,
            primaryKey: true,
            allowNull: false,
          },
          inference_id: {
            type: Sequelize.INTEGER,
            allowNull: false,
          },
        },
        { transaction },
      );

      await queryInterface.createTable(
        'appraisal_proof_requests',
        {
          inference_id: {
            // globally unique inference id, even across batches
            type: Sequelize.INTEGER,
            primaryKey: true,
            allowNull: false,
          },
          batch_id: {
            // 1 batch of inferences per proof
            type: Sequelize.INTEGER,
            allowNull: false,
          },
          asset_id: {
            type: Sequelize.TEXT,
            allowNull: false,
          },
          collection_id: {
            type: Sequelize.TEXT,
            allowNull: false,
          },
          upshot_appraisal_wei: {
            // nearest appraisal used in the request
            type: Sequelize.TEXT,
            allowNull: false,
          },
          upshot_appraisal_timestamp: {
            // nearest appraisal used in the request
            type: Sequelize.INTEGER,
            allowNull: false,
          },
          requested_at: {
            // when we first requested the proof to modulus
            type: Sequelize.DATE,
            allowNull: false,
          },
        },
        { transaction },
      );

      await queryInterface.createTable(
        'appraisal_proofs',
        {
          inference_id: {
            type: Sequelize.INTEGER,
            primaryKey: true,
            allowNull: false,
          },
          batch_id: {
            type: Sequelize.INTEGER,
            allowNull: false,
          },
          asset_id: {
            type: Sequelize.TEXT,
            allowNull: false,
          },
          // above is knowable before the proof is verified
          // below is knowable after the proof is made, verified, and received by us
          modulus_appraisal_wei: {
            type: Sequelize.TEXT,
            // allowNull: false, // modulus really should add this
          },
          modulus_appraisal_timestamp: {
            type: Sequelize.INTEGER,
            allowNull: false,
          },
          verified_at: {
            // when it was verified on-chain
            type: Sequelize.DATE,
          },
          zk_metadata_link: {
            type: Sequelize.TEXT,
            allowNull: false,
          },
          batch_inference_index: {
            type: Sequelize.INTEGER,
            allowNull: false,
          },
          tx_hash: {
            // tx of proof verification
            type: Sequelize.TEXT,
          },
          explorer_link: {
            // direct link to verification output
            type: Sequelize.TEXT,
            allowNull: false,
          },
          verifier_address: {
            type: Sequelize.TEXT,
          },
          verifier_explorer_link: {
            type: Sequelize.TEXT,
          },
          fulfilled_at: {
            // when we received the proof from modulus
            type: Sequelize.DATE,
            allowNull: false,
          },
        },
        { transaction },
      );

      await transaction.commit();
    } catch (error) {
      await transaction.rollback();
      // Log all errors, but don't throw to prevent migration failure
      console.log(error);
    }
  },

  async down(queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      await queryInterface.dropTable('appraisal_proof_pending_requests', {
        transaction,
      });
      await queryInterface.dropTable('appraisal_proof_requests', {
        transaction,
      });
      await queryInterface.dropTable('appraisal_proofs', { transaction });

      await transaction.commit();
    } catch (error) {
      await transaction.rollback();
      console.log(error);
    }
  },
};
