'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    try {
      const transaction = await queryInterface.sequelize.transaction();

      // WORKER COUNT
      await queryInterface.createTable(
        'allora_topic_worker_count',
        {
          id: {
            type: Sequelize.BIGINT,
            primaryKey: true,
            autoIncrement: true,
          },
          topic_id: {
            type: Sequelize.INTEGER,
            allowNull: false,
          },
          timestamp: {
            type: Sequelize.INTEGER,
            allowNull: false,
          },
          value: {
            type: Sequelize.INTEGER,
            allowNull: false,
          },
          createdAt: {
            type: Sequelize.DATE,
            allowNull: false,
            defaultValue: Sequelize.fn('NOW'),
          },
        },
        { transaction },
      );

      await queryInterface.addIndex(
        'allora_topic_worker_count',
        ['topic_id', 'timestamp'],
        {
          name: 'idx_allora_topic_worker_count',
          transaction,
        },
      );

      await transaction.commit();
    } catch (e) {
      await transaction.rollback();
      throw e;
    }
  },

  async down(queryInterface, Sequelize) {
    try {
      const transaction = await queryInterface.sequelize.transaction();
      await queryInterface.removeIndex(
        'allora_topic_worker_count',
        'idx_allora_topic_worker_count',
        { transaction },
      );

      await queryInterface.dropTable('allora_topic_worker_count', {
        transaction,
      });
      await transaction.commit();
    } catch {
      await transaction.rollback();
      throw e;
    }
  },
};
