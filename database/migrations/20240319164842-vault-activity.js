'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('vault_activity', {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      vault_address: {
        type: Sequelize.TEXT,
        allowNull: false,
      },
      action_type: {
        type: Sequelize.STRING,
        allowNull: false,
      },
      user_address: {
        type: Sequelize.STRING,
        allowNull: false,
      },
      referrer_address: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      erc20_amount: {
        type: Sequelize.STRING,
        allowNull: false,
      },
      shares_amount: {
        type: Sequelize.STRING,
        allowNull: false,
      },
      tx_hash: {
        type: Sequelize.TEXT,
        allowNull: false,
      },
      block_timestamp: {
        type: Sequelize.INTEGER,
        allowNull: false,
      },
      created_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.fn('now'),
      },
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('vault_activity');
  },
};
