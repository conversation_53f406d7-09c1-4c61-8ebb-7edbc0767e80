'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      await queryInterface.createTable(
        'allora_wallet_balance',
        {
          id: {
            type: Sequelize.BIGINT,
            primaryKey: true,
            autoIncrement: true,
          },
          address: {
            type: Sequelize.TEXT,
            allowNull: false,
          },
          value: {
            type: Sequelize.TEXT,
            allowNull: false,
          },
          timestamp: {
            type: Sequelize.INTEGER,
            allowNull: false,
          },
          createdAt: {
            type: Sequelize.DATE,
            allowNull: false,
            defaultValue: Sequelize.fn('NOW'),
          },
        },
        { transaction },
      );

      await queryInterface.addIndex(
        'allora_wallet_balance',
        ['address', 'timestamp'],
        {
          name: 'idx_allora_wallet_balance',
          transaction,
        },
      );

      await transaction.commit();
    } catch (e) {
      await transaction.rollback();
      throw e;
    }
  },

  async down(queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      await queryInterface.removeIndex(
        'allora_wallet_balance',
        'idx_allora_wallet_balance',
        { transaction },
      );

      await queryInterface.dropTable('allora_wallet_balance', {
        transaction,
      });
      await transaction.commit();
    } catch {
      await transaction.rollback();
      throw e;
    }
  },
};
