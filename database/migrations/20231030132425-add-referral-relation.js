'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('ditto_referral_relation', {
      id: {
        type: Sequelize.UUID,
        primaryKey: true,
      },
      user_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
      },
      points: {
        type: Sequelize.INTEGER,
        allowNull: false,
      },
      related_user_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
      },
      created_at: {
        type: Sequelize.DATE,
        allowNull: false,
      },
    });
    await queryInterface.addIndex('ditto_referral_relation', ['user_id']);
    await queryInterface.addIndex('ditto_referral_relation', [
      'related_user_id',
    ]);

    await queryInterface.addColumn(
      'ditto_points_transactions',
      'boost_points',
      {
        type: Sequelize.INTEGER,
      },
    );
    await queryInterface.addColumn('ditto_points_transactions', 'multiplier', {
      type: Sequelize.FLOAT,
    });

    await queryInterface.addColumn('ditto_points_transactions', 'season_id', {
      type: Sequelize.INTEGER,
    });
    await queryInterface.changeColumn(
      'ditto_points_transactions',
      'related_user_id',
      {
        type: Sequelize.INTEGER,
        allowNull: true,
      },
    );
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('ditto_referral_relation');
    await queryInterface.removeColumn(
      'ditto_points_transactions',
      'boost_points',
    );
    await queryInterface.removeColumn(
      'ditto_points_transactions',
      'multiplier',
    );
    await queryInterface.removeColumn('ditto_points_transactions', 'season_id');
    await queryInterface.changeColumn(
      'ditto_points_transactions',
      'related_user_id',
      {
        type: Sequelize.INTEGER,
        allowNull: false,
      },
    );
  },
};
