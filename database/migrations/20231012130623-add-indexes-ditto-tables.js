'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    // pools table
    await queryInterface.addIndex('pools', ['address'], {
      name: 'idx_pools_address',
    });
    await queryInterface.addIndex('pools', ['collection_id'], {
      name: 'idx_pools_collection_id',
    });
    await queryInterface.addIndex('pools', ['owner'], {
      name: 'idx_pools_owner',
    });
    await queryInterface.addIndex(
      'pools',
      ['address', 'collection_id', 'owner'],
      { name: 'idx_pools_address_collection_id_owner' },
    );

    // latest_pool_prices table
    await queryInterface.addIndex('latest_pool_prices', ['pool_address'], {
      name: 'idx_latest_pool_prices_pool_address',
    });

    // ditto_pool_liquidity_stats table
    await queryInterface.addIndex(
      'ditto_pool_liquidity_stats',
      ['pool_address'],
      { name: 'idx_ditto_pool_liquidity_stats_pool_address' },
    );

    // liquidity_position table
    await queryInterface.addIndex('liquidity_positions', ['id'], {
      name: 'idx_liquidity_positions_id',
    });
    await queryInterface.addIndex('liquidity_positions', ['pool_address'], {
      name: 'idx_liquidity_positions_pool_address',
    });

    // liquidity_position_assets table
    await queryInterface.addIndex(
      'liquidity_position_assets',
      ['liquidity_position_id'],
      {
        name: 'idx_liquidity_position_assets_liquidity_position_id',
      },
    );
    await queryInterface.addIndex('liquidity_position_assets', ['asset_id'], {
      name: 'idx_liquidity_position_assets_asset_id',
    });

    // best_asset_asks table
    await queryInterface.addIndex('best_asset_asks', ['asset_id', 'valid_to'], {
      name: 'idx_best_asset_asks_asset_id_valid_to',
    });

    // best_asset_bids table
    await queryInterface.addIndex('best_asset_bids', ['asset_id', 'valid_to'], {
      name: 'idx_best_asset_bids_asset_id_valid_to',
    });

    // best_collection_asks table
    await queryInterface.addIndex(
      'best_collection_asks',
      ['collection_id', 'valid_to'],
      {
        name: 'idx_best_collection_asks_collection_id_valid_to',
      },
    );

    // best_collection_bids table
    await queryInterface.addIndex(
      'best_collection_bids',
      ['collection_id', 'valid_to'],
      {
        name: 'idx_best_collection_bids_collection_id_valid_to',
      },
    );

    // asset_bucket_proofs table
    await queryInterface.addIndex('asset_bucket_proofs', ['merkle_root'], {
      name: 'idx_abp_merkle_root',
    });
    await queryInterface.addIndex(
      'asset_bucket_proofs',
      ['asset_id', 'merkle_root'],
      {
        name: 'idx_abp_asset_id_merkle_root',
      },
    );

    // permitters table
    await queryInterface.addIndex('permitters', ['address'], {
      name: 'idx_permitters_address',
    });
    await queryInterface.addIndex('permitters', ['merkle_root'], {
      name: 'idx_permitters_merkle_root',
    });

    // bucket_snapshots table
    await queryInterface.addIndex('bucket_snapshots', ['merkle_root'], {
      name: 'idx_bucket_snapshots_merkle_root',
    });

    // buckets table
    await queryInterface.addIndex('buckets', ['id'], {
      name: 'idx_buckets_id',
    });
  },

  async down(queryInterface, Sequelize) {
    // pools table
    await queryInterface.removeIndex('pools', 'idx_pools_address');
    await queryInterface.removeIndex('pools', 'idx_pools_collection_id');
    await queryInterface.removeIndex('pools', 'idx_pools_owner');
    await queryInterface.removeIndex(
      'pools',
      'idx_pools_address_collection_id_owner',
    );

    // latest_pool_prices table
    await queryInterface.removeIndex(
      'latest_pool_prices',
      'idx_latest_pool_prices_pool_address',
    );

    // ditto_pool_liquidity_stats table
    await queryInterface.removeIndex(
      'ditto_pool_liquidity_stats',
      'idx_ditto_pool_liquidity_stats_pool_address',
    );

    // liquidity_positions table
    await queryInterface.removeIndex(
      'liquidity_positions',
      'idx_liquidity_positions_id',
    );
    await queryInterface.removeIndex(
      'liquidity_positions',
      'idx_liquidity_positions_pool_address',
    );

    // liquidity_position_assets table
    await queryInterface.removeIndex(
      'liquidity_position_assets',
      'idx_liquidity_position_assets_liquidity_position_id',
    );
    await queryInterface.removeIndex(
      'liquidity_position_assets',
      'idx_liquidity_position_assets_asset_id',
    );

    // best_asset_asks table
    await queryInterface.removeIndex(
      'best_asset_asks',
      'idx_best_asset_asks_asset_id_valid_to',
    );

    // best_asset_bids table
    await queryInterface.removeIndex(
      'best_asset_bids',
      'idx_best_asset_bids_asset_id_valid_to',
    );

    // best_collection_asks table
    await queryInterface.removeIndex(
      'best_collection_asks',
      'idx_best_collection_asks_collection_id_valid_to',
    );

    // best_collection_bids table
    await queryInterface.removeIndex(
      'best_collection_bids',
      'idx_best_collection_bids_collection_id_valid_to',
    );

    // asset_bucket_proofs table
    await queryInterface.removeIndex(
      'asset_bucket_proofs',
      'idx_abp_merkle_root',
    );
    await queryInterface.removeIndex(
      'asset_bucket_proofs',
      'idx_abp_asset_id_merkle_root',
    );

    // permitters table
    await queryInterface.removeIndex('permitters', 'idx_permitters_address');
    await queryInterface.removeIndex(
      'permitters',
      'idx_permitters_merkle_root',
    );

    // bucket_snapshots table
    await queryInterface.removeIndex(
      'bucket_snapshots',
      'idx_bucket_snapshots_merkle_root',
    );

    // buckets table
    await queryInterface.removeIndex('buckets', 'idx_buckets_id');
  },
};
