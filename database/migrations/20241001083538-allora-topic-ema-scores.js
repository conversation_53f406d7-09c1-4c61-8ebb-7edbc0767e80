'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      // worker scores
      await queryInterface.createTable(
        'allora_topic_worker_ema_scores',
        {
          address: {
            type: Sequelize.STRING,
            primaryKey: true,
          },
          topic_id: {
            type: Sequelize.INTEGER,
            primaryKey: true,
          },
          score: {
            type: Sequelize.NUMERIC,
            allowNull: true,
          },
          is_active: {
            type: Sequelize.BOOLEAN,
            allowNull: true,
          },
          createdAt: {
            type: Sequelize.DATE,
            allowNull: false,
            defaultValue: Sequelize.fn('NOW'),
          },
          updatedAt: {
            type: Sequelize.DATE,
            allowNull: false,
            defaultValue: Sequelize.fn('NOW'),
          },
        },
        { transaction },
      );

      await queryInterface.addIndex(
        'allora_topic_worker_ema_scores',
        ['topic_id', 'is_active'],
        {
          name: 'idx_allora_topic_worker_ema_scores',
          transaction,
        },
      );

      // reputer scores
      await queryInterface.createTable(
        'allora_topic_reputer_ema_scores',
        {
          address: {
            type: Sequelize.STRING,
            primaryKey: true,
          },
          topic_id: {
            type: Sequelize.INTEGER,
            primaryKey: true,
          },
          score: {
            type: Sequelize.NUMERIC,
            allowNull: true,
          },
          is_active: {
            type: Sequelize.BOOLEAN,
            allowNull: true,
          },
          createdAt: {
            type: Sequelize.DATE,
            allowNull: false,
            defaultValue: Sequelize.fn('NOW'),
          },
          updatedAt: {
            type: Sequelize.DATE,
            allowNull: false,
            defaultValue: Sequelize.fn('NOW'),
          },
        },
        { transaction },
      );

      await queryInterface.addIndex(
        'allora_topic_reputer_ema_scores',
        ['topic_id', 'is_active'],
        {
          name: 'idx_allora_topic_reputer_ema_scores',
          transaction,
        },
      );
      await transaction.commit();
    } catch (e) {
      await transaction.rollback();
      throw e;
    }
  },

  async down(queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      // worker scores
      await queryInterface.removeIndex(
        'allora_topic_worker_ema_scores',
        'idx_allora_topic_worker_ema_scores',
        { transaction },
      );

      await queryInterface.dropTable('allora_topic_worker_ema_scores', {
        transaction,
      });

      // reputer scores
      await queryInterface.removeIndex(
        'allora_topic_reputer_ema_scores',
        'idx_allora_topic_reputer_ema_scores',
        { transaction },
      );

      await queryInterface.dropTable('allora_topic_reputer_ema_scores', {
        transaction,
      });
      await transaction.commit();
    } catch {
      await transaction.rollback();
      throw e;
    }
  },
};
