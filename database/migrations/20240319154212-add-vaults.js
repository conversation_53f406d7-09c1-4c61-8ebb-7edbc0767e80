'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      await queryInterface.createTable('vaults', {
        address: {
          type: Sequelize.STRING,
          allowNull: false,
        },
        name: {
          type: Sequelize.TEXT,
          allowNull: true,
        },
        description: {
          type: Sequelize.TEXT,
        },
        vault_type: {
          type: Sequelize.TEXT,
          allowNull: false,
        },
        token_address: {
          type: Sequelize.STRING,
          allowNull: false,
        },
        token_symbol: {
          type: Sequelize.STRING,
          allowNull: true,
        },
        token_name: {
          type: Sequelize.STRING,
          allowNull: true,
        },
        token_image_url: {
          type: Sequelize.TEXT,
          allowNull: true,
        },
        token_decimals: {
          type: Sequelize.INTEGER,
          allowNull: true,
        },
        cg_asset_id: {
          type: Sequelize.STRING,
          allowNull: true,
        },
        performance_fee: {
          type: Sequelize.TEXT,
          allowNull: false,
        },
        tx_hash: {
          type: Sequelize.STRING,
          allowNull: false,
        },
        block_timestamp: {
          type: Sequelize.INTEGER,
          allowNull: false,
        },
        chain_id: {
          type: Sequelize.INTEGER,
          allowNull: false,
        },
        points_enabled: {
          type: Sequelize.BOOLEAN,
          allowNull: false,
        },
        points_multiplier: {
          type: Sequelize.DECIMAL,
          allowNull: false,
        },
        created_at: {
          type: Sequelize.DATE,
          defaultValue: Sequelize.fn('now'),
        },
      });
      await queryInterface.addConstraint('vaults', {
        fields: ['address', 'chain_id'],
        type: 'primary key',
        name: 'vaults_pkey',
        transaction,
      });
      await transaction.commit();
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('vaults');
  },
};
