'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      // create preconfigured badges table with percentiles for allora forge competitions
      await queryInterface.createTable(
        'allora_forge_badges',
        {
          id: {
            type: Sequelize.INTEGER,
            autoIncrement: true,
            primaryKey: true,
          },
          name: {
            type: Sequelize.STRING,
            allowNull: false,
          },
          description: {
            type: Sequelize.STRING,
            allowNull: true,
          },
          preview_image_url: {
            type: Sequelize.STRING,
            allowNull: true,
          },
          percentile: {
            type: Sequelize.NUMERIC,
            allowNull: false,
          },
          updated_at: {
            type: Sequelize.DATE,
            allowNull: false,
            defaultValue: Sequelize.fn('NOW'),
          },
          created_at: {
            type: Sequelize.DATE,
            allowNull: false,
            defaultValue: Sequelize.fn('NOW'),
          },
        },
        { transaction },
      );

      // create forge point transactions table
      await queryInterface.createTable(
        'allora_forge_point_transactions',
        {
          id: {
            type: Sequelize.INTEGER,
            primaryKey: true,
            autoIncrement: true,
          },
          user_id: {
            type: Sequelize.STRING,
            allowNull: false,
          },
          points: {
            type: Sequelize.DECIMAL,
            allowNull: false,
            defaultValue: 0,
          },
          competition_id: {
            type: Sequelize.INTEGER,
            allowNull: false,
            references: {
              model: 'allora_forge_competitions',
              key: 'id',
            },
          },
          block_number: {
            type: Sequelize.INTEGER,
            allowNull: true,
          },
          created_at: {
            type: Sequelize.DATE,
            defaultValue: Sequelize.fn('now'),
          },
        },
        {
          transaction,
          indexes: [
            {
              fields: ['competition_id', 'user_id'],
            },
            {
              fields: ['competition_id'],
            },
          ],
        },
      );

      // create competition badges table
      await queryInterface.createTable(
        'allora_forge_competition_badges',
        {
          id: {
            type: Sequelize.INTEGER,
            autoIncrement: true,
            primaryKey: true,
          },
          allora_user_id: {
            type: Sequelize.UUID,
            allowNull: false,
            references: {
              model: 'allora_users',
              key: 'id',
            },
          },
          allora_forge_competition_id: {
            type: Sequelize.INTEGER,
            allowNull: false,
            references: {
              model: 'allora_forge_competitions',
              key: 'id',
            },
          },
          allora_forge_badge_id: {
            type: Sequelize.INTEGER,
            allowNull: false,
            references: {
              model: 'allora_forge_badges',
              key: 'id',
            },
          },
          tags: {
            type: Sequelize.ARRAY(Sequelize.STRING),
            allowNull: true,
          },
          updated_at: {
            type: Sequelize.DATE,
            allowNull: false,
            defaultValue: Sequelize.fn('NOW'),
          },
          created_at: {
            type: Sequelize.DATE,
            allowNull: false,
            defaultValue: Sequelize.fn('NOW'),
          },
        },
        {
          transaction,
          uniqueKeys: {
            unique_allora_user_id_and_allora_forge_competition_id_and_allora_forge_badge_id:
              {
                fields: [
                  'allora_user_id',
                  'allora_forge_competition_id',
                  'allora_forge_badge_id',
                ],
              },
          },
        },
      );

      // leaderboard
      await queryInterface.createTable(
        'allora_forge_leaderboard',
        {
          user_id: {
            type: Sequelize.UUID,
            allowNull: false,
            primaryKey: true,
          },
          total_points: {
            type: Sequelize.DOUBLE,
            allowNull: false,
            defaultValue: 0,
          },
          created_at: {
            type: Sequelize.DATE,
            allowNull: false,
            defaultValue: Sequelize.fn('now'),
          },
          updated_at: {
            type: Sequelize.DATE,
            allowNull: false,
            defaultValue: Sequelize.fn('now'),
          },
        },
        { transaction },
      );

      await transaction.commit();
    } catch (e) {
      await transaction.rollback();
      throw e;
    }
  },

  async down(queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      // Drop tables in reverse order of creation
      await queryInterface.dropTable('allora_forge_leaderboard', {
        transaction,
      });
      await queryInterface.dropTable('allora_forge_competition_badges', {
        transaction,
      });
      await queryInterface.dropTable('allora_forge_point_transactions', {
        transaction,
      });
      await queryInterface.dropTable('allora_forge_badges', { transaction });

      await transaction.commit();
    } catch (e) {
      await transaction.rollback();
      throw e;
    }
  },
};
