'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();

    try {
      await queryInterface.sequelize.query(
        `        
        CREATE TABLE allora_ohlc_timeseries_history (
            ticker VARCHAR(50) NOT NULL,
            exchange_code VARCHAR(50) NOT NULL,
            date TIMESTAMP WITH TIME ZONE NOT NULL,
            open NUMERIC NOT NULL,
            high NUMERIC NOT NULL,
            low NUMERIC NOT NULL,
            close NUMERIC NOT NULL,
            trades_done INTEGER,
            volume NUMERIC,
            volume_notional NUMERIC,
            created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
            
            -- the primary key must include the partition key
            PRIMARY KEY (ticker, exchange_code, date)
        
        ) PARTITION BY LIST(ticker);
            
        -- the index on the partition key is necessary for efficient partitioning
        CREATE INDEX ON allora_ohlc_timeseries_history (ticker);
            
        -- any additional indexes must include the partition key
        -- indexes will be automatically propagated to existing and new partitions that will be added
        CREATE INDEX ON allora_ohlc_timeseries_history (ticker, (DATE(TIMEZONE('UTC', date))));
      `,
        { transaction },
      );

      await queryInterface.sequelize.query(
        `
        CREATE TABLE allora_ohlc_timeseries_history_default
        PARTITION OF allora_ohlc_timeseries_history DEFAULT;        

        CREATE TABLE allora_ohlc_timeseries_history_aixbtusdt
        PARTITION OF allora_ohlc_timeseries_history
        FOR VALUES IN ('aixbtusdt');
      
        CREATE TABLE allora_ohlc_timeseries_history_arbusd
        PARTITION OF allora_ohlc_timeseries_history
        FOR VALUES IN ('arbusd');
      
        CREATE TABLE allora_ohlc_timeseries_history_bnbusd
        PARTITION OF allora_ohlc_timeseries_history
        FOR VALUES IN ('bnbusd');
      
        CREATE TABLE allora_ohlc_timeseries_history_btcusd
        PARTITION OF allora_ohlc_timeseries_history
        FOR VALUES IN ('btcusd');
      
        CREATE TABLE allora_ohlc_timeseries_history_ethusd
        PARTITION OF allora_ohlc_timeseries_history
        FOR VALUES IN ('ethusd');
      
        CREATE TABLE allora_ohlc_timeseries_history_frxethweth
        PARTITION OF allora_ohlc_timeseries_history
        FOR VALUES IN ('frxethweth');
        
        CREATE TABLE allora_ohlc_timeseries_history_gameveth
        PARTITION OF allora_ohlc_timeseries_history
        FOR VALUES IN ('gameveth');
        
        CREATE TABLE allora_ohlc_timeseries_history_lunausdt
        PARTITION OF allora_ohlc_timeseries_history
        FOR VALUES IN ('lunausdt');
        
        CREATE TABLE allora_ohlc_timeseries_history_methusdt
        PARTITION OF allora_ohlc_timeseries_history
        FOR VALUES IN ('methusdt');
        
        CREATE TABLE allora_ohlc_timeseries_history_sekoiaeth
        PARTITION OF allora_ohlc_timeseries_history
        FOR VALUES IN ('sekoiaeth');
        
        CREATE TABLE allora_ohlc_timeseries_history_solusd
        PARTITION OF allora_ohlc_timeseries_history
        FOR VALUES IN ('solusd');
        
        CREATE TABLE allora_ohlc_timeseries_history_vadereth
        PARTITION OF allora_ohlc_timeseries_history
        FOR VALUES IN ('vadereth');
        
        CREATE TABLE allora_ohlc_timeseries_history_virtualusdt
        PARTITION OF allora_ohlc_timeseries_history
        FOR VALUES IN ('virtualusdt');
      `,
        { transaction },
      );

      await transaction.commit();
    } catch (e) {
      await transaction.rollback();
      throw e;
    }
  },

  async down(queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();

    try {
      await queryInterface.sequelize.query(
        `
        DROP TABLE IF EXISTS allora_ohlc_timeseries_history_default;
        DROP TABLE IF EXISTS allora_ohlc_timeseries_history_aixbtusdt;
        DROP TABLE IF EXISTS allora_ohlc_timeseries_history_arbusd;
        DROP TABLE IF EXISTS allora_ohlc_timeseries_history_bnbusd;
        DROP TABLE IF EXISTS allora_ohlc_timeseries_history_btcusd;
        DROP TABLE IF EXISTS allora_ohlc_timeseries_history_ethusd;
        DROP TABLE IF EXISTS allora_ohlc_timeseries_history_frxethweth;
        DROP TABLE IF EXISTS allora_ohlc_timeseries_history_gameveth;
        DROP TABLE IF EXISTS allora_ohlc_timeseries_history_lunausdt;
        DROP TABLE IF EXISTS allora_ohlc_timeseries_history_methusdt;
        DROP TABLE IF EXISTS allora_ohlc_timeseries_history_sekoiaeth;
        DROP TABLE IF EXISTS allora_ohlc_timeseries_history_solusd;
        DROP TABLE IF EXISTS allora_ohlc_timeseries_history_vadereth;
        DROP TABLE IF EXISTS allora_ohlc_timeseries_history_virtualusdt;
        DROP TABLE IF EXISTS allora_ohlc_timeseries_history;
      `,
        { transaction },
      );

      await transaction.commit();
    } catch (e) {
      await transaction.rollback();
      throw e;
    }
  },
};
