'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    // Create the friend_tech_profile_stats table
    await queryInterface.createTable('friend_tech_profile_stats', {
      market_cap_eth: {
        type: Sequelize.DOUBLE,
        allowNull: true,
      },
      share_price_wei: {
        type: Sequelize.TEXT,
        allowNull: true,
      },
      fees_earned_wei: {
        type: Sequelize.TEXT,
        allowNull: true,
      },
      supply: {
        type: Sequelize.INTEGER,
        allowNull: true,
      },
      holders: {
        type: Sequelize.INTEGER,
        allowNull: true,
        defaultValue: 0,
      },
      holding: {
        type: Sequelize.INTEGER,
        allowNull: true,
        defaultValue: 0,
      },
      buy_count: {
        type: Sequelize.INTEGER,
        allowNull: true,
      },
      sell_count: {
        type: Sequelize.INTEGER,
        allowNull: true,
      },
      buy_volume_wei: {
        type: Sequelize.TEXT,
        allowNull: true,
      },
      sell_volume_wei: {
        type: Sequelize.TEXT,
        allowNull: true,
      },
      wallet_address: {
        type: Sequelize.TEXT,
        allowNull: false,
        primaryKey: true,
      },
      created_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      },
    });

    // Create the friend_tech_profiles table
    await queryInterface.createTable('friend_tech_profiles', {
      wallet_address: {
        type: Sequelize.TEXT,
        allowNull: false,
        primaryKey: true,
      },
      twitter_username: {
        type: Sequelize.TEXT,
        allowNull: true,
      },
      telegram_username: {
        type: Sequelize.TEXT,
        allowNull: true,
      },
      telegram_id: {
        type: Sequelize.TEXT,
        allowNull: true,
      },
      trending_rank: {
        type: Sequelize.INTEGER,
        allowNull: true,
      },
      appraisal_wei: {
        type: Sequelize.TEXT,
        allowNull: true,
      },
      encrypted_key: {
        type: Sequelize.TEXT,
        allowNull: true,
      },
      iv: {
        type: Sequelize.TEXT,
        allowNull: true,
      },
      started_at: {
        type: Sequelize.DATE,
        allowNull: true,
      },
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('friend_tech_profile_stats');
    await queryInterface.dropTable('friend_tech_profiles');
  },
};
