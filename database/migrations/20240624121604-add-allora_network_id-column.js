'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    const transaction = await queryInterface.sequelize.transaction();

    try {
      await queryInterface.addColumn(
        'allora_topics',
        'allora_network_id',
        {
          type: Sequelize.STRING,
        },
        { transaction },
      );
      await queryInterface.addColumn(
        'adapter_locations',
        'allora_network_id',
        {
          type: Sequelize.STRING,
        },
        { transaction },
      );

      await queryInterface.sequelize.query(
        "UPDATE allora_topics SET allora_network_id = 'TESTNET'",
        { transaction },
      );
      await queryInterface.sequelize.query(
        "UPDATE adapter_locations SET allora_network_id = 'TESTNET'",
        { transaction },
      );
      await transaction.commit();
    } catch (e) {
      await transaction.rollback();
      throw e;
    }
  },

  down: async (queryInterface, Sequelize) => {
    const transaction = await queryInterface.sequelize.transaction();

    try {
      await queryInterface.removeColumn('allora_topics', 'allora_network_id', {
        transaction,
      });
      await queryInterface.removeColumn(
        'adapter_locations',
        'allora_network_id',
        {
          transaction,
        },
      );

      await transaction.commit();
    } catch (e) {
      await transaction.rollback();
      throw e;
    }
  },
};
