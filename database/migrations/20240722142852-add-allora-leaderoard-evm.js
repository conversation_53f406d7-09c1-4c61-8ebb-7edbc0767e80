'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      // leaderboard
      await queryInterface.createTable(
        'allora_leaderboard_evm',
        {
          user_id: {
            type: Sequelize.UUID,
            allowNull: false,
            primaryKey: true,
          },
          total_points: {
            type: Sequelize.DOUBLE,
            allowNull: false,
            defaultValue: 0,
          },
          created_at: {
            type: Sequelize.DATE,
            allowNull: false,
            defaultValue: Sequelize.fn('now'),
          },
          updated_at: {
            type: Sequelize.DATE,
            allowNull: false,
            defaultValue: Sequelize.fn('now'),
          },
        },
        { transaction },
      );
      await transaction.commit();
    } catch (error) {
      console.error(error);
      await transaction.rollback();
      throw error;
    }
  },

  async down(queryInterface) {
    const transaction = await queryInterface.sequelize.transaction();
    await queryInterface.dropTable('allora_leaderboard_evm', transaction);
    await transaction.commit();
  },
};
