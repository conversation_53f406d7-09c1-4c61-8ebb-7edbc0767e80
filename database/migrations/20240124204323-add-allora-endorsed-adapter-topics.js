'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      await queryInterface.createTable(
        'allora_topic_to_endorsed_adapter_topics',
        {
          allora_topic_id: {
            type: Sequelize.TEXT,
            allowNull: false,
          },
          chain_slug: {
            type: Sequelize.TEXT,
            allowNull: false,
          },
          adapter_topic_id: {
            type: Sequelize.TEXT,
            allowNull: false,
          },
        },
        { transaction },
      );

      await queryInterface.createTable(
        'allora_topics',
        {
          id: {
            type: Sequelize.TEXT,
            allowNull: false,
          },
          name: {
            type: Sequelize.TEXT,
            allowNull: false,
          },
          b7s_function_id: {
            type: Sequelize.TEXT,
            allowNull: false,
          },
          b7s_method: {
            type: Sequelize.TEXT,
            allowNull: false,
          },
          b7s_env_vars: {
            type: Sequelize.JSONB,
            allowNull: false,
          },
        },
        { transaction },
      );

      console.log(
        'It is up to the dev who merges this code to ensure that the tables added above in this migration are properly populated with the most up-to-date data. This is not done automatically.',
      );

      await transaction.commit();
    } catch (error) {
      await transaction.rollback();
      // Log all errors, but don't throw to prevent migration failure
      console.error(error);
    }
  },

  async down(queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      await queryInterface.dropTable(
        'allora_topic_to_endorsed_adapter_topics',
        { transaction },
      );
      await queryInterface.dropTable('allora_topics', { transaction });

      await transaction.commit();
    } catch (error) {
      await transaction.rollback();
      console.error(error);
    }
  },
};
