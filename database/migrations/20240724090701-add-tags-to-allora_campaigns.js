'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      // add tags column to the allora_campaigns table
      await queryInterface.addColumn(
        'allora_campaigns',
        'tags',
        {
          type: Sequelize.JSONB,
          allowNull: true,
          defaultValue: null,
        },
        { transaction },
      );

      await transaction.commit();
    } catch (error) {
      console.error(error);
      await transaction.rollback();
      throw error;
    }
  },

  async down(queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();
    await queryInterface.removeColumn('allora_campaigns', 'tags', {
      transaction,
    });
    await transaction.commit();
  },
};
