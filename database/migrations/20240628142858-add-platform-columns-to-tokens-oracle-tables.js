'use strict';
module.exports = {
  async up(queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      await queryInterface.addColumn(
        'tokens_oracle_supported_tokens',
        'platform',
        {
          type: Sequelize.STRING,
          allowNull: true,
        },
        { transaction },
      );
      await queryInterface.addColumn(
        'tokens_oracle_supported_tokens',
        'address',
        {
          type: Sequelize.STRING,
          allowNull: true,
        },
        { transaction },
      );

      await queryInterface.addColumn(
        'tokens_oracle_block_data',
        'platform',
        {
          type: Sequelize.STRING,
          allowNull: true,
        },
        { transaction },
      );
      await queryInterface.addColumn(
        'tokens_oracle_block_data',
        'address',
        {
          type: Sequelize.STRING,
          allowNull: true,
        },
        { transaction },
      );
      await transaction.commit();
    } catch (error) {
      console.error(error);
      await transaction.rollback();
      throw error;
    }
  },

  async down(queryInterface) {
    const transaction = await queryInterface.sequelize.transaction();
    await queryInterface.removeColumn(
      'tokens_oracle_supported_tokens',
      'platform',
      {
        transaction,
      },
    );
    await queryInterface.removeColumn(
      'tokens_oracle_supported_tokens',
      'address',
      {
        transaction,
      },
    );
    await queryInterface.removeColumn('tokens_oracle_block_data', 'platform', {
      transaction,
    });
    await queryInterface.removeColumn('tokens_oracle_block_data', 'address', {
      transaction,
    });
    await transaction.commit();
  },
};
