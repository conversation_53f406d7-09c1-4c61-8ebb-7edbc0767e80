'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.addColumn(
      'pools',
      'pool_manager_updater_last_update',
      {
        type: Sequelize.DATE,
        defaultValue: Sequelize.fn('now'),
      },
    );
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.removeColumn(
      'pools',
      'pool_manager_updater_last_update',
    );
  },
};
