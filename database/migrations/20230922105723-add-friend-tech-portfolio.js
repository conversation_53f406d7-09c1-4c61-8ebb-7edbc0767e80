'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    // Add 'recommendations' column to friend_tech_profiles
    await queryInterface.addColumn('friend_tech_profiles', 'recommendations', {
      type: Sequelize.JSONB,
      allowNull: true,
    });

    // Create the friend_tech_portfolios table
    await queryInterface.createTable('friend_tech_portfolios', {
      wallet_address: {
        type: Sequelize.TEXT,
        allowNull: false,
        primaryKey: true,
      },
      subject: {
        type: Sequelize.TEXT,
        allowNull: false,
        primaryKey: true,
      },
      portfolio_value_wei: {
        type: Sequelize.TEXT,
        allowNull: true,
      },
      share_count: {
        type: Sequelize.INTEGER,
        allowNull: true,
      },
      avg_price_wei: {
        type: Sequelize.TEXT,
        allowNull: true,
      },
      median_price_wei: {
        type: Sequelize.TEXT,
        allowNull: true,
      },
    });
  },

  async down(queryInterface, Sequelize) {
    // Remove 'recommendations' column from friend_tech_profiles
    await queryInterface.removeColumn(
      'friend_tech_profiles',
      'recommendations',
    );

    // Drop the friend_tech_portfolios table
    await queryInterface.dropTable('friend_tech_portfolios');
  },
};
