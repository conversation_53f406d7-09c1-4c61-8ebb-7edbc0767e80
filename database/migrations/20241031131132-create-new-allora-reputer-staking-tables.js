'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      // Create testnet table
      await queryInterface.createTable(
        'allora_topic_reputer_stake_allora_testnet_1',
        {
          id: {
            type: Sequelize.BIGINT,
            primaryKey: true,
            autoIncrement: true,
          },
          type: {
            type: Sequelize.TEXT,
            allowNull: true,
          },
          topic_id: {
            type: Sequelize.INTEGER,
            allowNull: true,
          },
          is_delegation: {
            type: Sequelize.BOOLEAN,
            allowNull: true,
          },
          sender: {
            type: Sequelize.TEXT,
            allowNull: true,
          },
          amount: {
            type: Sequelize.NUMERIC,
            allowNull: true,
          },
          reputer_address: {
            type: Sequelize.TEXT,
            allowNull: true,
          },
          target_block_height: {
            type: Sequelize.INTEGER,
            allowNull: true,
          },
          block_timestamp: {
            type: Sequelize.DATE,
            allowNull: true,
          },
          block_number: {
            type: Sequelize.INTEGER,
            allowNull: true,
          },
          createdAt: {
            type: Sequelize.DATE,
            allowNull: false,
            defaultValue: Sequelize.fn('NOW'),
          },
        },
        { transaction },
      );

      // Create devnet table
      await queryInterface.createTable(
        'allora_topic_reputer_stake_allora_devnet_1',
        {
          id: {
            type: Sequelize.BIGINT,
            primaryKey: true,
            autoIncrement: true,
          },
          type: {
            type: Sequelize.TEXT,
            allowNull: true,
          },
          topic_id: {
            type: Sequelize.INTEGER,
            allowNull: true,
          },
          is_delegation: {
            type: Sequelize.BOOLEAN,
            allowNull: true,
          },
          sender: {
            type: Sequelize.TEXT,
            allowNull: true,
          },
          amount: {
            type: Sequelize.NUMERIC,
            allowNull: true,
          },
          reputer_address: {
            type: Sequelize.TEXT,
            allowNull: true,
          },
          target_block_height: {
            type: Sequelize.INTEGER,
            allowNull: true,
          },
          block_timestamp: {
            type: Sequelize.DATE,
            allowNull: true,
          },
          block_number: {
            type: Sequelize.INTEGER,
            allowNull: true,
          },
          createdAt: {
            type: Sequelize.DATE,
            allowNull: false,
            defaultValue: Sequelize.fn('NOW'),
          },
        },
        { transaction },
      );

      // Create mainnet table
      await queryInterface.createTable(
        'allora_topic_reputer_stake_allora_mainnet_1',
        {
          id: {
            type: Sequelize.BIGINT,
            primaryKey: true,
            autoIncrement: true,
          },
          type: {
            type: Sequelize.TEXT,
            allowNull: true,
          },
          topic_id: {
            type: Sequelize.INTEGER,
            allowNull: true,
          },
          is_delegation: {
            type: Sequelize.BOOLEAN,
            allowNull: true,
          },
          sender: {
            type: Sequelize.TEXT,
            allowNull: true,
          },
          amount: {
            type: Sequelize.NUMERIC,
            allowNull: true,
          },
          reputer_address: {
            type: Sequelize.TEXT,
            allowNull: true,
          },
          target_block_height: {
            type: Sequelize.INTEGER,
            allowNull: true,
          },
          block_timestamp: {
            type: Sequelize.DATE,
            allowNull: true,
          },
          block_number: {
            type: Sequelize.INTEGER,
            allowNull: true,
          },
          createdAt: {
            type: Sequelize.DATE,
            allowNull: false,
            defaultValue: Sequelize.fn('NOW'),
          },
        },
        { transaction },
      );

      // Create indexes for testnet
      await queryInterface.addIndex(
        'allora_topic_reputer_stake_allora_testnet_1',
        ['topic_id', 'reputer_address', 'amount'],
        {
          name: 'idx_allora_topic_reputer_stake_allora_testnet_1_topic_id_reputer_address_amount',
          transaction,
        },
      );

      await queryInterface.addIndex(
        'allora_topic_reputer_stake_allora_testnet_1',
        ['topic_id', 'sender', 'amount'],
        {
          name: 'idx_allora_topic_reputer_stake_allora_testnet_1_topic_id_sender_amount',
          transaction,
        },
      );

      // Create indexes for devnet
      await queryInterface.addIndex(
        'allora_topic_reputer_stake_allora_devnet_1',
        ['topic_id', 'reputer_address', 'amount'],
        {
          name: 'idx_allora_topic_reputer_stake_allora_devnet_1_topic_id_reputer_address_amount',
          transaction,
        },
      );

      await queryInterface.addIndex(
        'allora_topic_reputer_stake_allora_devnet_1',
        ['topic_id', 'sender', 'amount'],
        {
          name: 'idx_allora_topic_reputer_stake_allora_devnet_1_topic_id_sender_amount',
          transaction,
        },
      );

      // Create indexes for mainnet
      await queryInterface.addIndex(
        'allora_topic_reputer_stake_allora_mainnet_1',
        ['topic_id', 'reputer_address', 'amount'],
        {
          name: 'idx_allora_topic_reputer_stake_allora_mainnet_1_topic_id_reputer_address_amount',
          transaction,
        },
      );

      await queryInterface.addIndex(
        'allora_topic_reputer_stake_allora_mainnet_1',
        ['topic_id', 'sender', 'amount'],
        {
          name: 'idx_allora_topic_reputer_stake_allora_mainnet_1_topic_id_sender_amount',
          transaction,
        },
      );

      await transaction.commit();
    } catch (e) {
      await transaction.rollback();
      throw e;
    }
  },

  async down(queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      // Drop testnet indexes and table
      await queryInterface.removeIndex(
        'allora_topic_reputer_stake_allora_testnet_1',
        'idx_allora_topic_reputer_stake_allora_testnet_1_topic_id_reputer_address_amount',
        { transaction },
      );
      await queryInterface.removeIndex(
        'allora_topic_reputer_stake_allora_testnet_1',
        'idx_allora_topic_reputer_stake_allora_testnet_1_topic_id_sender_amount',
        { transaction },
      );
      await queryInterface.dropTable(
        'allora_topic_reputer_stake_allora_testnet_1',
        {
          transaction,
        },
      );

      // Drop devnet indexes and table
      await queryInterface.removeIndex(
        'allora_topic_reputer_stake_allora_devnet_1',
        'idx_allora_topic_reputer_stake_allora_devnet_1_topic_id_reputer_address_amount',
        { transaction },
      );
      await queryInterface.removeIndex(
        'allora_topic_reputer_stake_allora_devnet_1',
        'idx_allora_topic_reputer_stake_allora_devnet_1_topic_id_sender_amount',
        { transaction },
      );
      await queryInterface.dropTable(
        'allora_topic_reputer_stake_allora_devnet_1',
        {
          transaction,
        },
      );

      // Drop mainnet indexes and table
      await queryInterface.removeIndex(
        'allora_topic_reputer_stake_allora_mainnet_1',
        'idx_allora_topic_reputer_stake_allora_mainnet_1_topic_id_reputer_address_amount',
        { transaction },
      );
      await queryInterface.removeIndex(
        'allora_topic_reputer_stake_allora_mainnet_1',
        'idx_allora_topic_reputer_stake_allora_mainnet_1_topic_id_sender_amount',
        { transaction },
      );
      await queryInterface.dropTable(
        'allora_topic_reputer_stake_allora_mainnet_1',
        {
          transaction,
        },
      );

      await transaction.commit();
    } catch (e) {
      await transaction.rollback();
      throw e;
    }
  },
};
