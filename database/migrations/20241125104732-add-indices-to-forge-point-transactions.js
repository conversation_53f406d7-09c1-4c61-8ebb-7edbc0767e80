'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      await queryInterface.addIndex(
        'allora_forge_point_transactions',
        ['competition_id', 'user_id'],
        {
          name: 'idx_forge_points_competition_user',
          transaction,
        },
      );

      await queryInterface.addIndex(
        'allora_forge_point_transactions',
        ['competition_id'],
        {
          name: 'idx_forge_points_competition',
          transaction,
        },
      );

      await transaction.commit();
    } catch (e) {
      await transaction.rollback();
      throw e;
    }
  },

  async down(queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      await queryInterface.removeIndex(
        'allora_forge_point_transactions',
        'idx_forge_points_competition_user',
        { transaction },
      );

      await queryInterface.removeIndex(
        'allora_forge_point_transactions',
        'idx_forge_points_competition',
        { transaction },
      );

      await transaction.commit();
    } catch (e) {
      await transaction.rollback();
      throw e;
    }
  },
};
