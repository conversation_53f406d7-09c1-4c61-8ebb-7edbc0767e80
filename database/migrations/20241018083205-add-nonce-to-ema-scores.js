'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      // worker ema scores - add nonce column and index
      await queryInterface.sequelize.query(
        `
        ALTER TABLE allora_topic_worker_ema_scores
        ADD COLUMN nonce INTEGER;
      `,
        { transaction },
      );

      await queryInterface.addIndex(
        'allora_topic_worker_ema_scores',
        ['address', 'nonce'],
        {
          name: 'idx_allora_topic_worker_ema_scores_address_nonce',
          transaction,
        },
      );

      // reputer ema scores - add nonce column and index
      await queryInterface.sequelize.query(
        `
        ALTER TABLE allora_topic_reputer_ema_scores
        ADD COLUMN nonce INTEGER;
      `,
        { transaction },
      );

      await queryInterface.addIndex(
        'allora_topic_reputer_ema_scores',
        ['address', 'nonce'],
        {
          name: 'idx_allora_topic_reputer_ema_scores_address_nonce',
          transaction,
        },
      );

      await transaction.commit();
    } catch (e) {
      await transaction.rollback();
      throw e;
    }
  },

  down: async (queryInterface, Sequelize) => {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      // worker ema scores - drop index and nonce column
      await queryInterface.removeIndex(
        'allora_topic_worker_ema_scores',
        'idx_allora_topic_worker_ema_scores_address_nonce',
        { transaction },
      );

      await queryInterface.sequelize.query(
        `
        ALTER TABLE allora_topic_worker_ema_scores
        DROP COLUMN IF EXISTS nonce;
      `,
        { transaction },
      );

      // reputer ema scores - drop index and nonce column
      await queryInterface.removeIndex(
        'allora_topic_reputer_ema_scores',
        'idx_allora_topic_reputer_ema_scores_address_nonce',
        { transaction },
      );

      await queryInterface.sequelize.query(
        `
        ALTER TABLE allora_topic_reputer_ema_scores
        DROP COLUMN IF EXISTS nonce;
      `,
        { transaction },
      );
      await transaction.commit();
    } catch (e) {
      await transaction.rollback();
      throw e;
    }
  },
};
