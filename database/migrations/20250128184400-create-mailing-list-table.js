'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      await queryInterface.createTable('robonet_mailing_list', {
        name: {
          type: Sequelize.TEXT,
          allowNull: true,
        },
        email: {
          type: Sequelize.TEXT,
          allowNull: false,
          primaryKey: true,
        },
        created_at: {
          type: Sequelize.DATE,
          defaultValue: Sequelize.fn('now'),
        },
        updated_at: {
          type: Sequelize.DATE,
          defaultValue: Sequelize.fn('now'),
        },
      }, { transaction })

      await transaction.commit()
    } catch (e) {
      await transaction.rollback();
      throw e
    }
  },

  down: async (queryInterface, Sequelize) => {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      await queryInterface.dropTable('robonet_mailing_list');

      await transaction.commit()
    } catch (e) {
      await transaction.rollback();
      throw e;
    }
  },
};
