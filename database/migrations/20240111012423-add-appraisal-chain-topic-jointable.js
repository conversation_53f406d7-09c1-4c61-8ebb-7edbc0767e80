'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      await queryInterface.createTable(
        'asset_appraisal_onchain_locations',
        {
          chain_slug: {
            type: Sequelize.TEXT,
            allowNull: false,
          },
          collection_id: {
            type: Sequelize.TEXT,
            allowNull: false,
          },
          topic_id: {
            type: Sequelize.TEXT,
            allowNull: false,
          },
          actively_pushed: {
            type: Sequelize.BOOLEAN,
            allowNull: false,
            defaultValue: false,
          },
        },
        { transaction },
      );

      console.log('It is up to the dev who merges this code to ensure that the tables added above in this migration are properly populated with the most up-to-date data. This is not done automatically.')

      await transaction.commit();
    } catch (error) {
      await transaction.rollback();
      // Log all errors, but don't throw to prevent migration failure
      console.error(error);
    }
  },

  async down(queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      await queryInterface.dropTable('asset_appraisal_onchain_locations', { transaction });

      await transaction.commit();
    } catch (error) {
      await transaction.rollback();
      console.error(error);
    }
  },
};
