'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable(
      'ditto_strategists',
      {
        wallet_address: {
          type: Sequelize.TEXT,
          primaryKey: true,
        },
        pool_address: {
          type: Sequelize.TEXT,
          primaryKey: true,
        },
        is_private_pool: {
          type: Sequelize.BOOLEAN,
        },
        description: {
          type: Sequelize.TEXT,
        },
        fee_lp_wei: {
          type: Sequelize.TEXT,
        },
        fee_admin_wei: {
          type: Sequelize.TEXT,
        },
      },
      {
        uniqueKeys: {
          strategists_unique: {
            fields: ['wallet_address', 'pool_address'],
          },
        },
      },
    );

    await queryInterface.createTable('ditto_orders', {
      id: {
        type: Sequelize.TEXT,
        primaryKey: true,
      },
      offerer: {
        type: Sequelize.TEXT,
      },
      zone: {
        type: Sequelize.TEXT,
      },
      order_type: {
        type: Sequelize.INTEGER,
      },
      start_time: {
        type: Sequelize.INTEGER,
      },
      end_time: {
        type: Sequelize.INTEGER,
      },
      zone_hash: {
        type: Sequelize.TEXT,
      },
      salt: {
        type: Sequelize.TEXT,
      },
      conduit_key: {
        type: Sequelize.TEXT,
      },
      counter: {
        type: Sequelize.INTEGER,
      },
      signature: {
        type: Sequelize.TEXT,
      },
    });

    await queryInterface.createTable('ditto_order_items', {
      order_id: {
        type: Sequelize.TEXT,
        allowNull: false,
        references: {
          model: 'ditto_orders',
          key: 'id',
        },
        onDelete: 'CASCADE',
      },
      item_type: {
        type: Sequelize.TEXT,
        allowNull: false,
      },
      token: {
        // usually the collection id
        type: Sequelize.TEXT,
        allowNull: false,
      },
      identifier_or_criteria: {
        // usually the token id
        type: Sequelize.TEXT,
        allowNull: false,
      },
      start_amount: {
        type: Sequelize.TEXT,
        allowNull: false,
      },
      end_amount: {
        type: Sequelize.TEXT,
        allowNull: false,
      },
      type: {
        type: Sequelize.ENUM('consideration', 'offer'),
        allowNull: false,
      },
      recipient: {
        // Only set for considerations
        type: Sequelize.TEXT,
        allowNull: true,
      },
      created_at: {
        type: Sequelize.DATE,
        allowNull: false,
      },
      updated_at: {
        type: Sequelize.DATE,
        allowNull: false,
      },
    });

    await queryInterface.addIndex('ditto_orders', ['offerer']);
    await queryInterface.addIndex('ditto_order_items', ['token']);
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('strategists');
    await queryInterface.dropTable('orders');
    await queryInterface.dropTable('ditto_order_items');
  },
};
