'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.changeColumn(
      'ditto_points_transactions',
      'season_id',
      {
        type: Sequelize.INTEGER,
        allowNull: false,
      },
    );
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.changeColumn(
      'ditto_points_transactions',
      'season_id',
      {
        type: Sequelize.INTEGER,
        allowNull: true,
      },
    );
  },
};
