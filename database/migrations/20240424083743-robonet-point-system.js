'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      // seasons
      await queryInterface.createTable(
        'robonet_seasons',
        {
          id: {
            type: Sequelize.INTEGER,
            primaryKey: true,
            autoIncrement: true,
          },
          name: {
            type: Sequelize.STRING,
          },
          start_date: {
            type: Sequelize.DATE,
          },
          end_date: {
            type: Sequelize.DATE,
          },
          created_at: {
            type: Sequelize.DATE,
            defaultValue: Sequelize.fn('now'),
          },
          updated_at: {
            type: Sequelize.DATE,
            defaultValue: Sequelize.fn('now'),
          },
        },
        { transaction },
      );

      // season points
      await queryInterface.createTable(
        'robonet_season_points',
        {
          season_id: {
            type: Sequelize.INTEGER,
            primaryKey: true,
          },
          address: {
            type: Sequelize.STRING,
            primaryKey: true,
          },
          liquidity_points: {
            type: Sequelize.INTEGER,
            allowNull: false,
            defaultValue: 0,
          },
          referral_points: {
            type: Sequelize.INTEGER,
            allowNull: false,
            defaultValue: 0,
          },
          total_points: {
            type: Sequelize.INTEGER,
            allowNull: false,
            defaultValue: 0,
          },
          created_at: {
            type: Sequelize.DATE,
            allowNull: false,
            defaultValue: Sequelize.fn('now'),
          },
          updated_at: {
            type: Sequelize.DATE,
            allowNull: false,
            defaultValue: Sequelize.fn('now'),
          },
        },
        { transaction },
      );

      // leaderboard
      await queryInterface.createTable(
        'robonet_leaderboard',
        {
          season_id: {
            type: Sequelize.INTEGER,
            allowNull: false,
            primaryKey: true,
          },
          address: {
            type: Sequelize.TEXT,
            allowNull: false,
            primaryKey: true,
          },
          liquidity_points: {
            type: Sequelize.INTEGER,
            allowNull: false,
            defaultValue: 0,
          },
          referral_points: {
            type: Sequelize.INTEGER,
            allowNull: false,
            defaultValue: 0,
          },
          total_points: {
            type: Sequelize.INTEGER,
            allowNull: false,
            defaultValue: 0,
          },
          index: {
            type: Sequelize.INTEGER,
            allowNull: false,
          },
          created_at: {
            type: Sequelize.DATE,
            allowNull: false,
            defaultValue: Sequelize.fn('now'),
          },
          updated_at: {
            type: Sequelize.DATE,
            allowNull: false,
            defaultValue: Sequelize.fn('now'),
          },
        },
        { transaction },
      );

      // schedule
      await queryInterface.createTable(
        'robonet_points_schedule',
        {
          id: {
            type: Sequelize.INTEGER,
            primaryKey: true,
            autoIncrement: true,
          },
          execution_time: {
            type: Sequelize.DATE,
            allowNull: false,
          },
          period_start: {
            type: Sequelize.DATE,
            allowNull: false,
          },
          period_end: {
            type: Sequelize.DATE,
            allowNull: false,
          },
        },
        { transaction },
      );
      await transaction.commit();
    } catch (error) {
      console.error(error);
      await transaction.rollback();
      throw error;
    }
  },

  async down(queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();
    // seasons
    await queryInterface.dropTable('robonet_seasons', transaction);
    // season points
    await queryInterface.dropTable('robonet_season_points', transaction);
    // leaderboard
    await queryInterface.dropTable('robonet_leaderboard', transaction);
    // schedule
    await queryInterface.dropTable('robonet_points_schedule', transaction);
    await transaction.commit();
  },
};
