'use strict';
module.exports = {
  async up(queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      await queryInterface.addColumn(
        'vault_stats',
        'weekly_apr',
        {
          type: Sequelize.NUMERIC,
          allowNull: false,
          defaultValue: 0,
        },
        { transaction },
      );

      await queryInterface.addColumn(
        'vault_stats',
        'daily_change',
        {
          type: Sequelize.NUMERIC,
          allowNull: false,
          defaultValue: 0,
        },
        { transaction },
      );

      await queryInterface.addColumn(
        'vault_stats',
        'weekly_change',
        {
          type: Sequelize.NUMERIC,
          allowNull: false,
          defaultValue: 0,
        },
        { transaction },
      );

      await queryInterface.addColumn(
        'vault_stats',
        'monthly_change',
        {
          type: Sequelize.NUMERIC,
          allowNull: false,
          defaultValue: 0,
        },
        { transaction },
      );

      await queryInterface.addColumn(
        'vault_stats',
        'inception_change',
        {
          type: Sequelize.NUMERIC,
          allowNull: false,
          defaultValue: 0,
        },
        { transaction },
      );

      await transaction.commit();
    } catch (error) {
      console.error(error);
      await transaction.rollback();
      throw error;
    }
  },

  async down(queryInterface) {
    const transaction = await queryInterface.sequelize.transaction();
    await queryInterface.removeColumn('vault_stats', 'weekly_apr', {
      transaction,
    });
    await queryInterface.removeColumn('vault_stats', 'daily_change', {
      transaction,
    });
    await queryInterface.removeColumn('vault_stats', 'weekly_change', {
      transaction,
    });
    await queryInterface.removeColumn('vault_stats', 'monthly_change', {
      transaction,
    });
    await queryInterface.removeColumn('vault_stats', 'inception_change', {
      transaction,
    });
    await transaction.commit();
  },
};
