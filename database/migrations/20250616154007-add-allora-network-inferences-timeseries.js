'use strict';

const networkSuffixes = [
  'allora_devnet_1',
  'allora_testnet_1',
  'allora_mainnet_1',
];

module.exports = {
  async up(queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();

    try {
      //  NETWORK INFERENCES
      for (const networkSuffix of networkSuffixes) {
        const tableName = `allora_network_inferences_${networkSuffix}`;

        await queryInterface.createTable(
          tableName,
          {
            id: {
              type: Sequelize.BIGINT,
              primaryKey: true,
              autoIncrement: true,
            },
            topic_id: {
              type: Sequelize.INTEGER,
              allowNull: false,
            },
            combined_value: {
              type: Sequelize.TEXT,
              allowNull: false,
            },
            naive_value: {
              type: Sequelize.TEXT,
              allowNull: false,
            },
            timestamp: {
              type: Sequelize.INTEGER,
              allowNull: false,
            },
            created_at: {
              type: Sequelize.DATE,
              allowNull: false,
              defaultValue: Sequelize.fn('NOW'),
            },
          },
          { transaction },
        );

        await queryInterface.addIndex(tableName, ['topic_id', 'timestamp'], {
          name: `idx_network_inferences_${networkSuffix}`,
          transaction,
        });
      }
      await transaction.commit();
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  },

  async down(queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      for (const networkSuffix of networkSuffixes) {
        const tableName = `allora_network_inferences_${networkSuffix}`;
        await queryInterface.dropTable(tableName, { transaction });
        await queryInterface.removeIndex(
          tableName,
          `idx_network_inferences_${networkSuffix}`,
          { transaction },
        );
      }
      await transaction.commit();
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  },
};
