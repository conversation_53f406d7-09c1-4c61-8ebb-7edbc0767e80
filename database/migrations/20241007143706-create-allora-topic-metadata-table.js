'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      await queryInterface.createTable(
        'allora_topics_metadata',
        {
          id: {
            type: Sequelize.INTEGER,
            primaryKey: true,
          },
          creator: {
            type: Sequelize.STRING,
            allowNull: true,
          },
          name: {
            type: Sequelize.STRING,
            allowNull: true,
          },
          loss_method: {
            type: Sequelize.STRING,
            allowNull: true,
          },
          epoch_length: {
            type: Sequelize.STRING,
            allowNull: true,
          },
          ground_truth_lag: {
            type: Sequelize.STRING,
            allowNull: true,
          },
          p_norm: {
            type: Sequelize.STRING,
            allowNull: true,
          },
          alpha_regret: {
            type: Sequelize.STRING,
            allowNull: true,
          },
          allow_negative: {
            type: Sequelize.BOOLEAN,
            allowNull: true,
          },
          epsilon: {
            type: Sequelize.STRING,
            allowNull: true,
          },
          initial_regret: {
            type: Sequelize.TEXT,
            allowNull: true,
          },
          worker_submission_window: {
            type: Sequelize.STRING,
            allowNull: true,
          },
          merit_sortition_alpha: {
            type: Sequelize.STRING,
            allowNull: true,
          },
          active_inferer_quantile: {
            type: Sequelize.STRING,
            allowNull: true,
          },
          active_forecaster_quantile: {
            type: Sequelize.STRING,
            allowNull: true,
          },
          active_reputer_quantile: {
            type: Sequelize.STRING,
            allowNull: true,
          },
          created_at: {
            type: Sequelize.DATE,
            allowNull: false,
            defaultValue: Sequelize.fn('NOW'),
          },
        },
        { transaction },
      );
      await transaction.commit();
    } catch (e) {
      await transaction.rollback();
      throw e;
    }
  },

  async down(queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      await queryInterface.dropTable('allora_topics_metadata', { transaction });
      await transaction.commit();
    } catch (e) {
      await transaction.rollback();
      throw e;
    }
  },
};
