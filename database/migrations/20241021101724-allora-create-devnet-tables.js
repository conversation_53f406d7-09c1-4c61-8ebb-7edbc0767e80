'use strict';

/*
This migration is to create the tables for allora_devnet_1.
- allora_topics_metadata
- allora_topic_worker_stats
- allora_topic_worker_losses
- allora_topic_worker_ema_scores
- allora_topic_worker_count
- allora_topic_total_staked
- allora_topic_reputer_stats
- allora_topic_reputer_losses
- allora_topic_reputer_ema_scores
- allora_topic_emissions
- allora_wallet_balance
*/

module.exports = {
  up: async (queryInterface, Sequelize) => {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      // Create tables for devnet_1
      await queryInterface.sequelize.query(
        `
      CREATE TABLE allora_topics_metadata_allora_devnet_1 (
          id integer PRIMARY KEY,
          creator character varying(255),
          name character varying(255),
          loss_method character varying(255),
          epoch_length character varying(255),
          ground_truth_lag character varying(255),
          p_norm character varying(255),
          alpha_regret character varying(255),
          allow_negative boolean,
          epsilon character varying(255),
          initial_regret text,
          worker_submission_window character varying(255),
          merit_sortition_alpha character varying(255),
          active_inferer_quantile character varying(255),
          active_forecaster_quantile character varying(255),
          active_reputer_quantile character varying(255),
          created_at timestamp with time zone NOT NULL DEFAULT now()
      );
      `,
        { transaction },
      );

      await queryInterface.sequelize.query(
        `
      CREATE TABLE allora_topic_worker_stats_allora_devnet_1 (
          address character varying(255),
          topic_id integer,
          weight numeric DEFAULT 0,
          loss numeric DEFAULT 0,
          total_earned numeric DEFAULT 0,
          created_at timestamp with time zone NOT NULL DEFAULT now(),
          updated_at timestamp with time zone NOT NULL DEFAULT now(),
          CONSTRAINT allora_topic_worker_stats_allora_devnet_1_pkey PRIMARY KEY (address, topic_id)
      );
        `,
        { transaction },
      );

      await queryInterface.sequelize.query(
        `
      CREATE TABLE allora_topic_worker_losses_allora_devnet_1 (
          id bigint DEFAULT nextval('allora_topic_worker_losses_id_seq'::regclass) PRIMARY KEY,
          topic_id integer NOT NULL,
          address character varying(255) NOT NULL,
          timestamp integer NOT NULL,
          value text NOT NULL,
          "createdAt" timestamp with time zone NOT NULL DEFAULT now()
      );
      -- Indices -------------------------------------------------------
      CREATE INDEX idx_topic_worker_losses_allora_devnet_1 ON allora_topic_worker_losses_allora_devnet_1(topic_id int4_ops,address text_ops,timestamp int4_ops);
      `,
        { transaction },
      );

      await queryInterface.sequelize.query(
        `
      CREATE TABLE allora_topic_worker_ema_scores_allora_devnet_1 (
          address character varying(255),
          topic_id integer,
          score numeric,
          is_active boolean,
          "createdAt" timestamp with time zone NOT NULL DEFAULT now(),
          "updatedAt" timestamp with time zone NOT NULL DEFAULT now(),
          nonce integer,
          CONSTRAINT allora_topic_worker_ema_scores_allora_devnet_1_pkey PRIMARY KEY (address, topic_id)
      );
      -- Indices -------------------------------------------------------
      CREATE INDEX idx_allora_topic_worker_ema_scores_allora_devnet_1 ON allora_topic_worker_ema_scores_allora_devnet_1(topic_id int4_ops,is_active bool_ops);
      CREATE INDEX idx_allora_topic_worker_ema_scores_allora_devnet_1_address_nonce ON allora_topic_worker_ema_scores_allora_devnet_1(address text_ops,nonce int4_ops);
      `,
        { transaction },
      );

      await queryInterface.sequelize.query(
        `
      CREATE TABLE allora_topic_worker_count_allora_devnet_1 (
          id bigint DEFAULT nextval('allora_topic_worker_count_id_seq'::regclass) PRIMARY KEY,
          topic_id integer NOT NULL,
          timestamp integer NOT NULL,
          value integer NOT NULL,
          "createdAt" timestamp with time zone NOT NULL DEFAULT now()
      );
      -- Indices -------------------------------------------------------
      CREATE INDEX idx_allora_topic_worker_count_allora_devnet_1 ON allora_topic_worker_count_allora_devnet_1(topic_id int4_ops,timestamp int4_ops);
      `,
        { transaction },
      );

      await queryInterface.sequelize.query(
        `
        CREATE TABLE allora_topic_total_staked_allora_devnet_1 (
          id bigint DEFAULT nextval('allora_topic_total_staked_id_seq'::regclass) PRIMARY KEY,
          topic_id integer NOT NULL,
          timestamp integer NOT NULL,
          value text NOT NULL,
          "createdAt" timestamp with time zone NOT NULL DEFAULT now()
      );
      -- Indices -------------------------------------------------------
      CREATE INDEX idx_allora_topic_total_staked_allora_devnet_1 ON allora_topic_total_staked_allora_devnet_1(topic_id int4_ops,timestamp int4_ops);  
      `,
        { transaction },
      );

      await queryInterface.sequelize.query(
        `
      CREATE TABLE allora_topic_reputer_stats_allora_devnet_1 (
          address character varying(255),
          topic_id integer,
          weight numeric DEFAULT 0,
          loss numeric DEFAULT 0,
          total_earned numeric DEFAULT 0,
          created_at timestamp with time zone NOT NULL DEFAULT now(),
          updated_at timestamp with time zone NOT NULL DEFAULT now(),
          CONSTRAINT allora_topic_reputer_stats_allora_devnet_1_pkey PRIMARY KEY (address, topic_id)
      );
      `,
        { transaction },
      );

      await queryInterface.sequelize.query(
        `
        CREATE TABLE allora_topic_reputer_losses_allora_devnet_1 (
          id bigint DEFAULT nextval('allora_topic_reputer_losses_id_seq'::regclass) PRIMARY KEY,
          topic_id integer NOT NULL,
          address character varying(255) NOT NULL,
          timestamp integer NOT NULL,
          value text NOT NULL,
          "createdAt" timestamp with time zone NOT NULL DEFAULT now()
      );
      -- Indices -------------------------------------------------------
      CREATE INDEX idx_topic_reputer_losses_allora_devnet_1 ON allora_topic_reputer_losses_allora_devnet_1(topic_id int4_ops,address text_ops,timestamp int4_ops);
      `,
        { transaction },
      );

      await queryInterface.sequelize.query(
        `
      CREATE TABLE allora_topic_reputer_ema_scores_allora_devnet_1 (
          address character varying(255),
          topic_id integer,
          score numeric,
          is_active boolean,
          "createdAt" timestamp with time zone NOT NULL DEFAULT now(),
          "updatedAt" timestamp with time zone NOT NULL DEFAULT now(),
          nonce integer,
          CONSTRAINT allora_topic_reputer_ema_scores_allora_devnet_1_pkey PRIMARY KEY (address, topic_id)
      );
      -- Indices -------------------------------------------------------
      CREATE INDEX idx_allora_topic_reputer_ema_scores_allora_devnet_1 ON allora_topic_reputer_ema_scores_allora_devnet_1(topic_id int4_ops,is_active bool_ops);
      CREATE INDEX idx_allora_topic_reputer_ema_scores_address_allora_devnet_1_nonce ON allora_topic_reputer_ema_scores_allora_devnet_1(address text_ops,nonce int4_ops);
      `,
        { transaction },
      );

      await queryInterface.sequelize.query(
        `
      CREATE TABLE allora_topic_emissions_allora_devnet_1 (
          id bigint DEFAULT nextval('allora_topic_emissions_id_seq'::regclass) PRIMARY KEY,
          topic_id integer NOT NULL,
          timestamp integer NOT NULL,
          value text NOT NULL,
          "createdAt" timestamp with time zone NOT NULL DEFAULT now()
      );
      -- Indices -------------------------------------------------------
      CREATE INDEX idx_allora_topic_emissions_allora_devnet_1 ON allora_topic_emissions_allora_devnet_1(topic_id int4_ops,timestamp int4_ops);
      CREATE UNIQUE INDEX uniq_allora_topic_emissions_allora_devnet_1 ON allora_topic_emissions_allora_devnet_1(topic_id int4_ops,timestamp int4_ops);
      `,
        { transaction },
      );

      await queryInterface.sequelize.query(`
      CREATE TABLE allora_wallet_balance_allora_devnet_1 (
          id BIGSERIAL PRIMARY KEY,
          address text NOT NULL,
          value text NOT NULL,
          timestamp integer NOT NULL,
          "createdAt" timestamp with time zone NOT NULL DEFAULT now()
      );
      -- Indices -------------------------------------------------------
      CREATE INDEX idx_allora_wallet_balance_allora_devnet_1 ON allora_wallet_balance_allora_devnet_1(address text_ops,timestamp int4_ops);  
      `);

      await transaction.commit();
    } catch (e) {
      await transaction.rollback();
      throw e;
    }
  },

  down: async (queryInterface, Sequelize) => {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      // Drop tables and indexes for devnet_1
      await queryInterface.sequelize.query(
        `
        -- Drop allora_topics_metadata_allora_devnet_1
        DROP TABLE IF EXISTS allora_topics_metadata_allora_devnet_1;

        -- Drop allora_topic_worker_stats_allora_devnet_1
        DROP TABLE IF EXISTS allora_topic_worker_stats_allora_devnet_1;

        -- Drop allora_topic_worker_losses_allora_devnet_1
        DROP INDEX IF EXISTS idx_topic_worker_losses_allora_devnet_1;
        DROP TABLE IF EXISTS allora_topic_worker_losses_allora_devnet_1;

        -- Drop allora_topic_worker_ema_scores_allora_devnet_1
        DROP INDEX IF EXISTS idx_allora_topic_worker_ema_scores_allora_devnet_1;
        DROP INDEX IF EXISTS idx_allora_topic_worker_ema_scores_allora_devnet_1_address_nonce;
        DROP TABLE IF EXISTS allora_topic_worker_ema_scores_allora_devnet_1;

        -- Drop allora_topic_worker_count_allora_devnet_1
        DROP INDEX IF EXISTS idx_allora_topic_worker_count_allora_devnet_1;
        DROP TABLE IF EXISTS allora_topic_worker_count_allora_devnet_1;

        -- Drop allora_topic_total_staked_allora_devnet_1
        DROP INDEX IF EXISTS idx_allora_topic_total_staked_allora_devnet_1;
        DROP TABLE IF EXISTS allora_topic_total_staked_allora_devnet_1;

        -- Drop allora_topic_reputer_stats_allora_devnet_1
        DROP TABLE IF EXISTS allora_topic_reputer_stats_allora_devnet_1;

        -- Drop allora_topic_reputer_losses_allora_devnet_1
        DROP INDEX IF EXISTS idx_topic_reputer_losses;
        DROP TABLE IF EXISTS allora_topic_reputer_losses_allora_devnet_1;

        -- Drop allora_topic_reputer_ema_scores_allora_devnet_1
        DROP INDEX IF EXISTS idx_allora_topic_reputer_ema_scores_allora_devnet_1;
        DROP INDEX IF EXISTS idx_allora_topic_reputer_ema_scores_address_allora_devnet_1_nonce;
        DROP TABLE IF EXISTS allora_topic_reputer_ema_scores_allora_devnet_1;

        -- Drop allora_topic_emissions_allora_devnet_1
        DROP INDEX IF EXISTS idx_allora_topic_emissions_allora_devnet_1;
        DROP INDEX IF EXISTS uniq_allora_topic_emissions_allora_devnet_1;
        DROP TABLE IF EXISTS allora_topic_emissions_allora_devnet_1;

        -- Drop allora_wallet_balance_allora_devnet_1
        DROP INDEX IF EXISTS idx_allora_wallet_balance_allora_devnet_1;
        DROP TABLE IF EXISTS allora_wallet_balance_allora_devnet_1;
      `,
        { transaction },
      );

      await transaction.commit();
    } catch (e) {
      await transaction.rollback();
      throw e;
    }
  },
};
