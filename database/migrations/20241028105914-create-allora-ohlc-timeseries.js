'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      await queryInterface.createTable(
        'allora_ohlc_timeseries',
        {
          id: {
            type: Sequelize.BIGINT,
            primaryKey: true,
            autoIncrement: true,
          },
          ticker: {
            type: Sequelize.STRING(50),
            allowNull: false,
          },
          exchange_code: {
            type: Sequelize.STRING(50),
            allowNull: true,
          },
          date: {
            type: Sequelize.DATE,
            allowNull: false,
          },
          open: {
            type: Sequelize.DECIMAL,
            allowNull: false,
          },
          high: {
            type: Sequelize.DECIMAL,
            allowNull: false,
          },
          low: {
            type: Sequelize.DECIMAL,
            allowNull: false,
          },
          close: {
            type: Sequelize.DECIMAL,
            allowNull: false,
          },
          trades_done: {
            type: Sequelize.INTEGER,
            allowNull: true,
            defaultValue: null,
          },
          volume: {
            type: Sequelize.DECIMAL,
            allowNull: true,
            defaultValue: null,
          },
          volume_notional: {
            type: Sequelize.DECIMAL,
            allowNull: true,
            defaultValue: null,
          },
          created_at: {
            type: Sequelize.DATE,
            allowNull: false,
            defaultValue: Sequelize.fn('NOW'),
          },
        },
        { transaction },
      );

      // Create composite index with exchangeCode ASC and date DESC using a raw SQL query
      await queryInterface.sequelize.query(
        `CREATE INDEX idx_allora_ohlc_ticker_date_desc ON "allora_ohlc_timeseries" ("ticker" ASC, "date" DESC);`,
        { transaction },
      );

      await queryInterface.sequelize.query(
        `CREATE INDEX idx_allora_ohlc_date_desc ON "allora_ohlc_timeseries" ("date" DESC);`,
        { transaction },
      );

      // Add unique constraint on ticker, exchange_code, and date
      await queryInterface.addConstraint('allora_ohlc_timeseries', {
        fields: ['ticker', 'exchange_code', 'date'],
        type: 'unique',
        name: 'unique_ticker_exchange_date',
        transaction,
      });

      await transaction.commit();
    } catch (e) {
      await transaction.rollback();
      throw e;
    }
  },

  async down(queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      // Remove unique constraint
      await queryInterface.removeConstraint(
        'allora_ohlc_timeseries',
        'unique_ticker_exchange_date',
        { transaction },
      );

      // Remove indexes
      await queryInterface.sequelize.query(
        `DROP INDEX IF EXISTS idx_allora_ohlc_ticker_date_desc;`,
        { transaction },
      );

      await queryInterface.sequelize.query(
        `DROP INDEX IF EXISTS idx_allora_ohlc_date_desc;`,
        { transaction },
      );

      // Drop the table
      await queryInterface.dropTable('allora_ohlc_timeseries', { transaction });

      await transaction.commit();
    } catch (e) {
      await transaction.rollback();
      throw e;
    }
  },
};
