'use strict';

module.exports = {
  async up (queryInterface, Sequelize) {
    await queryInterface.addColumn('ditto_orders', 'extra_data', {
      type: Sequelize.TEXT,
    });
    await queryInterface.addColumn('ditto_orders', 'total_original_consideration_items', {
      type: Sequelize.INTEGER,
    });
    await queryInterface.addColumn('ditto_orders', 'created_at', {
      type: Sequelize.DATE,
      defaultValue: Sequelize.fn('now'),
    });
    await queryInterface.addColumn('ditto_orders', 'updated_at', {
      type: Sequelize.DATE,
      defaultValue: Sequelize.fn('now'),
    });
  },

  async down (queryInterface, Sequelize) {
    await queryInterface.removeColumn('ditto_orders', 'extra_data');
    await queryInterface.removeColumn('ditto_orders', 'total_original_consideration_items');
    await queryInterface.removeColumn('ditto_orders', 'created_at');
    await queryInterface.removeColumn('updated_at', 'updated_at');
  }
};
