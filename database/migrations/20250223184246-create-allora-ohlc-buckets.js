'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      await queryInterface.createTable(
        'allora_ohlc_buckets',
        {
          id: {
            type: Sequelize.BIGINT,
            primaryKey: true,
            autoIncrement: true,
          },
          ticker: {
            type: Sequelize.STRING(15),
            allowNull: false,
          },
          periodicity: {
            type: Sequelize.STRING(10),
            allowNull: false,
            defaultValue: 'month',
          },
          state: {
            type: Sequelize.STRING(15),
            allowNull: false,
            defaultValue: 'pending',
          },
          start: {
            type: Sequelize.DATEONLY,
            allowNull: false,
          },
          end: {
            type: Sequelize.DATEONLY,
            allowNull: false,
          },
          s3_object_key: {
            type: Sequelize.STRING(50),
            allowNull: true,
          },
          created_at: {
            type: Sequelize.DATE,
            allowNull: false,
            defaultValue: Sequelize.fn('NOW'),
          },
          updated_at: {
            type: Sequelize.DATE,
            allowNull: false,
            defaultValue: Sequelize.fn('NOW'),
          },
        },
        { transaction },
      );

      await queryInterface.addConstraint('allora_ohlc_buckets', {
        fields: ['ticker', 'periodicity', 'start'],
        type: 'unique',
        name: 'unique_ticker_period',
        transaction,
      });

      await transaction.commit();
    } catch (e) {
      await transaction.rollback();
      throw e;
    }
  },

  async down(queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      await queryInterface.removeConstraint(
        'allora_ohlc_buckets',
        'unique_ticker_period',
        { transaction },
      );

      await queryInterface.dropTable('allora_ohlc_buckets', { transaction });

      await transaction.commit();
    } catch (e) {
      await transaction.rollback();
      throw e;
    }
  },
};
