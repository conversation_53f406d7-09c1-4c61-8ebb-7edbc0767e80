'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      await queryInterface.sequelize.query(
        `
        ALTER TABLE allora_topic_worker_stats
        ALTER COLUMN total_earned TYPE NUMERIC USING total_earned::NUMERIC,
        
        ALTER COLUMN weight SET DEFAULT 0,
        ALTER COLUMN loss SET DEFAULT 0,
        ALTER COLUMN total_earned SET DEFAULT 0;
      `,
        { transaction },
      );

      await queryInterface.sequelize.query(
        `
        ALTER TABLE allora_topic_reputer_stats        
        ALTER COLUMN weight SET DEFAULT 0,
        ALTER COLUMN loss SET DEFAULT 0,
        ALTER COLUMN total_earned SET DEFAULT 0;
      `,
        { transaction },
      );

      await transaction.commit();
    } catch (e) {
      await transaction.rollback();
      throw e;
    }
  },

  down: async (queryInterface, Sequelize) => {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      // Revert the column type back to TEXT
      await queryInterface.sequelize.query(
        `
        ALTER TABLE allora_topic_worker_stats
        ALTER COLUMN total_earned TYPE TEXT USING total_earned::TEXT,

        ALTER COLUMN weight SET DEFAULT NULL,
        ALTER COLUMN loss SET DEFAULT NULL,
        ALTER COLUMN total_earned SET DEFAULT NULL;
      `,
        { transaction },
      );

      await queryInterface.sequelize.query(
        `
        ALTER TABLE allora_topic_reputer_stats        
        ALTER COLUMN weight SET DEFAULT NULL,
        ALTER COLUMN loss SET DEFAULT NULL,
        ALTER COLUMN total_earned SET DEFAULT NULL;
      `,
        { transaction },
      );

      await transaction.commit();
    } catch (e) {
      await transaction.rollback();
      throw e;
    }
  },
};
