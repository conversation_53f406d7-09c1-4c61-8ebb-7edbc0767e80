'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      // Create table of timeseries of inferences for allora models per use case
      await queryInterface.createTable(
        'allora_token_price_topic_tokens',
        {
          token: {
            type: Sequelize.TEXT,
            allowNull: false,
          },
          price_topic_id: {
            type: Sequelize.TEXT,
            allowNull: false,
          },
          chain_id: {
            type: Sequelize.TEXT,
            allowNull: false,
          },
          created_at: {
            type: Sequelize.DATE,
            allowNull: false,
            defaultValue: Sequelize.fn('NOW'),
          },
        },
        {
          transaction,
        },
      );

      // Insert all tokens that have a price topic id
      await queryInterface.bulkInsert('allora_token_price_topic_tokens', [
        // ETH
        { token: 'ETH', chain_id: 'allora-testnet-1', price_topic_id: '1', created_at: new Date() },
        { token: 'ETH', chain_id: 'allora-testnet-1', price_topic_id: '2', created_at: new Date() },
        { token: 'ETH', chain_id: 'allora-testnet-1', price_topic_id: '12', created_at: new Date() },
        { token: 'ETH', chain_id: 'allora-testnet-1', price_topic_id: '17', created_at: new Date() },
        { token: 'ETH', chain_id: 'allora-testnet-1', price_topic_id: '19', created_at: new Date() },
        { token: 'ETH', chain_id: 'allora-testnet-1', price_topic_id: '21', created_at: new Date() },
        { token: 'ETH', chain_id: 'allora-testnet-1', price_topic_id: '7', created_at: new Date() },
        { token: 'ETH', chain_id: 'allora-testnet-1', price_topic_id: '15', created_at: new Date() },
        { token: 'ETH', chain_id: 'allora-testnet-1', price_topic_id: '13', created_at: new Date() },
        { token: 'ETH', chain_id: 'allora-testnet-1', price_topic_id: '28', created_at: new Date() },
        { token: 'ETH', chain_id: 'allora-testnet-1', price_topic_id: '30', created_at: new Date() },
        { token: 'ETH', chain_id: 'allora-testnet-1', price_topic_id: '29', created_at: new Date() },
        // BTC
        { token: 'BTC', chain_id: 'allora-testnet-1', price_topic_id: '4', created_at: new Date() },
        { token: 'BTC', chain_id: 'allora-testnet-1', price_topic_id: '18', created_at: new Date() },
        { token: 'BTC', chain_id: 'allora-testnet-1', price_topic_id: '20', created_at: new Date() },
        { token: 'BTC', chain_id: 'allora-testnet-1', price_topic_id: '3', created_at: new Date() },
        { token: 'BTC', chain_id: 'allora-testnet-1', price_topic_id: '14', created_at: new Date() },
        { token: 'BTC', chain_id: 'allora-testnet-1', price_topic_id: '16', created_at: new Date() },
      ], { transaction });

      await transaction.commit();
    } catch (e) {
      await transaction.rollback();
      throw e;
    }
  },

  async down(queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      // Drop tables in reverse order of creation
      await queryInterface.dropTable('allora_token_price_topic_tokens', {
        transaction,
      });

      await transaction.commit();
    } catch (e) {
      await transaction.rollback();
      throw e;
    }
  },
};
