'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      // Modify WORKER STATS columns
      await queryInterface.changeColumn(
        'allora_topic_worker_stats',
        'weight',
        {
          type: Sequelize.NUMERIC,
          allowNull: true, // Allow NULL
        },
        { transaction },
      );
      await queryInterface.changeColumn(
        'allora_topic_worker_stats',
        'loss',
        {
          type: Sequelize.NUMERIC,
          allowNull: true, // Allow NULL
        },
        { transaction },
      );
      await queryInterface.changeColumn(
        'allora_topic_worker_stats',
        'total_earned',
        {
          type: Sequelize.TEXT,
          allowNull: true, // Allow NULL
        },
        { transaction },
      );

      // Rename createdAt and updatedAt to created_at and updated_at
      await queryInterface.renameColumn(
        'allora_topic_worker_stats',
        'createdAt',
        'created_at',
        { transaction },
      );
      await queryInterface.renameColumn(
        'allora_topic_worker_stats',
        'updatedAt',
        'updated_at',
        { transaction },
      );

      // Modify REPUTER STATS columns
      await queryInterface.changeColumn(
        'allora_topic_reputer_stats',
        'weight',
        {
          type: Sequelize.NUMERIC,
          allowNull: true, // Allow NULL
        },
        { transaction },
      );
      await queryInterface.changeColumn(
        'allora_topic_reputer_stats',
        'loss',
        {
          type: Sequelize.NUMERIC,
          allowNull: true, // Allow NULL
        },
        { transaction },
      );
      await queryInterface.changeColumn(
        'allora_topic_reputer_stats',
        'total_earned',
        {
          type: Sequelize.NUMERIC, // Change TEXT to NUMERIC to match the original table definition
          allowNull: true, // Allow NULL
        },
        { transaction },
      );

      // Rename createdAt and updatedAt to created_at and updated_at
      await queryInterface.renameColumn(
        'allora_topic_reputer_stats',
        'createdAt',
        'created_at',
        { transaction },
      );
      await queryInterface.renameColumn(
        'allora_topic_reputer_stats',
        'updatedAt',
        'updated_at',
        { transaction },
      );

      await transaction.commit();
    } catch (e) {
      await transaction.rollback();
      throw e;
    }
  },

  down: async (queryInterface, Sequelize) => {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      // Revert WORKER STATS columns back to NOT NULL
      await queryInterface.changeColumn(
        'allora_topic_worker_stats',
        'weight',
        {
          type: Sequelize.NUMERIC,
          allowNull: false,
        },
        { transaction },
      );
      await queryInterface.changeColumn(
        'allora_topic_worker_stats',
        'loss',
        {
          type: Sequelize.NUMERIC,
          allowNull: false,
        },
        { transaction },
      );
      await queryInterface.changeColumn(
        'allora_topic_worker_stats',
        'total_earned',
        {
          type: Sequelize.TEXT,
          allowNull: false,
        },
        { transaction },
      );

      // Rename created_at and updated_at back to createdAt and updatedAt
      await queryInterface.renameColumn(
        'allora_topic_worker_stats',
        'created_at',
        'createdAt',
        { transaction },
      );
      await queryInterface.renameColumn(
        'allora_topic_worker_stats',
        'updated_at',
        'updatedAt',
        { transaction },
      );

      // Revert REPUTER STATS columns back to NOT NULL
      await queryInterface.changeColumn(
        'allora_topic_reputer_stats',
        'weight',
        {
          type: Sequelize.NUMERIC,
          allowNull: false,
        },
        { transaction },
      );
      await queryInterface.changeColumn(
        'allora_topic_reputer_stats',
        'loss',
        {
          type: Sequelize.NUMERIC,
          allowNull: false,
        },
        { transaction },
      );
      await queryInterface.changeColumn(
        'allora_topic_reputer_stats',
        'total_earned',
        {
          type: Sequelize.NUMERIC,
          allowNull: false,
        },
        { transaction },
      );

      // Rename created_at and updated_at back to createdAt and updatedAt
      await queryInterface.renameColumn(
        'allora_topic_reputer_stats',
        'created_at',
        'createdAt',
        { transaction },
      );
      await queryInterface.renameColumn(
        'allora_topic_reputer_stats',
        'updated_at',
        'updatedAt',
        { transaction },
      );

      await transaction.commit();
    } catch (e) {
      await transaction.rollback();
      throw e;
    }
  },
};
