'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      await queryInterface.createTable(
        'pancakeswap_points_transactions',
        {
          tx_hash: {
            type: Sequelize.STRING,
            allowNull: false,
            primaryKey: true,
          },
          log_index: {
            type: Sequelize.STRING,
            allowNull: false,
            primaryKey: true,
          },
          epoch: {
            type: Sequelize.INTEGER,
            allowNull: false,
          },
          user_address: {
            type: Sequelize.STRING,
          },
          event_type: {
            type: Sequelize.STRING,
            allowNull: false,
          },
          event_payload: {
            type: Sequelize.JSONB,
            allowNull: false,
          },
          block_timestamp: {
            type: Sequelize.STRING,
            allowNull: false,
          },
        },
        { transaction },
      );
      await transaction.commit();
    } catch (error) {
      console.error(error);
      await transaction.rollback();
      throw error;
    }
  },

  async down(queryInterface) {
    const transaction = await queryInterface.sequelize.transaction();
    await queryInterface.dropTable(
      'pancakeswap_points_transactions',
      transaction,
    );
    await transaction.commit();
  },
};
