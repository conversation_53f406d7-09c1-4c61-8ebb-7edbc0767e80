'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.addColumn('pools', 'pool_manager_contract_owner', {
      type: Sequelize.STRING,
      allowNull: true,
    });
    await queryInterface.addColumn('pools', 'pool_manager_template_index', {
      type: Sequelize.INTEGER,
      allowNull: true,
    });
    await queryInterface.addColumn('pools', 'pool_manager_updater', {
      type: Sequelize.STRING,
      allowNull: true,
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.removeColumn('pools', 'pool_manager_contract_owner');
    await queryInterface.removeColumn('pools', 'pool_manager_template_index');
    await queryInterface.removeColumn('pools', 'pool_manager_updater');
  },
};
