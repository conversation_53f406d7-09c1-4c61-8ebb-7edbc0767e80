'use strict';
module.exports = {
  async up(queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      // add topic_id and block_number to allora_point_transactions
      await queryInterface.addColumn(
        'allora_point_transactions',
        'topic_id',
        {
          type: Sequelize.INTEGER,
          allowNull: true,
          defaultValue: null,
        },
        { transaction },
      );

      await queryInterface.addColumn(
        'allora_point_transactions',
        'block_number',
        {
          type: Sequelize.INTEGER,
          allowNull: true,
          defaultValue: null,
        },
        { transaction },
      );
      await transaction.commit();
    } catch (error) {
      console.error(error);
      await transaction.rollback();
      throw error;
    }
  },

  async down(queryInterface) {
    const transaction = await queryInterface.sequelize.transaction();
    await queryInterface.removeColumn('allora_point_transactions', 'topic_id', {
      transaction,
    });
    await queryInterface.removeColumn(
      'allora_point_transactions',
      'block_number',
      { transaction },
    );
    await transaction.commit();
  },
};
