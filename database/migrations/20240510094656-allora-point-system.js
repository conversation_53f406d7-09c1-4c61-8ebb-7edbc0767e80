'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      // allora user
      await queryInterface.createTable(
        'allora_users',
        {
          id: {
            type: Sequelize.UUID,
            primaryKey: true,
            defaultValue: Sequelize.UUIDV4,
          },
          privy_id: {
            type: Sequelize.STRING,
            allowNull: true,
            defaultValue: null,
            unique: true,
          },
          evm_address: {
            type: Sequelize.STRING,
            allowNull: true,
            defaultValue: null,
            unique: true,
          },
          cosmos_address: {
            type: Sequelize.STRING,
            allowNull: true,
            defaultValue: null,
            unique: true,
          },
          created_at: {
            type: Sequelize.DATE,
            defaultValue: Sequelize.fn('now'),
          },
        },
        { transaction },
      );
      // seasons
      await queryInterface.createTable(
        'allora_campaigns',
        {
          slug: {
            type: Sequelize.STRING,
            primaryKey: true,
          },
          name: {
            type: Sequelize.STRING,
            allowNull: false,
          },
          description: {
            type: Sequelize.STRING,
            allowNull: true,
          },
          points: {
            type: Sequelize.DOUBLE,
            allowNull: true,
          },
          multiplier: {
            type: Sequelize.INTEGER,
            allowNull: false,
            defaultValue: 1,
          },
          topic_ids: {
            type: Sequelize.JSONB,
            allowNull: true,
            defaultValue: [],
          },
          start_date: {
            type: Sequelize.DATE,
            allowNull: false,
          },
          end_date: {
            type: Sequelize.DATE,
            allowNull: false,
          },
          type: {
            type: Sequelize.STRING,
            allowNull: true,
          },
          created_at: {
            type: Sequelize.DATE,
            defaultValue: Sequelize.fn('now'),
          },
          updated_at: {
            type: Sequelize.DATE,
            defaultValue: Sequelize.fn('now'),
          },
        },
        { transaction },
      );

      await queryInterface.createTable(
        'allora_point_transactions',
        {
          id: {
            type: Sequelize.INTEGER,
            primaryKey: true,
            autoIncrement: true,
          },
          user_id: {
            type: Sequelize.STRING,
            allowNull: false,
          },
          points: {
            type: Sequelize.DOUBLE,
            allowNull: false,
            defaultValue: 0,
          },
          campaign_slug: {
            type: Sequelize.STRING,
            allowNull: false,
          },
          created_at: {
            type: Sequelize.DATE,
            defaultValue: Sequelize.fn('now'),
          },
        },
        { transaction },
      );

      // leaderboard
      await queryInterface.createTable(
        'allora_leaderboard',
        {
          user_id: {
            type: Sequelize.UUID,
            allowNull: false,
            primaryKey: true,
          },
          total_points: {
            type: Sequelize.DOUBLE,
            allowNull: false,
            defaultValue: 0,
          },
          created_at: {
            type: Sequelize.DATE,
            allowNull: false,
            defaultValue: Sequelize.fn('now'),
          },
          updated_at: {
            type: Sequelize.DATE,
            allowNull: false,
            defaultValue: Sequelize.fn('now'),
          },
        },
        { transaction },
      );

      // schedule
      await queryInterface.createTable(
        'allora_points_schedule',
        {
          id: {
            type: Sequelize.INTEGER,
            primaryKey: true,
            autoIncrement: true,
          },
          execution_time: {
            type: Sequelize.DATE,
            allowNull: false,
          },
          period_start: {
            type: Sequelize.DATE,
            allowNull: false,
          },
          period_end: {
            type: Sequelize.DATE,
            allowNull: false,
          },
        },
        { transaction },
      );
      await transaction.commit();
    } catch (error) {
      console.error(error);
      await transaction.rollback();
      throw error;
    }
  },

  async down(queryInterface) {
    const transaction = await queryInterface.sequelize.transaction();
    await queryInterface.dropTable('allora_users', transaction);
    await queryInterface.dropTable('allora_campaigns', transaction);
    await queryInterface.dropTable('allora_point_transactions', transaction);
    await queryInterface.dropTable('allora_leaderboard', transaction);
    await queryInterface.dropTable('allora_points_schedule', transaction);
    await transaction.commit();
  },
};
