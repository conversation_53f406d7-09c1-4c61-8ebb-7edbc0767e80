'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();
    try {

      // add is_endorsed column
      await queryInterface.addColumn(
        'allora_topics_metadata_allora_testnet_1',
        'is_endorsed',
        {
          type: Sequelize.BOOLEAN,
          allowNull: false,
          defaultValue: false,
        },
        { transaction },
      );

      await queryInterface.addColumn(
        'allora_topics_metadata_allora_devnet_1',
        'is_endorsed',
        {
          type: Sequelize.BOOLEAN,
          allowNull: false,
          defaultValue: false,
        },
        { transaction },
      );

      await queryInterface.addColumn(
        'allora_topics_metadata_allora_mainnet_1',
        'is_endorsed',
        {
          type: Sequelize.BOOLEAN,
          allowNull: false,
          defaultValue: false,
        },
        { transaction },
      );

      // add is_blacklisted column
      await queryInterface.addColumn(
        'allora_topics_metadata_allora_testnet_1',
        'is_blacklisted',
        {
          type: Sequelize.BOOLEAN,
          allowNull: false,
          defaultValue: false,
        },
        { transaction },
      );

      await queryInterface.addColumn(
        'allora_topics_metadata_allora_devnet_1',
        'is_blacklisted',
        {
          type: Sequelize.BOOLEAN,
          allowNull: false,
          defaultValue: false,
        },
        { transaction },
      );

      await queryInterface.addColumn(
        'allora_topics_metadata_allora_mainnet_1',
        'is_blacklisted',
        {
          type: Sequelize.BOOLEAN,
          allowNull: false,
          defaultValue: false,
        },
        { transaction },
      );

      await transaction.commit();
    } catch (error) {
      console.error(error);
      await transaction.rollback();
      throw error;
    }
  },

  async down(queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      // remove is_endorsed column
      await queryInterface.removeColumn(
        'allora_topics_metadata_allora_testnet_1',
        'is_endorsed',
        {
          transaction,
        },
      );

      await queryInterface.removeColumn(
        'allora_topics_metadata_allora_devnet_1',
        'is_endorsed',
        {
          transaction,
        },
      );

      await queryInterface.removeColumn(
        'allora_topics_metadata_allora_mainnet_1',
        'is_endorsed',
        {
          transaction,
        },
      );

      // remove is_blacklisted column
      await queryInterface.removeColumn(
        'allora_topics_metadata_allora_testnet_1',
        'is_blacklisted',
        {
          transaction,
        },
      );

      await queryInterface.removeColumn(
        'allora_topics_metadata_allora_devnet_1',
        'is_blacklisted',
        {
          transaction,
        },
      );

      await queryInterface.removeColumn(
        'allora_topics_metadata_allora_mainnet_1',
        'is_blacklisted',
        {
          transaction,
        },
      );

      await transaction.commit();
    } catch (error) {
      console.error(error);
      await transaction.rollback();
      throw error;
    }
  },
};
