'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      // Drop tables
      await queryInterface.sequelize.query(`DROP TABLE IF EXISTS "alerts" CASCADE`, { transaction });
      await queryInterface.sequelize.query(`DROP TABLE IF EXISTS "alert_settings" CASCADE`, { transaction });
      await queryInterface.sequelize.query(`DROP TABLE IF EXISTS "appraisals" CASCADE`, { transaction });
      await queryInterface.sequelize.query(`DROP TABLE IF EXISTS "asks" CASCADE`, { transaction });
      await queryInterface.sequelize.query(`DROP TABLE IF EXISTS "appraisal_proof_pending_requests" CASCADE`, { transaction });
      await queryInterface.sequelize.query(`DROP TABLE IF EXISTS "appraisal_proofs" CASCADE`, { transaction });
      await queryInterface.sequelize.query(`DROP TABLE IF EXISTS "appraisal_proof_requests" CASCADE`, { transaction });
      await queryInterface.sequelize.query(`DROP TABLE IF EXISTS "appraisal_proofs_latest_by_asset" CASCADE`, { transaction });
      await queryInterface.sequelize.query(`DROP TABLE IF EXISTS "appraisals_daily" CASCADE`, { transaction });
      await queryInterface.sequelize.query(`DROP TABLE IF EXISTS "appraisals_hourly" CASCADE`, { transaction });
      await queryInterface.sequelize.query(`DROP TABLE IF EXISTS "asset_bucket_proofs" CASCADE`, { transaction });
      await queryInterface.sequelize.query(`DROP TABLE IF EXISTS "asset_owners" CASCADE`, { transaction });
      await queryInterface.sequelize.query(`DROP TABLE IF EXISTS "asset_owners_h1" CASCADE`, { transaction });
      await queryInterface.sequelize.query(`DROP TABLE IF EXISTS "asset_tags" CASCADE`, { transaction });
      await queryInterface.sequelize.query(`DROP TABLE IF EXISTS "asset_traits" CASCADE`, { transaction });
      await queryInterface.sequelize.query(`DROP TABLE IF EXISTS "assets" CASCADE`, { transaction });
      await queryInterface.sequelize.query(`DROP TABLE IF EXISTS "associated_collections" CASCADE`, { transaction });
      await queryInterface.sequelize.query(`DROP TABLE IF EXISTS "best_asset_asks" CASCADE`, { transaction });
      await queryInterface.sequelize.query(`DROP TABLE IF EXISTS "best_asset_bids" CASCADE`, { transaction });
      await queryInterface.sequelize.query(`DROP TABLE IF EXISTS "best_collection_bids" CASCADE`, { transaction });
      await queryInterface.sequelize.query(`DROP TABLE IF EXISTS "best_collection_asks" CASCADE`, { transaction });
      await queryInterface.sequelize.query(`DROP TABLE IF EXISTS "best_collection_bids_bak" CASCADE`, { transaction });
      await queryInterface.sequelize.query(`DROP TABLE IF EXISTS "bucket_snapshots" CASCADE`, { transaction });
      await queryInterface.sequelize.query(`DROP TABLE IF EXISTS "buckets" CASCADE`, { transaction });
      await queryInterface.sequelize.query(`DROP TABLE IF EXISTS "collections_shortlist" CASCADE`, { transaction });
      await queryInterface.sequelize.query(`DROP TABLE IF EXISTS "collections" CASCADE`, { transaction });
      await queryInterface.sequelize.query(`DROP TABLE IF EXISTS "collection_tags" CASCADE`, { transaction });
      await queryInterface.sequelize.query(`DROP TABLE IF EXISTS "collection_stats" CASCADE`, { transaction });
      await queryInterface.sequelize.query(`DROP TABLE IF EXISTS "collections_mape_30d_100d_all" CASCADE`, { transaction });
      await queryInterface.sequelize.query(`DROP TABLE IF EXISTS "collections_mape_30d_100d_all_load" CASCADE`, { transaction });
      await queryInterface.sequelize.query(`DROP TABLE IF EXISTS "ditto_collection_liquidity_stats" CASCADE`, { transaction });
      await queryInterface.sequelize.query(`DROP TABLE IF EXISTS "ditto_leaderboard" CASCADE`, { transaction });
      await queryInterface.sequelize.query(`DROP TABLE IF EXISTS "ditto_liquidity_provided_logs" CASCADE`, { transaction });
      await queryInterface.sequelize.query(`DROP TABLE IF EXISTS "ditto_order_items" CASCADE`, { transaction });
      await queryInterface.sequelize.query(`DROP TABLE IF EXISTS "ditto_orders" CASCADE`, { transaction });
      await queryInterface.sequelize.query(`DROP TABLE IF EXISTS "ditto_points_transactions" CASCADE`, { transaction });
      await queryInterface.sequelize.query(`DROP TABLE IF EXISTS "ditto_pool_liquidity_stats" CASCADE`, { transaction });
      await queryInterface.sequelize.query(`DROP TABLE IF EXISTS "ditto_raw_add_liquidity" CASCADE`, { transaction });
      await queryInterface.sequelize.query(`DROP TABLE IF EXISTS "ditto_raw_remove_liquidity" CASCADE`, { transaction });
      await queryInterface.sequelize.query(`DROP TABLE IF EXISTS "ditto_raw_swap_erc20_to_nft" CASCADE`, { transaction });
      await queryInterface.sequelize.query(`DROP TABLE IF EXISTS "ditto_raw_swap_nft_to_erc20" CASCADE`, { transaction });
      await queryInterface.sequelize.query(`DROP TABLE IF EXISTS "ditto_referral_relation" CASCADE`, { transaction });
      await queryInterface.sequelize.query(`DROP TABLE IF EXISTS "ditto_seasons" CASCADE`, { transaction });
      await queryInterface.sequelize.query(`DROP TABLE IF EXISTS "ditto_season_points" CASCADE`, { transaction });
      await queryInterface.sequelize.query(`DROP TABLE IF EXISTS "ditto_strategists" CASCADE`, { transaction });
      await queryInterface.sequelize.query(`DROP TABLE IF EXISTS "ditto_user_points" CASCADE`, { transaction });
      await queryInterface.sequelize.query(`DROP TABLE IF EXISTS "ditto_user_tasks" CASCADE`, { transaction });
      await queryInterface.sequelize.query(`DROP TABLE IF EXISTS "ditto_tasks" CASCADE`, { transaction });
      await queryInterface.sequelize.query(`DROP TABLE IF EXISTS "ens_lookups" CASCADE`, { transaction });
      await queryInterface.sequelize.query(`DROP TABLE IF EXISTS "events" CASCADE`, { transaction });
      await queryInterface.sequelize.query(`DROP TABLE IF EXISTS "event_tags" CASCADE`, { transaction });
      await queryInterface.sequelize.query(`DROP TABLE IF EXISTS "friend_tech_profiles" CASCADE`, { transaction });
      await queryInterface.sequelize.query(`DROP TABLE IF EXISTS "friend_tech_profiles_stats" CASCADE`, { transaction });
      await queryInterface.sequelize.query(`DROP TABLE IF EXISTS "friend_tech_portfolios" CASCADE`, { transaction });
      await queryInterface.sequelize.query(`DROP TABLE IF EXISTS "friend_tech_profile_stats" CASCADE`, { transaction });
      await queryInterface.sequelize.query(`DROP TABLE IF EXISTS "market_stats" CASCADE`, { transaction });
      await queryInterface.sequelize.query(`DROP TABLE IF EXISTS "index_constituents" CASCADE`, { transaction });
      await queryInterface.sequelize.query(`DROP TABLE IF EXISTS "index_onchain_locations" CASCADE`, { transaction });
      await queryInterface.sequelize.query(`DROP TABLE IF EXISTS "index_prices" CASCADE`, { transaction });
      await queryInterface.sequelize.query(`DROP TABLE IF EXISTS "index_prices_backfill" CASCADE`, { transaction });
      await queryInterface.sequelize.query(`DROP TABLE IF EXISTS "index_prices_shadow" CASCADE`, { transaction });
      await queryInterface.sequelize.query(`DROP TABLE IF EXISTS "index_window_stats" CASCADE`, { transaction });
      await queryInterface.sequelize.query(`DROP TABLE IF EXISTS "index_window_stats_shadow" CASCADE`, { transaction });
      await queryInterface.sequelize.query(`DROP TABLE IF EXISTS "indexes" CASCADE`, { transaction });
      await queryInterface.sequelize.query(`DROP TABLE IF EXISTS "latest_asset_appraisal_stats" CASCADE`, { transaction });
      await queryInterface.sequelize.query(`DROP TABLE IF EXISTS "latest_asset_buckets" CASCADE`, { transaction });
      await queryInterface.sequelize.query(`DROP TABLE IF EXISTS "latest_collection_ape_stats" CASCADE`, { transaction });
      await queryInterface.sequelize.query(`DROP TABLE IF EXISTS "latest_asset_appraisal_ask_stats" CASCADE`, { transaction });
      await queryInterface.sequelize.query(`DROP TABLE IF EXISTS "latest_asset_appraisal_sale_stats" CASCADE`, { transaction });
      await queryInterface.sequelize.query(`DROP TABLE IF EXISTS "latest_asset_appraisal_stats" CASCADE`, { transaction });
      await queryInterface.sequelize.query(`DROP TABLE IF EXISTS "latest_asset_ask_stats" CASCADE`, { transaction });
      await queryInterface.sequelize.query(`DROP TABLE IF EXISTS "latest_asset_buckets" CASCADE`, { transaction });
      await queryInterface.sequelize.query(`DROP TABLE IF EXISTS "latest_asset_mint_sale_transfer_stats" CASCADE`, { transaction });
      await queryInterface.sequelize.query(`DROP TABLE IF EXISTS "latest_asset_sale_stats" CASCADE`, { transaction });
      await queryInterface.sequelize.query(`DROP TABLE IF EXISTS "latest_collection_ape_stats" CASCADE`, { transaction });
      await queryInterface.sequelize.query(`DROP TABLE IF EXISTS "latest_collection_appraisal_stats" CASCADE`, { transaction });
      await queryInterface.sequelize.query(`DROP TABLE IF EXISTS "latest_collection_ask_stats" CASCADE`, { transaction });
      await queryInterface.sequelize.query(`DROP TABLE IF EXISTS "latest_collection_gmi_sale_stats" CASCADE`, { transaction });
      await queryInterface.sequelize.query(`DROP TABLE IF EXISTS "latest_collection_mint_stats" CASCADE`, { transaction });
      await queryInterface.sequelize.query(`DROP TABLE IF EXISTS "latest_collection_sale_stats" CASCADE`, { transaction });
      await queryInterface.sequelize.query(`DROP TABLE IF EXISTS "latest_pool_prices" CASCADE`, { transaction });
      await queryInterface.sequelize.query(`DROP TABLE IF EXISTS "latest_trait_appraisal_ask_stats" CASCADE`, { transaction });
      await queryInterface.sequelize.query(`DROP TABLE IF EXISTS "latest_trait_appraisal_stats" CASCADE`, { transaction });
      await queryInterface.sequelize.query(`DROP TABLE IF EXISTS "latest_trait_ask_stats" CASCADE`, { transaction });
      await queryInterface.sequelize.query(`DROP TABLE IF EXISTS "latest_trait_sale_stats" CASCADE`, { transaction });
      await queryInterface.sequelize.query(`DROP TABLE IF EXISTS "liquidity_position_assets" CASCADE`, { transaction });
      await queryInterface.sequelize.query(`DROP TABLE IF EXISTS "liquidity_positions" CASCADE`, { transaction });
      await queryInterface.sequelize.query(`DROP TABLE IF EXISTS "new_api_tier_alert_limits" CASCADE`, { transaction });
      await queryInterface.sequelize.query(`DROP TABLE IF EXISTS "permitters" CASCADE`, { transaction });
      await queryInterface.sequelize.query(`DROP TABLE IF EXISTS "pools" CASCADE`, { transaction });
      await queryInterface.sequelize.query(`DROP TABLE IF EXISTS "portfolio_values_daily" CASCADE`, { transaction });
      await queryInterface.sequelize.query(`DROP TABLE IF EXISTS "recommended_collections" CASCADE`, { transaction });
      await queryInterface.sequelize.query(`DROP TABLE IF EXISTS "sequelize_appraisal_proof_command_repository_fixtures" CASCADE`, { transaction });
      await queryInterface.sequelize.query(`DROP TABLE IF EXISTS "sequelize_appraisal_proof_query_repository_fixtures" CASCADE`, { transaction });
      await queryInterface.sequelize.query(`DROP TABLE IF EXISTS "sequelize_asset_repository_fixtures" CASCADE`, { transaction });
      await queryInterface.sequelize.query(`DROP TABLE IF EXISTS "sequelize_redis_trait_repository_fixtures" CASCADE`, { transaction });
      await queryInterface.sequelize.query(`DROP TABLE IF EXISTS "shadow_top_holders_all" CASCADE`, { transaction });
      await queryInterface.sequelize.query(`DROP TABLE IF EXISTS "shadow_top_holders_all_load" CASCADE`, { transaction });
      await queryInterface.sequelize.query(`DROP TABLE IF EXISTS "shadow_traits" CASCADE`, { transaction });
      await queryInterface.sequelize.query(`DROP TABLE IF EXISTS "similar_assets" CASCADE`, { transaction });
      await queryInterface.sequelize.query(`DROP TABLE IF EXISTS "simple_hash_collections" CASCADE`, { transaction });
      await queryInterface.sequelize.query(`DROP TABLE IF EXISTS "subscriptions" CASCADE`, { transaction });
      await queryInterface.sequelize.query(`DROP TABLE IF EXISTS "tags" CASCADE`, { transaction });
      await queryInterface.sequelize.query(`DROP TABLE IF EXISTS "tags_stars" CASCADE`, { transaction });
      await queryInterface.sequelize.query(`DROP TABLE IF EXISTS "tags_acl" CASCADE`, { transaction });
      await queryInterface.sequelize.query(`DROP TABLE IF EXISTS "temp_duplicate_asks" CASCADE`, { transaction });
      await queryInterface.sequelize.query(`DROP TABLE IF EXISTS "temp_duplicate_asks_min_10" CASCADE`, { transaction });
      await queryInterface.sequelize.query(`DROP TABLE IF EXISTS "top_assets" CASCADE`, { transaction });
      await queryInterface.sequelize.query(`DROP TABLE IF EXISTS "top_collections" CASCADE`, { transaction });
      await queryInterface.sequelize.query(`DROP TABLE IF EXISTS "top_holders_all" CASCADE`, { transaction });
      await queryInterface.sequelize.query(`DROP TABLE IF EXISTS "top_holders" CASCADE`, { transaction });
      await queryInterface.sequelize.query(`DROP TABLE IF EXISTS "top_traits" CASCADE`, { transaction });
      await queryInterface.sequelize.query(`DROP TABLE IF EXISTS "tokens_oracle_block_data" CASCADE`, { transaction });
      await queryInterface.sequelize.query(`DROP TABLE IF EXISTS "tokens_oracle_supported_tokens" CASCADE`, { transaction });
      await queryInterface.sequelize.query(`DROP TABLE IF EXISTS "traits" CASCADE`, { transaction });
      await queryInterface.sequelize.query(`DROP TABLE IF EXISTS "tag_stars" CASCADE`, { transaction });
      await queryInterface.sequelize.query(`DROP TABLE IF EXISTS "user_settings" CASCADE`, { transaction });
      await queryInterface.sequelize.query(`DROP TABLE IF EXISTS "user_email_counts_daily" CASCADE`, { transaction });
      await queryInterface.sequelize.query(`DROP TABLE IF EXISTS "user_alert_counts_daily" CASCADE`, { transaction });
      await queryInterface.sequelize.query(`DROP TABLE IF EXISTS "wallet_tags" CASCADE`, { transaction });
      await queryInterface.sequelize.query(`DROP TABLE IF EXISTS "wallet_balances" CASCADE`, { transaction });
      await queryInterface.sequelize.query(`DROP TABLE IF EXISTS "wallets" CASCADE`, { transaction });

      // Drop enums
      await queryInterface.sequelize.query(`DROP TYPE IF EXISTS "enum_ditto_order_items_type"`, { transaction });
      await queryInterface.sequelize.query(`DROP TYPE IF EXISTS "enum_event_tags_secondary_entity_type"`, { transaction });
      await queryInterface.sequelize.query(`DROP TYPE IF EXISTS "enum_events_type"`, { transaction });
      await queryInterface.sequelize.query(`DROP TYPE IF EXISTS "enum_tags_entity_type"`, { transaction });
      await queryInterface.sequelize.query(`DROP TYPE IF EXISTS "enum_traits_display_type"`, { transaction });

      // Vacuum cannot run inside of a transaction => commit current transaction and run vacuum after
      await transaction.commit();

      // Actually, ran this in staging, and will manually run in prod later, after auto-vacuum removes some clutter first
      // // Run Vacuum Full -- Be sure to run this migration at an intentional time!
      // await queryInterface.sequelize.query('VACUUM FULL ANALYZE');
    } catch (e) {
      await transaction.rollback();
      throw e;
    }
  },

  async down(queryInterface, Sequelize) {
    console.log('THERE IS NO DOWN for migration: 20250121020632-allora-cleaning.js');
  },
};
