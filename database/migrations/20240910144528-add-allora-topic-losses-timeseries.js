'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      //  WORKER LOSSES
      await queryInterface.createTable(
        'allora_topic_worker_losses',
        {
          id: {
            type: Sequelize.BIGINT,
            primaryKey: true,
            autoIncrement: true,
          },
          topic_id: {
            type: Sequelize.INTEGER,
            allowNull: false,
          },
          address: {
            type: Sequelize.STRING,
            allowNull: false,
          },
          timestamp: {
            type: Sequelize.INTEGER,
            allowNull: false,
          },
          value: {
            type: Sequelize.TEXT,
            allowNull: false,
          },
          createdAt: {
            type: Sequelize.DATE,
            allowNull: false,
            defaultValue: Sequelize.fn('NOW'),
          },
        },
        { transaction },
      );

      await queryInterface.addIndex(
        'allora_topic_worker_losses',
        ['topic_id', 'address', 'timestamp'],
        {
          name: 'idx_topic_worker_losses',
          transaction,
        },
      );

      //  REPUTER LOSSES
      await queryInterface.createTable(
        'allora_topic_reputer_losses',
        {
          id: {
            type: Sequelize.BIGINT,
            primaryKey: true,
            autoIncrement: true,
          },
          topic_id: {
            type: Sequelize.INTEGER,
            allowNull: false,
          },
          address: {
            type: Sequelize.STRING,
            allowNull: false,
          },
          timestamp: {
            type: Sequelize.INTEGER,
            allowNull: false,
          },
          value: {
            type: Sequelize.TEXT,
            allowNull: false,
          },
          createdAt: {
            type: Sequelize.DATE,
            allowNull: false,
            defaultValue: Sequelize.fn('NOW'),
          },
        },
        { transaction },
      );

      await queryInterface.addIndex(
        'allora_topic_reputer_losses',
        ['topic_id', 'address', 'timestamp'],
        {
          name: 'idx_topic_reputer_losses',
          transaction,
        },
      );

      await transaction.commit();
    } catch (e) {
      await transaction.rollback();
      throw e;
    }
  },

  down: async (queryInterface, Sequelize) => {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      // WORKER LOSSES
      await queryInterface.removeIndex(
        'allora_topic_worker_losses',
        'idx_topic_worker_losses',
        { transaction },
      );

      await queryInterface.dropTable('allora_topic_worker_losses', {
        transaction,
      });

      // REPUTER LOSSES
      await queryInterface.removeIndex(
        'allora_topic_worker_losses',
        'idx_topic_reputer_losses',
        { transaction },
      );

      await queryInterface.dropTable('allora_topic_reputer_losses', {
        transaction,
      });

      await transaction.commit();
    } catch (e) {
      await transaction.rollback();
      throw e;
    }
  },
};
