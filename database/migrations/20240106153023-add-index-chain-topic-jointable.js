'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      /**
       * This is important for dealing with signing and verifying messages on different chains.
       * Chains are identified by their slug (e.g. arbitrum), which is unique within a network (e.g. Ethereum).
       *
       * Ethereum: Hexadecimal format (0x prefixed), derived from the secp256k1 ECDSA public key.
       * Solana: Base58 encoded, derived from the Ed25519 public key.
       * Avalanche: Bech32 encoded for addresses, underlying it uses ECDSA (secp256k1) and Ed25519 public keys.
       * Cosmos: Bech32 encoded for addresses, underlying it uses ECDSA (secp256k1) and Ed25519 public keys.
       */
      await queryInterface.createTable(
        'chains',
        {
          slug: { // e.g. ethereum, arbitrum, sepolia, cosmoshub-4
            type: Sequelize.TEXT,
            primaryKey: true,
            allowNull: false,
          },
          id_within_network: { // e.g. 11155111 for Sepolia on Ethereum
            type: Sequelize.TEXT,
            allowNull: false,
          },
          network_name: { // e.g. ethereum, solana, avalanche, cosmos
            type: Sequelize.TEXT,
            allowNull: false,
          },
        },
        { transaction },
      );

      await queryInterface.createTable(
        'index_onchain_locations',
        {
          chain_slug: {
            type: Sequelize.TEXT,
            allowNull: false,
          },
          index_id: {
            type: Sequelize.TEXT,
            allowNull: false,
          },
          topic_id: {
            type: Sequelize.TEXT,
            allowNull: false,
          },
          actively_pushed: {
            type: Sequelize.BOOLEAN,
            allowNull: false,
            defaultValue: true,
          },
        },
        { transaction },
      );

      await queryInterface.createTable(
        'adapter_locations',
        {
          chain_slug: {
            type: Sequelize.TEXT,
            allowNull: false,
          },
          adapter_address: {
            type: Sequelize.TEXT,
            allowNull: false,
          },
          signer_address: {
            type: Sequelize.TEXT,
            allowNull: false,
          },
        },
        { transaction },
      );

      console.log('It is up to the dev who merges this code to ensure that the tables added above in this migration are properly populated with the most up-to-date data. This is not done automatically.')

      await transaction.commit();
    } catch (error) {
      await transaction.rollback();
      // Log all errors, but don't throw to prevent migration failure
      console.error(error);
    }
  },

  async down(queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      await queryInterface.dropTable('chains', { transaction });
      await queryInterface.dropTable('index_onchain_locations', { transaction });
      await queryInterface.dropTable('adapter_locations', { transaction });

      await transaction.commit();
    } catch (error) {
      await transaction.rollback();
      console.error(error);
    }
  },
};
