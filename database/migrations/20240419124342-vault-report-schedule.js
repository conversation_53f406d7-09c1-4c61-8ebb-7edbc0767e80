'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    'use strict';
    await queryInterface.createTable('vault_report_schedule', {
      vault_address: {
        type: Sequelize.TEXT,
        primaryKey: true,
        allowNull: false,
      },
      is_active: {
        type: Sequelize.BOOLEAN,
        allowNull: false,
        defaultValue: true,
      },
    });
  },
  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('vault_report_schedule');
  },
};
