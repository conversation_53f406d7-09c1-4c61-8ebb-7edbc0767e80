'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('vault_reports', {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      vault_address: {
        type: Sequelize.TEXT,
        allowNull: false,
      },
      profit: {
        type: Sequelize.TEXT,
        allowNull: false,
      },
      loss: {
        type: Sequelize.TEXT,
        allowNull: false,
      },
      protocol_fees: {
        type: Sequelize.TEXT,
        allowNull: false,
      },
      performance_fees: {
        type: Sequelize.TEXT,
        allowNull: false,
      },
      total_debt: {
        type: Sequelize.TEXT,
        allowNull: true,
      },
      total_idle: {
        type: Sequelize.TEXT,
        allowNull: true,
      },
      total_supply: {
        type: Sequelize.TEXT,
        allowNull: true,
      },
      price_per_share: {
        type: Sequelize.TEXT,
        allowNull: true,
      },
      tx_hash: {
        type: Sequelize.TEXT,
        allowNull: false,
      },
      block_timestamp: {
        type: Sequelize.INTEGER,
        allowNull: false,
      },
      created_at: {
        type: Sequelize.DATE,
        defaultValue: Sequelize.fn('now'),
      },
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('vault_reports');
  },
};
