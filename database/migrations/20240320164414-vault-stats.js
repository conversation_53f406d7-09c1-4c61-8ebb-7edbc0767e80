'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      await queryInterface.createTable(
        'vault_stats',
        {
          vault_address: {
            type: Sequelize.TEXT,
            allowNull: false,
            primaryKey: true,
          },
          daily_apr: {
            type: Sequelize.NUMERIC,
            defaultValue: 0,
            allowNull: false,
          },
          monthly_apr: {
            type: Sequelize.NUMERIC,
            defaultValue: 0,
            allowNull: false,
          },
          inception_apr: {
            type: Sequelize.NUMERIC,
            defaultValue: 0,
            allowNull: false,
          },
          total_deposits_wei: {
            type: Sequelize.TEXT,
            allowNull: false,
            defaultValue: '0',
          },
          updated_at: {
            type: Sequelize.DATE,
            defaultValue: Sequelize.fn('now'),
          },
          created_at: {
            type: Sequelize.DATE,
            defaultValue: Sequelize.fn('now'),
          },
        },
        { transaction },
      );
      await transaction.commit();
    } catch (e) {
      await transaction.rollback();
      throw e;
    }
  },

  down: async (queryInterface, Sequelize) => {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      await queryInterface.dropTable('vault_stats');

      await transaction.commit();
    } catch (e) {
      await transaction.rollback();
      throw e;
    }
  },
};
