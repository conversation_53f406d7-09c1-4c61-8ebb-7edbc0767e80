'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      // WORKER STATS
      await queryInterface.createTable(
        'allora_topic_worker_stats',
        {
          address: {
            type: Sequelize.STRING,
            primaryKey: true,
          },
          topic_id: {
            type: Sequelize.INTEGER,
            primaryKey: true,
          },
          weight: {
            type: Sequelize.NUMERIC,
            allowNull: false,
          },
          loss: {
            type: Sequelize.NUMERIC,
            allowNull: false,
          },
          total_earned: {
            type: Sequelize.TEXT,
            allowNull: false,
          },
          createdAt: {
            type: Sequelize.DATE,
            allowNull: false,
            defaultValue: Sequelize.fn('NOW'),
          },
          updatedAt: {
            type: Sequelize.DATE,
            allowNull: false,
            defaultValue: Sequelize.fn('NOW'),
          },
        },
        { transaction },
      );

      // REPUTER STATS
      await queryInterface.createTable(
        'allora_topic_reputer_stats',
        {
          address: {
            type: Sequelize.STRING,
            primaryKey: true,
          },
          topic_id: {
            type: Sequelize.INTEGER,
            primaryKey: true,
          },
          weight: {
            type: Sequelize.NUMERIC,
            allowNull: false,
          },
          loss: {
            type: Sequelize.NUMERIC,
            allowNull: false,
          },
          total_earned: {
            type: Sequelize.NUMERIC,
            allowNull: false,
          },
          createdAt: {
            type: Sequelize.DATE,
            allowNull: false,
            defaultValue: Sequelize.fn('NOW'),
          },
          updatedAt: {
            type: Sequelize.DATE,
            allowNull: false,
            defaultValue: Sequelize.fn('NOW'),
          },
        },
        { transaction },
      );

      await transaction.commit();
    } catch (e) {
      await transaction.rollback();
      throw e;
    }
  },

  down: async (queryInterface, Sequelize) => {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      // WORKER STATS
      await queryInterface.dropTable('allora_topic_worker_stats', {
        transaction,
      });

      // REPUTER STATS
      await queryInterface.dropTable('allora_topic_reputer_stats', {
        transaction,
      });
      await transaction.commit();
    } catch (e) {
      await transaction.rollback();
      throw e;
    }
  },
};
