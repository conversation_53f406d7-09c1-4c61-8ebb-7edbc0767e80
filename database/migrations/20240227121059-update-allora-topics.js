'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      await queryInterface.sequelize.query(
        `
        DELETE FROM allora_topics;
        `,
        { transaction },
      );

      await queryInterface.sequelize.query(
        `
        INSERT INTO allora_topics (id, name, b7s_function_id, b7s_method, b7s_env_vars) VALUES
        (1, 'Eth Price Prediction', 'bafybeigpiwl3o73zvvl6dxdqu7zqcub5mhg65jiky2xqb4rdhfmikswzqm', 'allora-inference-function.wasm', '[{"name": "BLS_REQUEST_PATH", "value": "/api"}, {"name": "ALLORA_ARG_PARAMS", "value": "ETH"}]'),
        (2, 'Yuga Index Level', 'bafybeigpiwl3o73zvvl6dxdqu7zqcub5mhg65jiky2xqb4rdhfmikswzqm', 'allora-inference-function.wasm', '[{"name": "BLS_REQUEST_PATH", "value": "/api"}, {"name": "ALLORA_ARG_PARAMS", "value": "yuga"}]'),
        (3, 'NFT Appraisals', 'bafybeihvikwjuqtijpurgsyiv5uwmmzg7ksibcwx6s3gjmkneasdn5kndy', 'nft-appraisals-inference.wasm', '[{"name": "BLS_REQUEST_PATH", "value": "/api"}, {"name": "ALLORA_ARG_PARAMS", "value": "******************************************/2921"}]'),
        (4, 'Watches', 'bafybeifmz4hyk63eynmwmx3htfrshb3egpgl77xmqa2pjuxgimmzssa5ai', 'watch-prices-inference.wasm', '[{"name": "BLS_REQUEST_PATH", "value": "/api"}, {"name": "ALLORA_ARG_PARAMS", "value": "******************************************/51"}]');
        `,
        { transaction },
      );
      await transaction.commit();
    } catch (error) {
      await transaction.rollback();
      // Log all errors, but don't throw to prevent migration failure
      console.error(error);
    }
  },

  async down(queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      await queryInterface.sequelize.query(
        `
        DELETE FROM allora_topics;
        `,
        { transaction },
      );
      await transaction.commit();
    } catch (error) {
      await transaction.rollback();
      // Log all errors, but don't throw to prevent migration failure
      console.error(error);
    }
  },
};
