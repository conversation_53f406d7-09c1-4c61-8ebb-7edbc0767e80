'use strict';
module.exports = {
  async up(queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      await queryInterface.addColumn(
        'allora_topics_metadata_allora_testnet_1',
        'is_active',
        {
          type: Sequelize.BOOLEAN,
          allowNull: true,
        },
        { transaction },
      );

      await queryInterface.addColumn(
        'allora_topics_metadata_allora_devnet_1',
        'is_active',
        {
          type: Sequelize.BOOLEAN,
          allowNull: true,
        },
        { transaction },
      );

      await queryInterface.addColumn(
        'allora_topics_metadata_allora_mainnet_1',
        'is_active',
        {
          type: Sequelize.BOOLEAN,
          allowNull: true,
        },
        { transaction },
      );

      await transaction.commit();
    } catch (error) {
      console.error(error);
      await transaction.rollback();
      throw error;
    }
  },

  async down(queryInterface) {
    const transaction = await queryInterface.sequelize.transaction();

    await queryInterface.removeColumn(
      'allora_topics_metadata_allora_testnet_1',
      'is_active',
      { transaction },
    );

    await queryInterface.removeColumn(
      'allora_topics_metadata_allora_devnet_1',
      'is_active',
      { transaction },
    );

    await queryInterface.removeColumn(
      'allora_topics_metadata_allora_mainnet_1',
      'is_active',
      { transaction },
    );

    await transaction.commit();
  },
};
